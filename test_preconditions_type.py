#!/usr/bin/env python3
"""
测试preconditions字段类型改变
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from ft_corpus_evaluator.parsers.corpus_parser import CorpusParser
from ft_corpus_evaluator.parsers.improved_markdown_parser import ImprovedMarkdownCorpusParser


def test_preconditions_type():
    """测试preconditions字段类型"""
    
    test_file = Path("corpus/RAN-5869391.md")
    if not test_file.exists():
        print(f"测试文件 {test_file} 不存在")
        return
    
    print("🔍 测试preconditions字段类型改变")
    print("=" * 60)
    
    # 使用原解析器
    print("\n📋 原解析器:")
    old_parser = CorpusParser()
    old_result = old_parser.parse_file(test_file)
    
    print(f"  类型: {type(old_result.test_info.preconditions)}")
    print(f"  值: {repr(old_result.test_info.preconditions[:100])}...")
    print(f"  长度: {len(old_result.test_info.preconditions)} 字符")
    
    # 使用改进的解析器
    print("\n🚀 ImprovedMarkdownCorpusParser:")
    improved_parser = ImprovedMarkdownCorpusParser()
    improved_result = improved_parser.parse_file(test_file)
    
    print(f"  类型: {type(improved_result.test_info.preconditions)}")
    print(f"  值: {repr(improved_result.test_info.preconditions[:100])}...")
    print(f"  长度: {len(improved_result.test_info.preconditions)} 字符")
    
    # 验证类型
    print("\n✅ 类型验证:")
    old_is_str = isinstance(old_result.test_info.preconditions, str)
    new_is_str = isinstance(improved_result.test_info.preconditions, str)
    
    print(f"  原解析器preconditions是str: {old_is_str}")
    print(f"  新解析器preconditions是str: {new_is_str}")
    
    if old_is_str and new_is_str:
        print("  🎉 两个解析器都正确返回str类型！")
        
        # 比较内容长度
        if len(old_result.test_info.preconditions) == len(improved_result.test_info.preconditions):
            print("  🎉 内容长度完全匹配！")
        else:
            print(f"  ⚠️  内容长度略有差异: 原{len(old_result.test_info.preconditions)} vs 新{len(improved_result.test_info.preconditions)}")
    else:
        print("  ❌ 类型不匹配")
    
    # 测试其他字段仍然是列表
    print("\n📝 其他列表字段验证:")
    list_fields = ['tc_steps', 'tc_expected_results']
    
    for field in list_fields:
        old_val = getattr(old_result.test_info, field)
        new_val = getattr(improved_result.test_info, field)
        
        old_is_list = isinstance(old_val, list)
        new_is_list = isinstance(new_val, list)
        
        status = "✅" if old_is_list and new_is_list else "❌"
        print(f"  {status} {field}: 原{type(old_val).__name__} vs 新{type(new_val).__name__}")


if __name__ == "__main__":
    test_preconditions_type()
    print("\n✅ 测试完成!")
