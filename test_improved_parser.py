#!/usr/bin/env python3
"""
测试改进的markdown解析器
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from ft_corpus_evaluator.parsers.corpus_parser import CorpusParser
from ft_corpus_evaluator.parsers.improved_markdown_parser import ImprovedMarkdownCorpusParser


def test_improved_parser():
    """测试改进的解析器"""
    
    test_file = Path("corpus/RAN-5869391.md")
    if not test_file.exists():
        print(f"测试文件 {test_file} 不存在")
        return
    
    print(f"测试文件: {test_file}")
    print("=" * 60)
    
    # 使用原解析器
    print("\n1. 原解析器结果:")
    print("-" * 30)
    try:
        old_parser = CorpusParser()
        old_result = old_parser.parse_file(test_file)
        print_corpus_info(old_result, "原解析器")
    except Exception as e:
        print(f"原解析器出错: {e}")
    
    # 使用改进的解析器
    print("\n2. 改进解析器结果:")
    print("-" * 30)
    try:
        improved_parser = ImprovedMarkdownCorpusParser()
        improved_result = improved_parser.parse_file(test_file)
        print_corpus_info(improved_result, "改进解析器")
    except Exception as e:
        print(f"改进解析器出错: {e}")
        import traceback
        traceback.print_exc()
    
    # 比较结果
    print("\n3. 详细比较:")
    print("-" * 30)
    try:
        compare_detailed(old_result, improved_result)
    except Exception as e:
        print(f"比较时出错: {e}")


def print_corpus_info(corpus, parser_name):
    """打印语料库信息"""
    print(f"\n{parser_name} - RdcInfo:")
    print(f"  rdc_id: {corpus.rdc_info.rdc_id}")
    print(f"  repo_name: {corpus.rdc_info.repo_name}")
    print(f"  gerrit_link: {corpus.rdc_info.gerrit_link}")
    print(f"  date: {corpus.rdc_info.date}")
    
    print(f"\n{parser_name} - TestInfo:")
    print(f"  test_title: {len(corpus.test_info.test_title)} chars")
    print(f"  preconditions length: {len(corpus.test_info.preconditions)} chars")
    print(f"  tc_steps count: {len(corpus.test_info.tc_steps)}")
    print(f"  tc_expected_results count: {len(corpus.test_info.tc_expected_results)}")
    
    print(f"\n{parser_name} - TagIdentification:")
    print(f"  business_tags count: {len(corpus.tag_identification.business_content_scene_tags)}")
    print(f"  code_tags count: {len(corpus.tag_identification.code_modify_scene_tags)}")
    
    print(f"\n{parser_name} - CodeSnippets:")
    print(f"  code_snippets count: {len(corpus.code_snippets)}")
    
    print(f"\n{parser_name} - DependencyCode:")
    print(f"  dependency_code count: {len(corpus.dependency_code)}")
    
    print(f"\n{parser_name} - SimilarCodeInfo:")
    if corpus.similar_code_info:
        print(f"  similar_test_case: {'存在' if corpus.similar_code_info.similar_test_case else '不存在'}")
        print(f"  similar_ft_code count: {len(corpus.similar_code_info.similar_ft_code)}")
        print(f"  similar_dependency_code count: {len(corpus.similar_code_info.similar_dependency_code)}")
    else:
        print("  similar_code_info: 不存在")


def compare_detailed(old_result, new_result):
    """详细比较结果"""
    
    # 比较RdcInfo
    print("RdcInfo比较:")
    rdc_fields = ['rdc_id', 'repo_name', 'gerrit_link', 'date']
    for field in rdc_fields:
        old_val = getattr(old_result.rdc_info, field, "")
        new_val = getattr(new_result.rdc_info, field, "")
        status = "✓" if old_val == new_val else "✗"
        print(f"  {field}: {status}")
        if old_val != new_val:
            print(f"    原: '{old_val}'")
            print(f"    新: '{new_val}'")
    
    # 比较TestInfo
    print("\nTestInfo比较:")
    test_fields = ['test_title', 'preconditions', 'tc_steps', 'tc_expected_results']
    for field in test_fields:
        old_val = getattr(old_result.test_info, field, "")
        new_val = getattr(new_result.test_info, field, "")
        
        if isinstance(old_val, list) and isinstance(new_val, list):
            status = "✓" if len(old_val) == len(new_val) else "✗"
            print(f"  {field}: {status} (原:{len(old_val)} 新:{len(new_val)})")
        else:
            old_len = len(str(old_val)) if old_val else 0
            new_len = len(str(new_val)) if new_val else 0
            status = "✓" if old_len == new_len else "✗"
            print(f"  {field}: {status} (原:{old_len} 新:{new_len})")
    
    # 比较其他字段
    print("\n其他字段比较:")
    
    # TagIdentification
    old_business = len(old_result.tag_identification.business_content_scene_tags)
    new_business = len(new_result.tag_identification.business_content_scene_tags)
    old_code = len(old_result.tag_identification.code_modify_scene_tags)
    new_code = len(new_result.tag_identification.code_modify_scene_tags)
    
    print(f"  business_tags: {'✓' if old_business == new_business else '✗'} (原:{old_business} 新:{new_business})")
    print(f"  code_tags: {'✓' if old_code == new_code else '✗'} (原:{old_code} 新:{new_code})")
    
    # CodeSnippets
    old_snippets = len(old_result.code_snippets)
    new_snippets = len(new_result.code_snippets)
    print(f"  code_snippets: {'✓' if old_snippets == new_snippets else '✗'} (原:{old_snippets} 新:{new_snippets})")
    
    # DependencyCode
    old_dep = len(old_result.dependency_code)
    new_dep = len(new_result.dependency_code)
    print(f"  dependency_code: {'✓' if old_dep == new_dep else '✗'} (原:{old_dep} 新:{new_dep})")
    
    # SimilarCodeInfo
    old_similar = old_result.similar_code_info is not None
    new_similar = new_result.similar_code_info is not None
    print(f"  similar_code_info存在: {'✓' if old_similar == new_similar else '✗'} (原:{old_similar} 新:{new_similar})")
    
    if old_similar and new_similar:
        old_ft = len(old_result.similar_code_info.similar_ft_code)
        new_ft = len(new_result.similar_code_info.similar_ft_code)
        print(f"  similar_ft_code: {'✓' if old_ft == new_ft else '✗'} (原:{old_ft} 新:{new_ft})")


if __name__ == "__main__":
    print("开始测试改进的markdown解析器...")
    test_improved_parser()
    print("\n测试完成!")
