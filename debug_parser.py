#!/usr/bin/env python3
"""
调试markdown解析器
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from ft_corpus_evaluator.parsers.markdown_corpus_parser import CorpusMarkdownRenderer
import mistune


def debug_markdown_parsing():
    """调试markdown解析过程"""
    
    # 读取实际文件的前100行
    test_file = Path("corpus/RAN-5869391.md")
    if not test_file.exists():
        print(f"测试文件 {test_file} 不存在")
        return
    
    content = test_file.read_text(encoding='utf-8')
    lines = content.split('\n')
    
    # 只取前50行进行调试
    debug_content = '\n'.join(lines[:50])
    
    print("调试内容:")
    print("=" * 60)
    print(debug_content)
    print("=" * 60)
    
    # 创建渲染器并解析
    renderer = CorpusMarkdownRenderer()
    markdown = mistune.create_markdown(renderer=renderer)
    
    # 解析
    result = markdown(debug_content)
    
    print("\n解析结果:")
    print("-" * 30)
    
    print("RdcInfo内容:")
    for i, content in enumerate(renderer.rdc_info_content):
        print(f"  [{i}] {content}")
    
    print(f"\nSections ({len(renderer.sections)}):")
    for name, section in renderer.sections.items():
        print(f"  {name}:")
        print(f"    content: {len(section.content)} items")
        for i, content in enumerate(section.content):
            print(f"      [{i}] {content[:100]}...")
        print(f"    subsections: {len(section.subsections)} items")
        for sub_name, sub_section in section.subsections.items():
            print(f"      {sub_name}:")
            print(f"        content: {len(sub_section.content)} items")
            for i, content in enumerate(sub_section.content):
                print(f"          [{i}] {content[:100]}...")
            print(f"        lists: {len(sub_section.lists)} items")
            for i, lst in enumerate(sub_section.lists):
                print(f"          list[{i}]: {lst}")
        print(f"    code_blocks: {len(section.code_blocks)} items")
        print(f"    lists: {len(section.lists)} items")
        for i, lst in enumerate(section.lists):
            print(f"      list[{i}]: {lst}")


if __name__ == "__main__":
    debug_markdown_parsing()
