from pathlib import Path
import sys
import json

sys.path.append(str(Path(__file__).parent.parent))

from ft_corpus_evaluator.core.evaluator_manager import EvaluatorManager
from ft_corpus_evaluator.core.batch_processor import BatchProcessor
from ft_corpus_evaluator.evaluators.dimension_evaluator import DimensionEvaluator
from ft_corpus_evaluator.models.evaluation_models import EvaluationDimension
from ft_corpus_evaluator.parsers.corpus_parser import CorpusParser
from ft_corpus_evaluator.parsers.improved_markdown_parser import ImprovedMarkdownCorpusParser


def example_single_file_evaluation():
    print("=== 单文件维度评估示例 ===")

    evaluator_manager = EvaluatorManager()
    
    corpus_file = Path("corpus/测试用例 RAN-6612580_ 低速迁出测量场景，存在gumai惩罚的邻区，异系统测量上报无输出.md")
    if not corpus_file.exists():
        print(f"示例文件 {corpus_file} 不存在，跳过单文件评估")
        return
    
    # parser = CorpusParser()
    # corpus = parser.parse_file(corpus_file)
    parser = ImprovedMarkdownCorpusParser()

    corpus = parser.parse_file(corpus_file)

    
    report = evaluator_manager.evaluate_corpus(corpus)
    
    print(f"文件: {report.file_path}")
    print(f"综合评分: {report.overall_score:.2f}")
    print(f"评估结果数量: {len(report.evaluation_results)}")
    
    for result in report.evaluation_results:
        if result.dimension:
            print(f"\n维度: {result.dimension.value}")
            print(f"  评分: {result.score:.2f}")
            print(f"  级别: {result.level.value}")
            print(f"  消息: {result.message}")
            if result.suggestions:
                print(f"  建议: {result.suggestions[:2]}")


def example_dimension_specific_evaluation():
    print("\n=== 特定维度评估示例 ===")
    
    evaluator_manager = EvaluatorManager()
    
    corpus_file = Path("corpus/RAN-5869391.md")
    if not corpus_file.exists():
        print(f"示例文件 {corpus_file} 不存在，跳过维度评估")
        return
    
    parser = CorpusParser()
    corpus = parser.parse_file(corpus_file)
    
    for dimension in EvaluationDimension:
        print(f"\n--- {dimension.value} 维度评估 ---")
        dimension_result = evaluator_manager.evaluate_by_dimension(corpus, dimension)
        
        if dimension_result:
            print(f"维度评分: {dimension_result.overall_score:.2f}")
            print(f"加权评分: {dimension_result.weighted_score:.2f}")
            print(f"启用指标: {dimension_result.enabled_metrics}")
            
            for metric_result in dimension_result.metric_results:
                print(f"  指标: {metric_result.metric_name}")
                print(f"    评分: {metric_result.score:.2f}")
                print(f"    级别: {metric_result.level.value}")
                print(f"    消息: {metric_result.message}")


def example_batch_processing():
    print("\n=== 批量处理示例 ===")
    
    evaluator_manager = EvaluatorManager()
    batch_processor = BatchProcessor(evaluator_manager)
    
    corpus_dir = Path("corpus")
    output_dir = Path("evaluation_results/three_dimension")
    
    if not corpus_dir.exists():
        print(f"语料库目录 {corpus_dir} 不存在，跳过批量处理")
        return
    
    result = batch_processor.process_directory(
        corpus_dir=corpus_dir,
        file_pattern="*.md",
        output_dir=output_dir
    )
    
    if "error" in result:
        print(f"批量处理错误: {result['error']}")
        return
    
    summary = result["summary"]
    print(f"处理文件数: {summary['total_files']}")
    print(f"平均评分: {summary['average_score']}")
    print(f"评分分布:")
    for level, count in summary["score_distribution"].items():
        print(f"  {level}: {count}")
    
    print(f"输出目录: {output_dir}")


def example_dimension_batch_processing():
    print("\n=== 维度批量处理示例 ===")
    
    evaluator_manager = EvaluatorManager()
    batch_processor = BatchProcessor(evaluator_manager)
    
    corpus_dir = Path("corpus")
    output_dir = Path("evaluation_results/dimension_specific")
    
    if not corpus_dir.exists():
        print(f"语料库目录 {corpus_dir} 不存在，跳过维度批量处理")
        return
    
    for dimension in EvaluationDimension:
        print(f"\n--- 批量处理 {dimension.value} 维度 ---")
        
        result = batch_processor.process_directory_by_dimension(
            corpus_dir=corpus_dir,
            dimension=dimension,
            file_pattern="*.md",
            output_dir=output_dir
        )
        
        if "error" in result:
            print(f"维度批量处理错误: {result['error']}")
            continue
        
        summary = result["summary"]
        print(f"处理文件数: {summary['total_files']}")
        print(f"平均评分: {summary['average_score']}")
        
        if summary.get("top_metric_issues"):
            print("主要指标问题:")
            for issue in summary["top_metric_issues"][:3]:
                print(f"  {issue['metric']}: {issue['frequency']} 次")


def example_metric_management():
    print("\n=== 指标管理示例 ===")
    
    evaluator_manager = EvaluatorManager()
    batch_processor = BatchProcessor(evaluator_manager)
    
    print("当前指标状态:")
    metrics_status = batch_processor.get_dimension_metrics_status()
    for dimension, metrics in metrics_status.items():
        print(f"\n{dimension} 维度:")
        for metric, enabled in metrics.items():
            status = "启用" if enabled else "禁用"
            print(f"  {metric}: {status}")
    
    print("\n禁用完整性维度的代码片段完整性指标...")
    batch_processor.disable_dimension_metric(
        EvaluationDimension.COMPLETENESS, 
        "code_snippets_completeness"
    )
    
    print("启用正确性维度的格式正确性指标...")
    batch_processor.enable_dimension_metric(
        EvaluationDimension.CORRECTNESS, 
        "format_correctness"
    )
    
    print("\n更新后的指标状态:")
    updated_status = batch_processor.get_dimension_metrics_status()
    for dimension, metrics in updated_status.items():
        print(f"\n{dimension} 维度:")
        for metric, enabled in metrics.items():
            status = "启用" if enabled else "禁用"
            print(f"  {metric}: {status}")


def example_custom_configuration():
    print("\n=== 自定义配置示例 ===")
    
    config_path = Path("ft_corpus_evaluator/config/evaluator_config.json")
    
    evaluator_manager = EvaluatorManager(dimension_config_path=str(config_path))
    
    print("使用自定义配置创建评估管理器")
    print(f"配置文件: {config_path}")

    print("维度评估器已启用")

    corpus_file = Path("corpus/RAN-5869391.md")
    if corpus_file.exists():
        parser = CorpusParser()
        corpus = parser.parse_file(corpus_file)

        report = evaluator_manager.evaluate_corpus(corpus)
        print(f"评估完成，综合评分: {report.overall_score:.2f}")
    else:
        print("示例文件不存在，跳过评估")





def main():
    print("维度评估框架使用示例")
    print("=" * 50)
    
    try:
        example_single_file_evaluation()
        example_dimension_specific_evaluation()
        # example_batch_processing()  # 暂时跳过批量处理
        # example_dimension_batch_processing()  # 暂时跳过维度批量处理
        example_metric_management()
        example_custom_configuration()
        
        print("\n" + "=" * 50)
        print("所有示例执行完成！")
        
    except Exception as e:
        print(f"示例执行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
