#!/usr/bin/env python3
"""
测试TargetTestCaseDescription修复
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from ft_corpus_evaluator.parsers.markdown_corpus_parser import MarkdownCorpusParser


def test_target_description_fix():
    """测试TargetTestCaseDescription修复"""
    print("🧪 测试TargetTestCaseDescription修复")
    print("=" * 60)
    
    test_file = Path("corpus/测试用例 RAN-6612580_ 低速迁出测量场景，存在gumai惩罚的邻区，异系统测量上报无输出.md")
    if not test_file.exists():
        print("❌ 测试文件不存在")
        return False
    
    # 创建解析器
    parser = MarkdownCorpusParser()
    
    # 解析文件
    result = parser.parse_file(test_file)
    
    print("✅ 解析成功！")
    
    # 测试TargetTestCaseDescription
    print("\n📋 TargetTestCaseDescription 测试:")
    if result.target_test_case_description:
        content = result.target_test_case_description.content
        print(f"  ✅ 有内容，长度: {len(content)} 字符")
        
        # 检查是否包含关键内容
        key_items = [
            "## STEP1: 网管参数配置",
            "### 用例需要配置参数列表",
            "### 涉及参数详情",
            "ManagedElement.GNBCUCPFunction.MobilityGlobalConfig.HOFailPunish",
            "hoPrepareFailPunishSwch",
            "lowSpeedNCellMigSwch",
            "低速用户向普通小区迁移开关"
        ]
        
        missing_items = []
        for item in key_items:
            if item not in content:
                missing_items.append(item)
        
        if not missing_items:
            print("  ✅ 包含所有关键内容")
        else:
            print(f"  ❌ 缺少内容: {missing_items}")
        
        # 检查格式是否保持
        has_step_headers = "## STEP1:" in content
        has_subsections = "### " in content
        has_json_blocks = "```json" in content
        has_lists = "- " in content
        
        print(f"  {'✅' if has_step_headers else '❌'} 保持STEP标题")
        print(f"  {'✅' if has_subsections else '❌'} 保持子标题")
        print(f"  {'✅' if has_json_blocks else '❌'} 保持JSON代码块")
        print(f"  {'✅' if has_lists else '❌'} 保持列表格式")
        
        # 显示内容预览
        print(f"\n📄 内容预览（前500字符）:")
        print(content[:500])
        print("...")
        
        success = (not missing_items and has_step_headers and 
                  has_subsections and has_json_blocks and has_lists and
                  len(content) > 1000)  # 应该有足够的内容
        
    else:
        print("  ❌ TargetTestCaseDescription为None")
        success = False
    
    print(f"\n📊 测试结果:")
    print(f"  TargetTestCaseDescription: {'✅ 成功' if success else '❌ 失败'}")
    
    return success


if __name__ == "__main__":
    success = test_target_description_fix()
    sys.exit(0 if success else 1)
