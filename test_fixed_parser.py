#!/usr/bin/env python3
"""
测试修复后的MarkdownCorpusParser
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from ft_corpus_evaluator.parsers.markdown_corpus_parser import MarkdownCorpusParser


def test_fixed_parser():
    """测试修复后的解析器"""
    print("🧪 测试修复后的MarkdownCorpusParser")
    print("=" * 60)
    
    test_file = Path("corpus/测试用例 RAN-6612580_ 低速迁出测量场景，存在gumai惩罚的邻区，异系统测量上报无输出.md")
    if not test_file.exists():
        print("❌ 测试文件不存在")
        return
    
    # 创建解析器
    parser = MarkdownCorpusParser()
    
    # 解析文件
    result = parser.parse_file(test_file)
    
    print("✅ 解析成功！")
    print("\n📋 TestInfo 解析结果:")
    print(f"  测试标题: {repr(result.test_info.test_title)}")
    print(f"  预置条件: {repr(result.test_info.preconditions[:100])}...")
    print(f"  TC步骤: {repr(result.test_info.tc_steps)}")
    print(f"  TC预期结果: {repr(result.test_info.tc_expected_results)}")
    print(f"  预期结果: {repr(result.test_info.expected_results)}")
    print(f"  验收准则: {repr(result.test_info.pass_criteria)}")
    
    # 验证所有字段都有值
    fields_with_values = 0
    if result.test_info.test_title: fields_with_values += 1
    if result.test_info.preconditions: fields_with_values += 1
    if result.test_info.tc_steps: fields_with_values += 1
    if result.test_info.tc_expected_results: fields_with_values += 1
    if result.test_info.expected_results: fields_with_values += 1
    if result.test_info.pass_criteria: fields_with_values += 1
    
    print(f"\n📊 统计:")
    print(f"  有值的字段数量: {fields_with_values}/6")
    print(f"  解析成功率: {fields_with_values/6*100:.1f}%")
    
    if fields_with_values == 6:
        print("\n🎉 所有TestInfo字段都成功解析！")
    else:
        print(f"\n⚠️  还有 {6-fields_with_values} 个字段没有值")
    
    return fields_with_values == 6


if __name__ == "__main__":
    success = test_fixed_parser()
    sys.exit(0 if success else 1)
