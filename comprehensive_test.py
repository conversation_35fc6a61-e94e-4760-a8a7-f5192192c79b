#!/usr/bin/env python3
"""
全面测试新的markdown解析器
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from ft_corpus_evaluator.parsers.corpus_parser import CorpusParser
from ft_corpus_evaluator.parsers.markdown_corpus_parser import MarkdownCorpusParser


def test_all_corpus_files():
    """测试所有语料文件"""
    
    corpus_dir = Path("corpus")
    if not corpus_dir.exists():
        print("corpus目录不存在")
        return
    
    md_files = list(corpus_dir.glob("*.md"))
    if not md_files:
        print("没有找到markdown文件")
        return
    
    print(f"找到 {len(md_files)} 个markdown文件")
    print("=" * 80)
    
    old_parser = CorpusParser()
    new_parser = MarkdownCorpusParser()
    
    for i, file_path in enumerate(md_files):
        print(f"\n[{i+1}/{len(md_files)}] 测试文件: {file_path.name}")
        print("-" * 60)
        
        try:
            # 使用原解析器
            old_result = old_parser.parse_file(file_path)
            
            # 使用新解析器
            new_result = new_parser.parse_file(file_path)
            
            # 比较结果
            compare_results(old_result, new_result, file_path.name)
            
        except Exception as e:
            print(f"解析文件 {file_path.name} 时出错: {e}")
            import traceback
            traceback.print_exc()


def compare_results(old_result, new_result, filename):
    """比较两个解析器的结果"""
    
    print(f"文件: {filename}")
    
    # 比较RdcInfo
    print("RdcInfo比较:")
    rdc_fields = ['rdc_id', 'repo_name', 'gerrit_link', 'date']
    for field in rdc_fields:
        old_val = getattr(old_result.rdc_info, field, "")
        new_val = getattr(new_result.rdc_info, field, "")
        status = "✓" if old_val == new_val else "✗"
        print(f"  {field}: {status} (原:{len(old_val)} 新:{len(new_val)})")
    
    # 比较TestInfo
    print("TestInfo比较:")
    test_fields = ['test_title', 'preconditions', 'tc_steps', 'tc_expected_results']
    for field in test_fields:
        old_val = getattr(old_result.test_info, field, "")
        new_val = getattr(new_result.test_info, field, "")
        
        if isinstance(old_val, list) and isinstance(new_val, list):
            status = "✓" if len(old_val) == len(new_val) else "✗"
            print(f"  {field}: {status} (原:{len(old_val)} 新:{len(new_val)})")
        else:
            old_len = len(str(old_val)) if old_val else 0
            new_len = len(str(new_val)) if new_val else 0
            status = "✓" if old_len == new_len else "✗"
            print(f"  {field}: {status} (原:{old_len} 新:{new_len})")
    
    # 比较TagIdentification
    print("TagIdentification比较:")
    old_business = len(old_result.tag_identification.business_content_scene_tags)
    new_business = len(new_result.tag_identification.business_content_scene_tags)
    old_code = len(old_result.tag_identification.code_modify_scene_tags)
    new_code = len(new_result.tag_identification.code_modify_scene_tags)
    
    business_status = "✓" if old_business == new_business else "✗"
    code_status = "✓" if old_code == new_code else "✗"
    
    print(f"  business_tags: {business_status} (原:{old_business} 新:{new_business})")
    print(f"  code_tags: {code_status} (原:{old_code} 新:{new_code})")
    
    # 比较CodeSnippets
    print("CodeSnippets比较:")
    old_snippets = len(old_result.code_snippets)
    new_snippets = len(new_result.code_snippets)
    snippets_status = "✓" if abs(old_snippets - new_snippets) <= 2 else "✗"  # 允许小差异
    print(f"  code_snippets: {snippets_status} (原:{old_snippets} 新:{new_snippets})")
    
    # 比较DependencyCode
    print("DependencyCode比较:")
    old_dep = len(old_result.dependency_code)
    new_dep = len(new_result.dependency_code)
    dep_status = "✓" if abs(old_dep - new_dep) <= 2 else "✗"  # 允许小差异
    print(f"  dependency_code: {dep_status} (原:{old_dep} 新:{new_dep})")
    
    # 比较SimilarCodeInfo
    print("SimilarCodeInfo比较:")
    old_similar = old_result.similar_code_info is not None
    new_similar = new_result.similar_code_info is not None
    similar_status = "✓" if old_similar == new_similar else "✗"
    print(f"  similar_code_info存在: {similar_status} (原:{old_similar} 新:{new_similar})")


def performance_test():
    """性能测试"""
    import time
    
    test_file = Path("corpus/RAN-5869391.md")
    if not test_file.exists():
        print("测试文件不存在，跳过性能测试")
        return
    
    print("\n性能测试:")
    print("=" * 40)
    
    # 测试原解析器
    old_parser = CorpusParser()
    start_time = time.time()
    for _ in range(10):
        old_parser.parse_file(test_file)
    old_time = time.time() - start_time
    
    # 测试新解析器
    new_parser = MarkdownCorpusParser()
    start_time = time.time()
    for _ in range(10):
        new_parser.parse_file(test_file)
    new_time = time.time() - start_time
    
    print(f"原解析器 (10次): {old_time:.4f}s (平均: {old_time/10:.4f}s)")
    print(f"新解析器 (10次): {new_time:.4f}s (平均: {new_time/10:.4f}s)")
    
    if new_time < old_time:
        improvement = ((old_time - new_time) / old_time) * 100
        print(f"新解析器快 {improvement:.1f}%")
    else:
        degradation = ((new_time - old_time) / old_time) * 100
        print(f"新解析器慢 {degradation:.1f}%")


if __name__ == "__main__":
    print("开始全面测试markdown解析器...")
    
    # 测试所有文件
    test_all_corpus_files()
    
    # 性能测试
    performance_test()
    
    print("\n全面测试完成!")
