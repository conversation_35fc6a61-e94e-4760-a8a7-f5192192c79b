**RdcInfo**
- rdc_id:RAN-5869391
- repo_name:5g_nr_v3/cpf/ucs/huc_alpha
- gerrit_link:https://gerrit.zte.com.cn/#/c/19399836/
- date:2024-08-26
- BasebandBoard: 通用
- domain: 03-5G-CPA
- compile_script:ft/uc/update-compile-cmd.sh
- compile_command_json_path:ft/uc/compileCmdDir
## TestInfo

### 测试标题: 
kpiAdjustRatio【4】等于N（非0、非100），随机值等于N，SN触发pscellchange失败，SGNB_MODIFICATION_REFUSE消息中携带的 “procedure cancel”，则TY

### 预制条件:
NSA组网：
1.gNB1上建立小区cell1、cell2运行正常。eNB1上建立小区cell3运行正常。
2.cell1、cell2、cell3互相配置邻区关系。满足SN添加条件。

### TC步骤:
测试步骤: 
设置kpiAdjustRatio=0;0;0;0;N;0（其中N为非0、非100），radom（y）的生成随机值等于N，SN触发pscellchange失败，SGNB_MODIFICATION_REFUSE消息中携带的 “procedure cancel”




### TC预期结果:
测试结果: 
本次失败TY，如下标准计数器统计值为：
C600690047 PSCellChange请求次数：+1；
C600690048 PSCellChange成功次数：+1；
C600690001 SN触发的PSCellChange请求次数：+1；
C600690011 SN触发的PSCellChange成功次数，测量触发：+1；
C600690049 SN触发的异频PSCellChange请求次数：+1；
C600690050 SN触发的异频PSCellChange成功次数：+1；
C600690057 基于负荷均衡的SN触发的PSCellChange请求次数：+1；
C600690058 基于负荷均衡的SN触发的PSCellChange成功次数：+1；
C600600016 SN内部PSCell的变更成功次数，测量触发：+1；
C600600017 SN内部PSCell的变更失败次数，F1 Context建立失败：0；
C600600018 SN内部PSCell的变更失败次数，重配完成超时：0；
C600600019 SN内部PSCell的变更失败次数，其他原因：0；
C605030019 灵活可配的PLMN的PSCellChange请求次数：+1；
C605030020 灵活可配的PLMN的PSCellChange成功次数：+1；

信令，metrics，autolog均不TY，存在按照实际场景上报，如下内部计数器统计值为：
SN触发pscellchange失败，SnModRefuse失败，由于other原因：0；
SN触发pscellchange失败，SnModRefuse失败，由于流程冲突：+1；
SN触发pscellchange失败，SnModRefuse失败，由于UE_lost：0；

如下标准计数器不TY,按照实际场景上报：
C600600020 SN修改成功次数：0；
C600600021 SN修改失败次数,F1 Context修改失败：0；
C600600022 SN修改失败次数,X2口重配超时：0；
C600600023 SN修改失败次数，其它原因：+1；
C600690043 SN触发SN修改请求次数：+1；
C600690044 SN触发SN修改成功次数：0；
C605030017 灵活可配的PLMN的SN触发SN修改请求次数：+1；
C605030018 灵活可配的PLMN的SN触发SN修改成功次数：0； 




### 预期结果:
<p>见单步确认结果</p>

### 通过准则:
<p>见单步确认结果</p>


## Tag Identification
### business_content_scence_tag
- asn消息构造
- KPI校验
### code_modify_scence_tag
- type:add_test


**QUESTION**

作为5G通信协议开发专家和C++高级工程师，您需要根据**目标测试用例描述**，根据**代码生成要求**,生成**目标测试用例描述**测试代码

# 目标用例生成可能依赖代码

代码路径:ft/uc/ftest/nsa/include/ft-sn-pscell-change/domain/kpi/TTrigInraSnPscellChangeStat.h
```cpp
DECL_EXPECT_KPI(TRIG_INTRA_SN_PSCELL_CHANGE_STAT, TTrigIntraSNPScellChangeStat)
```

代码路径:ft/uc/ftest/common/include/ft-common/domain/tools/kpi/TTrigPsCellChange.h
```cpp
DECL_EXPECT_KPI(TRIG_CU_PSCELL_CHANGE_STAT, TrigCuPScellChangeStat)
```

代码路径:ft/uc/ftest/common/src/ft-common/domain/tools/sigtrace/ToolSignalTraceAsserter.cpp
```cpp
void ToolSignalTraceAsserter::clear()
{
    procDataId.clear();
    sigTraceDatas.clear();
    cdtsigTraceDatas.clear();
}
```

代码路径:code/ms/uc/debug/stub/src/UcDebug.cpp
```cpp
DEF_PRINTER_WITH_FILE_NAME(setSignalTraceSwitch(BYTE status), "OFF(0) ON(1)")
{
    if(ucs_lm::LoadStateManager::getInstance().getCpuLoadLevel() <= ucs_if::medium)
    {
        USI_NS::GlobalSwitch::setSignalTrace(0 != status);
    }
}
```

代码路径:code/ms/uc/debug/stub/src/UeDebug.cpp
```cpp
DEF_PRINTER_WITH_FILE_NAME(setFineAdjustMnRefuseProcCancel(WORD16 value), "set Fine Adjust Mn Refuse Procedure Cancel")
{
    Mo::setFineAdjustMnRefuseProcCancel(value);
}
```

代码路径:ft/uc/ftest/nsa/src/ft-sn-pscell-change/FtSnPscellChange.cpp
```cpp
void preSuccess()
    {
        sendChangeIndicateFromStableOp();
        PsCellChgf1RouteSelectionAction();
        drbConfigAction();
        dstBpfUeContextModifyAction();
        pscellChgDrbDuGtpuInfoCfgAction();
    }
```

代码路径:ft/uc/ftest/nsa/src/ft-sn-pscell-change/action/FtSnPscellChangeAction.cpp
```cpp
void FtSnPscellChangeAction::srcBpfUeContextModifyAction()
{
    servicePscellChange <---- fakeOss(__EventId(EV_T_PSCELL_CHANGE_STOP_INDICATOR_TIMEOUT), P_S_NR_PSCELLCHANG_INSTANCE);
    servicePscellChange ----> fakeBpf (S_UE_CONFIG, __InstKey(PsCellChgHucToLucmKey), __EventId(DuUeCtxtModifyReqMsg), SnPscellUeCtxtModifySrcReqAsserter());
    servicePscellChange <---- fakeBpf (S_UE_CONFIG, __InstKey(PsCellChgLucToHucKey), __EventId(DuUeCtxtModifyRspMsg), UeContextModifyRspBuilder(), P_S_NR_PSCELLCHANG_INSTANCE);
}
```

代码路径:ft/uc/ftest/nsa/src/ft-sn-pscell-change/action/FtSnPscellChangeAction.cpp
```cpp
void FtSnPscellChangeAction::x2PscellRRCRecfgRefuseAction()
{
    servicePscellChange ----> fakeBrs (S_UCS_BRS, __InstKey(x2sctpKey), __EventId(brs_sctp::Message::kSctpDataRequest), PScellChangeRrcRecfgAsserter());
    servicePscellChange <---- fakeUcm (S_HUCM_HUC, __InstKey(ucmUcKey), __EventId(hucm_if::Message::kX2SnModifyRefuse), SnModifyRefuseBuilder());
}
```

代码路径:ft/uc/ftest/nsa/src/ft-sn-pscell-change/action/FtSnPscellChangeAction.cpp
```cpp
void FtSnPscellChangeAction::relPceSourceLinkAction()
{
    if(GET_SCENCE("PsCellChgHoBack"))
    {
        servicePscellChange ----> fakeDpf (S_CPF_DPF, __InstKey(PsCellChgDpfKey), __EventId(e1ap::DrbMessage::kSnDrbContextModifyRequest), PceRelOneSizeLinkReqAsserter(DRB_LINK_INFO_TARGET_CONFIG, IS_PSCELL_CHG));
    }
    else
    {
        servicePscellChange ----> fakeDpf (S_CPF_DPF, __InstKey(PsCellChgDpfKey), __EventId(e1ap::DrbMessage::kSnDrbContextModifyRequest), PceRelOneSizeLinkReqAsserter(DRB_LINK_INFO_SOURCE_CONFIG, IS_PSCELL_CHG));
    }
    servicePscellChange <---- fakeDpf (S_CPF_DPF, __InstKey(PsCellChgDpfKey), __EventId(e1ap::DrbMessage::kSnDrbContextModifyResponse),DpfModRelRspBuilder(),P_S_NR_PSCELLCHANG_ADDRESS);
}
```

代码路径:ft/uc/ftest/nsa/src/ft-sn-pscell-change/action/FtSnPscellChangeAction.cpp
```cpp
void FtSnPscellChangeAction::psCellChgNotifyAnrAction(bool result, WORD32 neighborPCI, WORD32 nrCellID)
{
    if(GET_SCENCE("PsCellChgHoBack"))
    {
        servicePscellChange ----> fakeSon (S_UC_SON_UE_NOTIFY, __InstKey(PsCellChgBackAnrKey), __EventId(ucs_son::Message::kPscellChangeStatisticsNotify), PscellChangeNotifyAnrAsserter(result, neighborPCI, nrCellID));
    }
    else
    {
        servicePscellChange ----> fakeSon (S_UC_SON_UE_NOTIFY, __InstKey(PsCellChgAnrKey), __EventId(ucs_son::Message::kPscellChangeStatisticsNotify), PscellChangeNotifyAnrAsserter(result, neighborPCI, nrCellID));
    }
}
```

代码路径:ft/uc/ftest/nsa/src/ft-sn-pscell-change/FtSnPscellChange.cpp
```cpp
void releaseInStable()
    {
        X2SnRelRequiredAndConfirmAction();
        bpfUeCtxtModifyStopTransAction();
        dpfDataForwardAction();
        sendUeCtxtRelFromUcm();
        dpfRelAction();
        bpfRelAction();
        snPsGnbResourceReportWhenSnDelAction();
        cpfUeIdRelAction();
    }
```

代码路径:ft/uc/ftest/common/include/ft-common/domain/tools/kpi/common/GenericKpiSetter.h
```cpp
DEF_KPI_COUNTER_FIELD_WITH_PREX_TYPE(WORD16, tNrLteCellPairObject, gNBIdLength)
```

代码路径:ft/uc/ftest/common/include/ft-common/domain/tools/kpi/common/GenericKpiSetter.h
```cpp
DEF_KPI_COUNTER_FIELD_WITH_PREX_TYPE(WORD32, tNrLteCellPairObject, gNBplmn)
```

代码路径:ft/uc/ftest/common/include/ft-common/domain/tools/kpi/common/GenericKpiSetter.h
```cpp
DEF_KPI_COUNTER_FIELD_WITH_PREX_TYPE(WORD16, tNrLteCellPairObject, eCellId)
```

代码路径:ft/uc/ftest/common/include/ft-common/domain/tools/kpi/common/GenericKpiSetter.h
```cpp
DEF_KPI_COUNTER_FIELD_WITH_PREX_TYPE(WORD32, tNrLteCellPairObject, RelationgNBplmn)
```

代码路径:ft/uc/ftest/common/include/ft-common/domain/tools/kpi/common/GenericKpiSetter.h
```cpp
DEF_KPI_COUNTER_FIELD_WITH_PREX_TYPE(WORD32, tNrLteCellPairObject, eNodebId)
```

代码路径:ft/uc/ftest/common/include/ft-common/domain/tools/kpi/common/GenericKpiSetter.h
```cpp
DEF_KPI_COUNTER_FIELD_WITH_PREX_TYPE(WORD16, tNrLteCellPairObject, cellId)
```

代码路径:ft/uc/ftest/common/include/ft-common/domain/tools/kpi/common/GenericKpiSetter.h
```cpp
DEF_KPI_COUNTER_FIELD_WITH_PREX_TYPE(WORD32, tNrLteCellPairObject, gNBId)
```

代码路径:ft/uc/ftest/common/include/ft-common/domain/tools/kpi/common/GenericKpiSetter.h
```cpp
DEF_KPI_COUNTER_BYTE_FIELD(byCounterType)
```

代码路径:ft/uc/ftest/common/include/ft-common/domain/tools/kpi/common/GenericKpiSetter.h
```cpp
DEF_KPI_COUNTER_FIELD_WITH_PREX_TYPE(WORD16, tPlmnInfoCUObject, cellId)
```

代码路径:ft/uc/ftest/common/include/ft-common/domain/tools/kpi/common/GenericKpiSetter.h
```cpp
DEF_KPI_COUNTER_FIELD_WITH_PREX_TYPE(WORD16, tPlmnInfoCUObject, gNBIdLength)
```

代码路径:ft/uc/ftest/common/include/ft-common/domain/tools/kpi/common/GenericKpiSetter.h
```cpp
DEF_KPI_COUNTER_FIELD_WITH_PREX_TYPE(WORD32, tPlmnInfoCUObject, gNBId)
```

代码路径:ft/uc/ftest/common/include/ft-common/domain/tools/kpi/common/GenericKpiSetter.h
```cpp
DEF_KPI_COUNTER_FIELD_WITH_PREX_TYPE(WORD32, tPlmnInfoCUObject, gNBplmn)
```

代码路径:ft/uc/ftest/common/include/ft-common/domain/tools/kpi/common/GenericKpiSetter.h
```cpp
DEF_KPI_COUNTER_BYTE_FIELD(byDirection)
```

代码路径:ft/uc/ftest/common/src/ft-common/domain/mo/builder/MoChangeNotifyBuilder.cpp
```cpp
Status MoChangeKpiAdjustRatioNotifyBuilder::doBuild(char* msg, FakeSystem& sys) const
{
    char* notify =R"(
           {"path":"#:DvFunction.DvUcs.DvUcsPara[dvAtt='0'].UcsKpiOptPara.kpiAdjustRatio",
           "serialNumber":11,
           "sid":7,
           "size":4,
           "total":6,
           "value":"1;1;1;4",
           "value_old":"0;0;0;2"}
    )";
    strcpy(msg, notify);
    return 0;
}
```


# 相似代码信息
## 相似文本测试用例描述
无

## 相似用例FT代码
代码路径:ft/uc/ftest/nsa/src/ft-sn-pscell-change/FtSnPscellChange.cpp
```cpp

TEST_F(FtSnPscellChangeWithoutSrb3, snpscellchange_without_srb3_roll_back_fail_after_stop_ind_timer_timeout_and_recv_x2_sn_modify_refuse)
{
    ToolSignalTraceAsserter::clear();
    setSignalTraceSwitch(1);
    SET_SCENCE("DrmRspWithServingCellMo");

    preSuccess();

    SET_SCENCE("IntraMnHo");
    sendIntraMnHoWithSnFromHucm();
    REMOVE_SCENCE("IntraMnHo");

    srcBpfUeContextModifyAction();

    x2PscellRRCRecfgRefuseAction();

    FtSnPscellChangeAction::relPceSourceLinkAction();
    psCellChgNotifyAnrAction(false);
    FtSnDelAction::snModRejectAction();
    releaseInStable();

}
```

## 相似用例代码依赖
无

# 代码生成要求
请根据提供的相关信息和相似信息，遵循以下原则进行测试代码生成：
1. 完整性原则
  - 必须输出完整的函数实现，包括函数声明、函数体和结束花括号
  - 不允许使用省略号或只显示修改部分
  - 保持函数的完整上下文


2. 参数化设计原则
  - 新增逻辑应通过函数参数控制
  - 避免硬编码固定值
  - 保持函数接口的灵活性


4. 测试用例适配
  - 确保测试用例覆盖新增功能
  - 维护测试用例的正确性
  - 补充必要的测试场景

5.代码修改文件生成方法

  -对于测试套和测试代码，按照相似用例文件名输出

  -对于action代码及函数体及修改，按照原有代码文件名输出

# 各类用例内容代码修改方法示例
## 设置参数开关
- 设置参数开关主要实在测试用例的开头预先通过API接口设置某些参数开关或配置某些参数为预期的值，以达到模拟测试场景的效果
### 例子
设置参数开关:打开协议对齐开关
接口信息：static void setProtocolAlignSwch(BYTE swch, const string& moId);
```cpp
TEST_F(FtResume, should_resume_success_when_mo_protocolcompatibilityswch_is_open_transfer_protocol_align_ind)
{
    Mo::setProtocolAlignSwch(1, "0");
    //....
}
```
## 预置稳态数据
- 预置稳态数据主要是在测试用例的开头预先设置流程中需要使用的上下文内容，以达到模拟测试场景的效果
- 预置稳态数据的相关接口声明和实现都在类UeDataUca2CommitBuilder中，在该类中修改现有或新增静态方法后，在测试用例开头调用
### 例子
预置稳态数据：设置AMF信息并启用TP记录开关
```cpp
static Status addAmfInfo(AmfInfo amfInfo = AmfInfo::BOTH, bool isReleaseSence = false);

Status UeDataUca2CommitBuilder::addAmfInfo(AmfInfo amfInfo, bool isReleaseSence)
{
    buildField(addAmfNgInfo, amfInfo, isReleaseSence);
    return USI_SUCCESS;
}

string addAmfNgInfo(string& s, AmfInfo& amfInfo, bool isReleaseSence)
    {
        p2s::AccessMsgRsp rsp;
        rsp.ParseFromString(s);

        PRINT_PB(rsp);

        p2s::ng_proto::Ng ng;
        auto list = rsp.mutable_rsp_data();
        for(int i = 0;i < list->size(); i++)
        {
            if(list->Get(i).type() == p2s::NG)
            {
                ng.ParseFromString(list->Get(i).data());
                if(amfInfo == AmfInfo::BOTH || amfInfo == AmfInfo::BOTH_3MNC)
                {
                    ng.mutable_ue_ngap_id_pair()->mutable_mme_ngap_ueid()->set_mme_ngap_ue_id(AMF_NG1AP_UE_ID);

                    FT_AUTO_PTR(ueNgapIdPair, ng.mutable_ue_ngap_id_pair());
                    FT_AUTO_PTR(mmeNgapUeid, ueNgapIdPair->mutable_mme_ngap_ueid());
                    mmeNgapUeid->set_mme_ngap_ue_id(AMF_NG1AP_UE_ID);

                    FT_AUTO_PTR(guami, ng.mutable_guami());
                    guami->set_amf_pointer(AMF_POINTER_ID);
                    guami->set_amf_region_id(AMF_REGION_ID);
                    guami->set_amf_set_id(AMF_SET_ID);
                    if(isReleaseSence)
                    {
                        if(amfInfo == AmfInfo::BOTH_3MNC)
                        {
                            guami->mutable_plmn()->set_mcc(servingPlmnId_mnc3dig.getMcc(),servingPlmnId_mnc3dig.getMccLen());
                            guami->mutable_plmn()->set_mnc(servingPlmnId_mnc3dig.getMnc(),servingPlmnId_mnc3dig.getMncLen());
                        }
                        else
                        {
                            guami->mutable_plmn()->set_mcc(servicePlmnId.getMcc(),servicePlmnId.getMccLen());
                            guami->mutable_plmn()->set_mnc(servicePlmnId.getMnc(),servicePlmnId.getMncLen());
                        }
                    }
                }
                else if(amfInfo == AmfInfo::ONLY_AMF_UE_ID)
                {
                    ng.clear_guami();
                    ng.mutable_ue_ngap_id_pair()->mutable_mme_ngap_ueid()->set_mme_ngap_ue_id(AMF_NG1AP_UE_ID);

                    FT_AUTO_PTR(ueNgapIdPair, ng.mutable_ue_ngap_id_pair());
                    FT_AUTO_PTR(mmeNgapUeid, ueNgapIdPair->mutable_mme_ngap_ueid());
                    mmeNgapUeid->set_mme_ngap_ue_id(AMF_NG1AP_UE_ID);
                }
                else if(amfInfo == AmfInfo::NOTALL)
                {
                    FT_AUTO_PTR(guami, ng.mutable_guami());
                    ng.clear_guami();
                    if(ng.has_ue_ngap_id_pair() && ng.ue_ngap_id_pair().has_mme_ngap_ueid())
                    {
                        ng.clear_ue_ngap_id_pair();
                    }
                }

                list->DeleteSubrange(i,1);
                PRINT_PB(ng)
            }
        }

        auto* r = rsp.add_rsp_data();
        r->set_type(p2s::NG);
        r->set_data(ng.SerializeAsString());

        return rsp.SerializeAsString();
    }

TEST_F(FtResume, should_resume_success_when_mo_protocolcompatibilityswch_is_close_no_transfer_protocol_align_ind)
{
    UeDataUca2CommitBuilder::addAmfInfo(); 
    //...
}   
```

## 稳态数据校验
- 主要是在业务流程结束后，校验当前流程的指定上下文中的数据是符合预期
- 新增或修改现有的FieldAsserter类，实现校验内容，对外提供校验接口，测试用例在结束时调用
### 例子
校验SpecialUeField的identify type为Trace_Id，type类型为TYPE_7
```cpp
static void doAssertIdentify(const WORD32 ueId, const BYTE identifyType, const BYTE vipType);

void SpecialUeFieldAssert::doAssertIdentify(const WORD32 ueId, const BYTE identifyType, const BYTE vipType)
{
    auto ueDataSharePtr = getUeDataByDataType(ueId, p2s::SPECIAL_UE_INFO);
    dumpUeDataByDataType(ueId, p2s::SPECIAL_UE_INFO);
    auto* specialUeInfo = (p2s::SpecialUeInfo*) ueDataSharePtr.get();
    FT_ASSERT_VALID_PTR(specialUeInfo);
    FT_ASSERT_TRUE(specialUeInfo->identify_type() == identifyType);
    FT_ASSERT_TRUE(specialUeInfo->vip_type() == vipType);
}

TEST_F(FtPduSessionSetupWithAtu, should_atu_pick_succ_for_trace_id_when_pdusessionsetup)
{
    //...
    SpecialUeFieldAssert::doAssertIdentify(CPF_UE_ID,Trace_Id,TYPE_7);
}
```

## 业务流程消息校验
- 模块在业务流程中向外部模块发送消息，用例需要校验该消息的内容是否符合预期
- 消息的交互都封装在用例的Action方法中，其中使用语法糖**---->**表示向外部发送消息，包含发送该消息使用的Session，InstKey，EventId和负责校验的Asserter类构造
- Asserter类负责实现具体的校验细节，其可能通过组装的方式，将消息的不同内容封装成不同的Asserter类，然后使用组装的方式来实现用例的具体校验要求
### 例子
PduSessionResourceSetupReq中携带安全信息信息
```cpp
struct E1PduSessionResourceSetupReqWithSecurityAsserter : E1PduSessionResourceSetupAbsReqAsserter
{
    E1PduSessionResourceSetupReqWithSecurityAsserter( bool isUpCapIntegrityProtectNia0 = false):
        isUpCapIntegrityProtectNia0(isUpCapIntegrityProtectNia0){}
    OVERRIDE(void assertMore(const e1ap::PduSessionResourceSetupRequest& req) const);
private:
    bool isUpCapIntegrityProtectNia0;
};

void E1PduSessionResourceSetupReqWithSecurityAsserter::assertMore(const e1ap::PduSessionResourceSetupRequest& req) const
{
    if(GlobalSwitch::isSecurityCapOn())
    {
       TEncIntInfo_up encIntInfo;
       TEncIntInfo_up* dest = &testKeyInfo;
       if(isUpCapIntegrityProtectNia0)
       {
           NAMESPACE(E1PduSessionResourceSetupReqAsserter)::driveKeyForFt(encIntInfo);
           dest = &encIntInfo;
       }

       FT_ASSERT_TRUE(req.has_security_config());
       auto securityCfg = req.security_config();

       FT_ASSERT_TRUE(securityCfg.security_algorithm().has_cipheing_algorithm());
       FT_ASSERT_TRUE(securityCfg.security_algorithm().cipheing_algorithm().value() == dest->dwEncAlg);

       if(g_drb_no_int)//打桩不配置DRB的完保算法。和空算法有区别
       {
           FT_ASSERT_TRUE(securityCfg.security_algorithm().has_integrity_protect_algorithm() == false);
       }
       else
       {
           FT_ASSERT_TRUE(securityCfg.security_algorithm().has_integrity_protect_algorithm());
           FT_ASSERT_TRUE(securityCfg.security_algorithm().integrity_protect_algorithm().value() == dest->dwInteAlg);
       }

       FT_ASSERT_TRUE(securityCfg.security_key().up_cipheing_key().size()==MAX_ENC_INT_KEY_LEN);
       FT_ASSERT_TRUE(memcmp((BYTE*)securityCfg.security_key().up_cipheing_key().c_str(), dest->aucKUpEnc, MAX_ENC_INT_KEY_LEN) == 0);
       FT_ASSERT_TRUE(securityCfg.security_key().up_integrity_protect_key().size()==MAX_ENC_INT_KEY_LEN);
       FT_ASSERT_TRUE(memcmp((BYTE*)securityCfg.security_key().up_integrity_protect_key().c_str(), dest->aucKUpInt, MAX_ENC_INT_KEY_LEN) == 0);
   }
}

void FtPduSessionSetupAction::dpfPduSessionSetupAction(initializer_list<E1PduSessionResourceSetupAbsReqAsserter*> list,bool isCnPdb)
{
    for(auto upId:FtPDUCfg::getAllUpId(BitInterval::more(__DRB_FAIL_PDU,true)))
    {
        localPduSetupAction(list,upId, isCnPdb);
    }
}

void FtPduSessionSetupAction::localPduSetupAction(initializer_list<E1PduSessionResourceSetupAbsReqAsserter*> list,WORD32 upId, bool isCnPdb)
{
    serviceUc ----> fakeDpf (S_CPF_DPF, __InstKey(dpfPduSetupKey), __EventId(e1ap::DrbMessage::kPduSessionResourceSetupRequest), E1PduSessionResourceSetupMutliReqAsserter(list, DPF_UE_ID, e1ap::ProcedureCode_ProcCode_MN_ERAB_SETUP));
    serviceUc <---- fakeDpf (S_CPF_DPF, __InstKey(dpfPduSetupKey), __EventId(e1ap::DrbMessage::kPduSessionResourceSetupResponse),E1PduSessionResourceSetupRspBuilder(upId, false, e1ap::UpSecurityResult_SecurityResult_REQUIRED, e1ap::UpSecurityResult_SecurityResult_REQUIRED, isCnPdb),P_S_NR_PDUSESSION_SETUP_ADDRESS);
}

TEST_F(FtPduSessionSetupWithAtu, should_atu_pick_succ_for_trace_id_when_pdusessionsetup)
{
    //业务流程省略
    //...
    dpfPduSessionSetupAction({new E1PduSessionResourceSetupReqWithSecurityAsserter(),
            new E1PduSessionResourceSetupReqWithPduListAsserter(INVALID_WORD32,false,1,SGW_TRAN_IP,sizeof(SGW_TRAN_IP),false,
            testRohc,e1ap::ForwardInfo_Type_direct_forward,true,false, 0, false)},false);
    //业务流程省略
    //...
}

```

## 业务流程消息构造
- 模块在业务流程收到外部模块发送的消息，再FT中进行构造后模拟收到外部模块发送的消息，用例需要构造符合预期的消息内容
- 消息的交互都封装在用例的Action方法中，其中使用语法糖**<----**表示收到外部发送消息，包含发送该消息使用的Session，InstKey，EventId和负责构造的Builder类实现
- Builder类负责实现具体的构造细节，其可能通过组装的方式，将消息的不同内容封装成不同的Builder类，然后使用组装的方式来实现用例的具体构造要求
### 例子
收到hucm模块的UeDlLargeSmallPacketInd消息
```cpp
void FtStableAction::recvUeDlLargeSmallPacketInd(hucm_if::Status status)
{
    serviceUc <---- fakeUcm (S_HUCM_HUC, __InstKey(ucmUcKey), __EventId(hucm_if::Message::kUeDlLargeSmallPacketInd), UeDlLargeSmallPacketIndBuilder(status));
}

struct UeDlLargeSmallPacketIndBuilder : GenericPbBuilder<hucm_if::Message>
{
    UeDlLargeSmallPacketIndBuilder(hucm_if::Status status = hucm_if::normal, hucm_if::Status caStatus = hucm_if::normal):status(status), caStatus(caStatus){}
    virtual Status doBuild(hucm_if::Message&, FakeSystem& system) const override;
private:
    hucm_if::Status status;
    hucm_if::Status caStatus;
};

Status UeDlLargeSmallPacketIndBuilder::doBuild(hucm_if::Message& msg, FakeSystem& sys) const
{
    AUTO_PTR(ind, msg.mutable_ue_dl_large_small_packet_ind());
    ind->set_cpf_ue_id(CPF_UE_ID);
    ind->set_status(status);
    ind->set_ca_status(caStatus);

    return USI_SUCCESS;
}

TEST_F(FtStable, should_transfer_ue_dl_large_small_packet_ind_with_normal_and_drm_no_output)
{
    attachAction();
    mockDrmUeLargeSmallPacketDecisionAction(p2s::drm::UeLargeSmallPacketInd_Status_normal,p2s::drm::UeLargeSmallPacketInd_Status_normal, NoOutPut, {}
            , {}, false, p2s::drm::UeLargeSmallPacketInd_Status_normal, p2s::drm::UeLargeSmallPacketInd_Status_normal, false, p2s::drm::LargeSmallPacketInd_Type_normal);
    recvUeDlLargeSmallPacketInd(hucm_if::normal);
    fakeDrm.clear();
}
```

# 建议的实现步骤
1. 先了解目标测试用例的各个测试点,学习每类测试点的修改方法
2. 阅读相似用例和给出的代码片段，了解各个代码之间的调用关系和业务语义
3. 逐条分析每个测试点，分析那些代码片段需要修改或新增,然和结合修改方法进行修改
4. 将**相似用例FT代码**完全作为你的基础用例,在该用例上进行修改和新增,你仅需要在基础用例上实现目标测试用例描述即可

# 输出格式
- 测试用例的命名必须按照TEST_F(测试套名称, should_{测试内容}_when_{预置条件}_then_{测试步骤})进行命名，{测试步骤}、{预置条件}和{测试内容}采用小写驼峰式命名并且禁止出现中文。

例子：TEST_F(InitOrcheTest, should_attach_success_when_uecap_switch_is_open)

新增或修改的测试代码
- path:路径信息
- type:变更类型 add_test|modify_code|add_code
- step_describe:修改涉及步骤标识，例如["STEP1"]
```cpp
```
例如：
- path:ft/uc_new/ftest/src/ft/feature/ft-suspend/FtSuspendAction.cpp
- type:modify_code
- step_describe:["STEP1"]
```cpp
完整的修改代码函数
```

# 输出要求
- 输出无关的代码不要做省略处理,你需要输出完整的函数片段

# 目标测试用例描述

## 业务流程消息构造
- STEP1:SN触发pscellchange失败，SGNB_MODIFICATION_REFUSE消息中携带的 “procedure cancel”
### 涉及接口
无
### 层级关系:
无


## KPI计数器校验
- STEP2:上报如下计数器：
C600690047 PSCellChange请求次数：+1；
C600690048 PSCellChange成功次数：+1；
C600690001 SN触发的PSCellChange请求次数：+1；
C600690011 SN触发的PSCellChange成功次数，测量触发：+1；
C600690049 SN触发的异频PSCellChange请求次数：+1；
C600690050 SN触发的异频PSCellChange成功次数：+1；
C605030019 灵活可配的PLMN的PSCellChange请求次数：+1；
C605030020 灵活可配的PLMN的PSCellChange成功次数：+1；

### 相关代码片段
```cpp
__TTrigIntraSNPScellChangeStat, byCounterType(IntraSnPSCellChange_Req).
                                                      tNrLteCellPairObject_gNBId(htonl(GNB_ID)).
                                                      tNrLteCellPairObject_cellId(htons(CELL_ID)).
                                                      tNrLteCellPairObject_eNodebId(htonl(ENB_ID)).
                                                      tNrLteCellPairObject_RelationgNBplmn(htonl(PLMN)).
                                                      tNrLteCellPairObject_eCellId(htons(ENB_CELL_ID)).
                                                      tNrLteCellPairObject_gNBplmn(htonl(GNBPLMN)).
                                                      tNrLteCellPairObject_gNBIdLength(htons(GNB_ID_LEN))

__TTrigIntraSNPScellChangeStat, byCounterType(IntraSnPSCellChange_Succ).
                                                      tNrLteCellPairObject_gNBId(htonl(GNB_ID)).
                                                      tNrLteCellPairObject_cellId(htons(CELL_ID)).
                                                      tNrLteCellPairObject_eNodebId(htonl(ENB_ID)).
                                                      tNrLteCellPairObject_RelationgNBplmn(htonl(PLMN)).
                                                      tNrLteCellPairObject_eCellId(htons(ENB_CELL_ID)).
                                                      tNrLteCellPairObject_gNBplmn(htonl(GNBPLMN)).
                                                      tNrLteCellPairObject_gNBIdLength(htons(GNB_ID_LEN))

__TrigCuPScellChangeStat, byCounterType(IntraSnPSCellChange_Req).
                                                   byDirection(PSCellChange_SgNBTrig).
                                                   tPlmnInfoCUObject_gNBplmn(htonl(GNBPLMN)).
                                                   tPlmnInfoCUObject_gNBId(htonl(GNB_ID)).
                                                   tPlmnInfoCUObject_gNBIdLength(htons(GNB_ID_LEN)).
                                                   tPlmnInfoCUObject_cellId(htons(CELL_ID)).
                                                   byIntraOrInter(IntraFeq)
__TrigCuPScellChangeStat, byCounterType(IntraSnPSCellChange_Succ).
                                                   byDirection(PSCellChange_SgNBTrig).
                                                   tPlmnInfoCUObject_gNBplmn(htonl(GNBPLMN)).
                                                   tPlmnInfoCUObject_gNBId(htonl(GNB_ID)).
                                                   tPlmnInfoCUObject_gNBIdLength(htons(GNB_ID_LEN)).
                                                   tPlmnInfoCUObject_cellId(htons(CELL_ID)).
                                                   byIntraOrInter(IntraFeq)
```




**ANSWER**

//STEPS_DESCRIBE_START
//STEP1:SN触发pscellchange失败，SGNB_MODIFICATION_REFUSE消息中携带的 “procedure cancel”
//*STEP2:上报如下计数器：
C600690047 PSCellChange请求次数：+1；
C600690048 PSCellChange成功次数：+1；
C600690001 SN触发的PSCellChange请求次数：+1；
C600690011 SN触发的PSCellChange成功次数，测量触发：+1；
C600690049 SN触发的异频PSCellChange请求次数：+1；
C600690050 SN触发的异频PSCellChange成功次数：+1；
C605030019 灵活可配的PLMN的PSCellChange请求次数：+1；
C605030020 灵活可配的PLMN的PSCellChange成功次数：+1；
*/
//STEPS_DESCRIBE_END

- path:ft/uc/ftest/nsa/src/ft-sn-pscell-change/FtSnPscellChange.cpp
- type:add_test
```cpp
TEST_F(FtSnPscellChangeWithoutSrb3, should_snpscellchange_fail_when_sn_mod_refuse_proc_cancel_assert_kpi)
{
    ToolSignalTraceAsserter::clear();
    setSignalTraceSwitch(1);
    setFineAdjustMnRefuseProcCancel(100);
    preSuccess();
    srcBpfUeContextModifyAction();
    SET_SCENCE("SnModifyRefuse");
    //STEP1_START
    x2PscellRRCRecfgRefuseAction();
    //STEP1_END
    REMOVE_SCENCE("SnModifyRefuse");

    FtSnPscellChangeAction::relPceSourceLinkAction();
    psCellChgNotifyAnrAction(false);
    releaseInStable();

    setSignalTraceSwitch(0);
    //STEP2_START
    EXPECT_KPIS(KPI_TDD(__TTrigIntraSNPScellChangeStat, byCounterType(IntraSnPSCellChange_Req).
                                                       tNrLteCellPairObject_gNBId(htonl(GNB_ID)).
                                                       tNrLteCellPairObject_cellId(htons(CELL_ID)).
                                                       tNrLteCellPairObject_eNodebId(htonl(ENB_ID)).
                                                       tNrLteCellPairObject_RelationgNBplmn(htonl(PLMN)).
                                                       tNrLteCellPairObject_eCellId(htons(ENB_CELL_ID)).
                                                       tNrLteCellPairObject_gNBplmn(htonl(GNBPLMN)).
                                                       tNrLteCellPairObject_gNBIdLength(htons(GNB_ID_LEN)))
                      ,KPI_TDD(__TTrigIntraSNPScellChangeStat, byCounterType(IntraSnPSCellChange_Succ).
                                                      tNrLteCellPairObject_gNBId(htonl(GNB_ID)).
                                                      tNrLteCellPairObject_cellId(htons(CELL_ID)).
                                                      tNrLteCellPairObject_eNodebId(htonl(ENB_ID)).
                                                      tNrLteCellPairObject_RelationgNBplmn(htonl(PLMN)).
                                                      tNrLteCellPairObject_eCellId(htons(ENB_CELL_ID)).
                                                      tNrLteCellPairObject_gNBplmn(htonl(GNBPLMN)).
                                                      tNrLteCellPairObject_gNBIdLength(htons(GNB_ID_LEN)))
                      ,KPI_TDD(__TrigCuPScellChangeStat, byCounterType(IntraSnPSCellChange_Req).
                                                         byDirection(PSCellChange_SgNBTrig).
                                                         tPlmnInfoCUObject_gNBplmn(htonl(GNBPLMN)).
                                                         tPlmnInfoCUObject_gNBId(htonl(GNB_ID)).
                                                         tPlmnInfoCUObject_gNBIdLength(htons(GNB_ID_LEN)).
                                                         tPlmnInfoCUObject_cellId(htons(CELL_ID)))
                      ,KPI_TDD(__TrigCuPScellChangeStat, byCounterType(IntraSnPSCellChange_Succ).
                                                         byDirection(PSCellChange_SgNBTrig).
                                                         tPlmnInfoCUObject_gNBplmn(htonl(GNBPLMN)).
                                                         tPlmnInfoCUObject_gNBId(htonl(GNB_ID)).
                                                         tPlmnInfoCUObject_gNBIdLength(htons(GNB_ID_LEN)).
                                                         tPlmnInfoCUObject_cellId(htons(CELL_ID)))
       );
    //STEP2_END
    setFineAdjustMnRefuseProcCancel(0);
}
```

