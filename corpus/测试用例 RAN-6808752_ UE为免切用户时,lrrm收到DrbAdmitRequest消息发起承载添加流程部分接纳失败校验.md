**RdcInfo**
- rdc_id:RAN-6808752
- repo_name:lrrm_lf
- gerrit_link:https://gerrit.zte.com.cn/#/c/22480375/1
- date:Jun 9, 2025 1:59 PM
## TestInfo
### 测试标题
UE为免切用户时,lrrm收到DrbAdmitRequest消息发起承载添加流程部分接纳失败校验
### 预置条件


### TC步骤
UE为免切用户,构造lrrm收到DrbAdmitRequest消息携带drb_to_be_setup_list;
其中携带的drb_to_be_setup_list包含的部分drb接纳失败;



### TC预期结果
和普通UE部分DRB接纳失败流程保持一致,lrrm回复Rsp消息携带result=success,填写successful_setup_drb_list和failed_setup_drb_list信元；
返回result=success,不输出reconfiguration_with_sync信元

### 预期结果
见单步预期结果

### 通过准则
见单步预期结果

## Tag Identification
### business_content_scence_tag
- 参数配置
- protobuf消息构造
- protobuf消息校验

### code_modify_scence_tag
- add_test_suit


**QUESTION**
作为5G通信协议开发专家和C++高级工程师，您需要根据**目标测试用例描述**，根据**代码生成要求**,生成**目标测试用例描述**测试代码

# 目标用例生成可能依赖代码
代码路径：ft/ft-test/ft-common/include/ft-common/domain/ue-admit/drb-admit/builder/DrbReqBuilder.h
```cpp
struct MultiAddDrbAdmitReqBuilderCommon : GenericMsgBuilder<UeMessage>
{
    MultiAddDrbAdmitReqBuilderCommon(const DrbReqInfo _drbReqInfo[], const U32 _drbNum, U32 deltaTimerlen = INVALID_U32, U32 drxLongCycle = INVALID_U32) : drbNum(_drbNum), deltaTimerlen(deltaTimerlen), drxLongCycle(drxLongCycle)
    {
        for(U32 idx = 0; idx < drbNum; idx++)
        {
            drbReqInfo[idx] = _drbReqInfo[idx];
        }
    }

    OVERRIDE(Status doBuild(UeMessage&, FakeSystem& system) const);

    DrbReqInfo drbReqInfo[8];
    U32 drbNum {0};
    U32 deltaTimerlen {INVALID_U32};
    U32 drxLongCycle {INVALID_U32};
};
```

# 目标用例生成可能依赖代码
代码路径：ft/ft-test/ft-common/src/ft-common/domain/ue-admit/drb-admit/builder/DrbReqBuilder.cpp
```cpp
Status MultiAddDrbAdmitReqBuilderCommon::doBuild(UeMessage& msg, FakeSystem& sys) const
{
    auto* drbReq = msg.mutable_drb_admit_request();
    drbReq->mutable_header()->set_phy_cell_l3_id(Identity::CELL_1);
    drbReq->mutable_header()->set_bpf_ue_id(drbReqInfo[0].gid);

    if(deltaTimerlen != INVALID_U32)
    {
        auto* deltaAckTimeout = drbReq->mutable_delta_vonr_uu_timer_len();
        RRM_USI_ASSERT_VALID_PTR_BOOL(deltaAckTimeout);
        deltaAckTimeout->set_value(deltaTimerlen);
    }

    if(drxLongCycle != INVALID_U32)
    {
        auto* drxCycle = drbReq->mutable_drx_cycle();
        drxCycle->set_long_drx_cycle(::lrrm_ue_if::DrxCycle_LongDrxCycle(drxLongCycle));

        drxCycle->set_drx_config_ind(true);
    }

    for(U32 idx = 0; idx < drbNum; idx++)
    {
        auto* drbToBeSetup = drbReq->add_drb_to_be_setup_list();
        drbToBeSetup->set_drb_identity(drbReqInfo[idx].drbId);

        auto *drbQosCap = drbToBeSetup->mutable_drb_qos_cap();
        RRM_USI_ASSERT_VALID_PTR_BOOL(drbQosCap);
        drbQosCap->set_qci(drbReqInfo[idx].qai);

        auto qci = drbReqInfo[idx].qai;
        gbrConfig(drbQosCap, qci);

        auto* arp = drbQosCap->mutable_arp();
        RRM_USI_ASSERT_VALID_PTR_BOOL(arp);
        arp->set_priority_level(drbReqInfo[idx].arp);
        arp->set_pre_emption_cap(::lrrm_ue_if::AllocRetentPriority_PreEmptionCap::AllocRetentPriority_PreEmptionCap_may_trigger_pre_emption);

        auto* nssai = drbToBeSetup->mutable_nssai();
        RRM_USI_ASSERT_VALID_PTR_BOOL(nssai);
        nssai->set_slice_service_type(drbReqInfo[idx].sliceType);
        auto* sliceDiff = nssai->mutable_slice_diff();
        RRM_USI_ASSERT_VALID_PTR_BOOL(sliceDiff);
        sliceDiff->set_value(drbReqInfo[idx].sliceDiff);

        auto* qosFlow = drbToBeSetup->add_qos_flow_info_list();
        RRM_USI_ASSERT_VALID_PTR_BOOL(qosFlow);
        auto* nonDynamic5qiDesc = qosFlow->mutable_non_dynamic_5qi_desc();
        RRM_USI_ASSERT_VALID_PTR_BOOL(nonDynamic5qiDesc);
        nonDynamic5qiDesc->set_nr_5qi(drbReqInfo[idx].qi5);
        auto* qosArp = qosFlow->mutable_alloc_retention_pri();
        RRM_USI_ASSERT_VALID_PTR_BOOL(qosArp);
        qosArp->set_priority_level(3);
    }

    return DCM_SUCCESS;
}
```

代码路径：ft/ft-test/ft-common/include/ft-common/domain/ue-admit/drb-admit/asserter/DrbRspAsserter.h
```cpp
struct DrbAdmitRspAsserterCommon : GenericMsgAsserter<UeMessage>
{
    DrbAdmitRspAsserterCommon(U32 ueId, Result result = Result::success, bool partDrbAdmit = false):
        ueId(ueId), result(result), partDrbAdmit(partDrbAdmit){}

    OVERRIDE(void doAssert(UeMessage& msg, FakeSystem& sys) const);

    U32 ueId;
    Result result;
    bool partDrbAdmit;
};
```

代码路径：ft/ft-test/ft-common/src/ft-common/domain/ue-admit/drb-admit/asserter/DrbRspAsserter.cpp
```cpp
void DrbAdmitRspAsserterCommon::doAssert(UeMessage& msg, FakeSystem& sys) const
{
    auto& drbRsp = msg.drb_admit_response();
    ASSERT_EQ(drbRsp.header().bpf_ue_id(), ueId);
    ASSERT_TRUE(drbRsp.result() == result);
    if (result != Result::success)
    {
        return;
    }
    ASSERT_TRUE(drbRsp.has_mac_cell_group_config());
    if(partDrbAdmit)
    {
        ASSERT_TRUE(drbRsp.successful_setup_drb_list_size() > 0);
        ASSERT_TRUE(drbRsp.failed_setup_drb_list_size() > 0);
    }
}
```

代码路径：ft/ft-test/ft-common/src/ft-common/test/handoverAdmit/FtHandoverAdmitTest.cpp
```cpp
void ueDrbAdd(const DrbReqInfo drbReqInfo[], U32 drbNum, const Result result = Result::success, bool partDrbAdmit = false)
{
	auto instKey = getInstKey();
	servRrm <---- fakeUc (UCS_LRRM_UE_SESSION, instKey,
			ID_VALUE(UeMessage::kDrbAdmitRequest), MultiAddDrbAdmitReqBuilderCommon(drbReqInfo, drbNum));
	servRrm ----> fakeUc (UCS_LRRM_UE_SESSION, instKey,
			ID_VALUE(UeMessage::kDrbAdmitResponse), DrbAdmitRspAsserterCommon(drbReqInfo[0].gid, result, partDrbAdmit));
	servRrm <---- fakeUc (UCS_LRRM_UE_SESSION, instKey,
			ID_VALUE(UeMessage::kDrbAdmitAcknowledge), DrbAdmitAckBuilderCommon(drbReqInfo[0].gid));
}
```


# 相似代码信息
## 相似文本测试用例描述
### 测试标题
测试用例 RAN-6808753: UE为免切用户时,lrrm收到HandoverAdmitRequest消息发起免切换流程承载部分接纳失败校验
### 预置条件

### TC步骤
UE为免切用户,构造lrrm收到HandoverAdmitRequest消息携带SrcUeContextInfo和non_ho_src_crnti信元;
其中携带的drb_to_be_setup_list包含的部分drb接纳失败;

### TC预期结果
和普通UE部分DRB接纳失败流程保持一致,lrrm回复Rsp消息填写successful_setup_drb_list和failed_setup_drb_list信元；
返回result=success,不输出reconfiguration_with_sync信元

### 预期结果
见单步预期结果

### 通过准则
见单步预期结果


## 相似用例FT代码
代码路径：ft/ft-test/ft-common/src/ft-common/test/handoverAdmit/FtHandoverAdmitTest.cpp
```cpp
TEST_F(FtHandoverAdmitHoFreeCase, RAN_5924225_5924219_6808753_ho_free_handover_admit_test)
{
    servRrmMain <---- fakeMo (S_BCS_MO_NOTIFY_SESSION, "#.DrbNumAdmission.drbNumACThrd"
                    , NS_RRM(MO_CHANGE_NOTIFY), DrbNumAcThrdMoBuilder(2, 0));

    U32 ueId = Identity::UE_GID1;
    U32 crnti = 22538;

    bool checkInitalBwp = true;
    U32 dmrsScramblingId = 22666;
    U32 scramblingId0 = 128;
    U32 scramblingId1 = 22666;

#ifdef LRRM_SUB1G_CODE
    checkInitalBwp = false;
    dmrsScramblingId = 128;
    scramblingId0 = 128;
    scramblingId1 = 22666;
#endif

    HandoverHoFreeRspAsserterCommon handoverRspAsserter{ueId, dmrsScramblingId, scramblingId0, scramblingId1, checkInitalBwp, Result::success, true};
    handoverAdmit(ueId, crnti, handoverRspAsserter);
}
```

## 相似用例代码依赖

代码路径：ft/ft-test/ft-common/include/ft-common/domain/ue-admit/handover-admit/asserter/HandoverRspAsserter.h
```cpp
struct HandoverHoFreeRspAsserterCommon : GenericMsgAsserter<UeMessage>
{
    HandoverHoFreeRspAsserterCommon(U32 ueId, U32 dmrsScramblingId, U32 scramblingId0, U32 scramblingId1,  bool checkInitalBwp, Result result = Result::success, bool partDrbAdmit = false)
    : ueId(ueId)
    , dmrsScramblingId(dmrsScramblingId)
    , scramblingId0(scramblingId0)
    , scramblingId1(scramblingId1)
    , checkInitalBwp(checkInitalBwp)
    , result(result)
    , partDrbAdmit(partDrbAdmit)
    {}

    OVERRIDE(void doAssert(UeMessage& msg, FakeSystem& sys) const);

    HandoverHoFreeRspAsserterCommon& setDuration(U32 _duration)
    {
        duration = _duration;
        return *this;
    }

private:
    U32 ueId;
    U32 dmrsScramblingId;
    U32 scramblingId0;
    U32 scramblingId1;
    bool checkInitalBwp;
    Result result;
    bool partDrbAdmit;
    U32 duration {2};

};
```

代码路径：ft/ft-test/ft-common/src/ft-common/domain/ue-admit/handover-admit/asserter/HandoverRspAsserter.cpp
```cpp
void HandoverHoFreeRspAsserterCommon::doAssert(UeMessage& msg, FakeSystem& sys) const
{
    USING_NS_PROTO_UE
     const auto& rsp = msg.handover_admit_response();
     ASSERT_EQ(rsp.has_header(), true);
     ASSERT_EQ(rsp.header().bpf_ue_id(), ueId);
     ASSERT_EQ(rsp.result(), result);

     if(result != Result::success)
     {
         return;
     }

     if(partDrbAdmit)
     {
        ASSERT_TRUE(rsp.successful_setup_drb_list_size() >  0);
        ASSERT_TRUE(rsp.failed_setup_drb_list_size() > 0);
     }
     ASSERT_FALSE(rsp.has_reconfiguration_with_sync());

    const auto& phyCellGroupCfg = rsp.physical_cellgroup_config();

    ASSERT_FALSE(phyCellGroupCfg.has_dcp_config());
    ASSERT_FALSE(phyCellGroupCfg.has_tpc_pusch_rnti());

    ASSERT_TRUE(rsp.has_serving_cell_config_dedicated());
    const auto& serCell = rsp.serving_cell_config_dedicated();

    if(checkInitalBwp)
    {
        const auto& initDlBwp = serCell.inital_downlink_bwp();
        const auto& pdcchCfg = initDlBwp.pdcch_config();
        const auto& pdcchSetUp = pdcchCfg.setup();
        U32 coresetNum = pdcchSetUp.control_resource_set_add_mod_list_size();
        ASSERT_TRUE(coresetNum > 0);
        for(U32 idx = 0; idx < coresetNum; idx++)
        {
            const auto& coreset = pdcchSetUp.control_resource_set_add_mod_list(idx);
            U32 dmrsScrambId = coreset.pdcch_dmrs_scrambling_id().value();
            EXPECT_EQ(dmrsScrambId, dmrsScramblingId);
            ASSERT_TRUE(coreset.duration() == duration);
            auto& cceRegType = coreset.cce_reg_mapping_type();
            EXPECT_TRUE(cceRegType.has_non_interleave());
        }

        const auto& pdschCfgSetup = initDlBwp.pdsch_config().setup();
        EXPECT_TRUE(pdschCfgSetup.has_dmrs_dl_for_pdsch_mapping_type_a());
        auto& dmrs = pdschCfgSetup.dmrs_dl_for_pdsch_mapping_type_a();
        EXPECT_TRUE(dmrs.has_setup());
        auto& dmrsSetup = dmrs.setup();
        EXPECT_EQ(dmrsSetup.srcambling_id0().value(), scramblingId0);
        EXPECT_EQ(dmrsSetup.scrambling_id1().value(), scramblingId1);

        return;
     }

     ASSERT_TRUE((U32)serCell.downlink_bwp_add_mod_list_size() ==  1);
     for(U32 idx = 0; idx < (U32)serCell.downlink_bwp_add_mod_list_size(); idx++)
     {
         const auto& downLink = serCell.downlink_bwp_add_mod_list(idx);
         const auto& downlinkDedicated = downLink.bwp_downlink_dedicated();
         const auto& pdcchCfg = downlinkDedicated.pdcch_config();
         const auto& pdcchSetUp = pdcchCfg.setup();

         U32 coresetNum = pdcchSetUp.control_resource_set_add_mod_list_size();
         ASSERT_TRUE(coresetNum > 0);
         for(U32 idx = 0; idx < coresetNum; idx++)
         {
             const auto& coreset = pdcchSetUp.control_resource_set_add_mod_list(idx);
             U32 dmrsScrambId = coreset.pdcch_dmrs_scrambling_id().value();
             EXPECT_EQ(dmrsScrambId, dmrsScramblingId);
             ASSERT_TRUE(coreset.duration() == duration);
             auto& cceRegType = coreset.cce_reg_mapping_type();
             EXPECT_TRUE(cceRegType.has_non_interleave());
         }

         const auto& pdschCfg = downlinkDedicated.pdsch_config();
         const auto& pdschSetup = pdschCfg.setup();
         ASSERT_TRUE(pdschSetup.has_dmrs_dl_for_pdsch_mapping_type_a());
         const auto& dmrsDownlinkCfg = pdschSetup.dmrs_dl_for_pdsch_mapping_type_a();
         const auto& dmrsDownlinkSetup = dmrsDownlinkCfg.setup();

         U32 srcambling_id0 = dmrsDownlinkSetup.srcambling_id0().value();
         U32 srcambling_id1 = dmrsDownlinkSetup.scrambling_id1().value();
         EXPECT_EQ(srcambling_id0, scramblingId0);
         EXPECT_EQ(srcambling_id1, scramblingId1);

     }
}
```

代码路径：ft/ft-test/ft-common/src/ft-common/test/handoverAdmit/FtHandoverAdmitTest.cpp
```cpp
void handoverAdmit(U32 ueId, U32 crnti, const GenericMsgAsserter<UeMessage>& handoverRspAsserter, bool isIntraGnb = false)
{
	auto instKey = getInstKey();
	servRrm <---- fakeUc (UCS_LRRM_UE_SESSION, instKey, ID_VALUE(UeMessage::kHandoverAdmitRequest), HandoverAdmitHoFreeBuilderCommon(ueId, crnti, INVALID_U8, isIntraGnb));
	servRrm ----> fakeUc (UCS_LRRM_UE_SESSION, instKey, ID_VALUE(UeMessage::kHandoverAdmitResponse), handoverRspAsserter);
	servRrm <---- fakeUc (UCS_LRRM_UE_SESSION, instKey, ID_VALUE(UeMessage::kHandoverAdmitAcknowledge), HandoverAdmitAckBuilderCommon(ueId));
}
```

# 代码生成要求
请根据提供的相关信息和相似信息，遵循以下原则进行测试代码生成：
1. 完整性原则
  - 必须输出完整的函数实现，包括函数声明、函数体和结束花括号
  - 不允许使用省略号或只显示修改部分
  - 保持函数的完整上下文

2. 参数化设计原则
  - 新增逻辑应通过函数参数控制
  - 避免硬编码固定值
  - 保持函数接口的灵活性

4. 测试用例适配
  - 确保测试用例覆盖新增功能
  - 维护测试用例的正确性
  - 补充必要的测试场景

5. 代码修改文件生成方法
  -对于测试套和测试代码，按照相似用例文件名输出
  -对于action代码及函数体及修改，按照原有代码文件名输出

# 各类用例内容代码修改方法示例
## 基础数据建立小区
- 基础数据建立小区主要是在测试用例的开头预先设置小区建立所需要的数据，以达到模拟测试场景的效果
- 基础数据建立小区的相关逻辑主要体现在测试套的static void SetUpTestCase()或    void doSetUp()
接口中，这两个接口本质是调用了小区建立的方法：.setup()，目的是使用合适的参数来建立符合诉求的小区场景
### 例子
FtIntraCellChgHoFreeCase61::doSetUp
```cpp
struct FtHandoverAdmitHoFreeCase : FtHoFreeFixture
{
    void doSetUp()
    {
        CellSetupRelease release;
        release.release();

        json modata = json::parse("{\"result\":{\"NRRadioInfrastructure.NRCarrierGroup\":[{\"moId\":\"1\",\"nrCarrierGroupId\":1,"
                "\"refNRCarrier\":[\"NRRadioInfrastructure=1,NRCarrier=1\"],\"testState\":\"1\"}],\"NRRadioInfrastructure.rntiAllocStrategy\":[\"0\"],\"NRRadioInfrastructure.rntiAllocSegmentIndex\":0}}");
        addHoFreeCfg(128, modata);
        release.sendNRRadioInfraToRrmMain(modata);

        HoFreeMoRspBuilder hoFreeMoRspBuilder("1", 128, "1", 2);
        release.setup(Identity::GNB_100,Identity::CELL_1, hoFreeMoRspBuilder.build());
    }
private:
    autoVariableResetter(stubRlc, 0);
#ifdef LRRM_SUB1G_CODE
    autoVariableResetter(ftStubNoAllocPucchHoFreeRes, 1);
#endif
};
```


## 业务流程消息校验
- 模块在业务流程中向外部模块发送消息，用例需要校验该消息的内容是否符合预期
- 消息的交互都封装在用例的Action方法中，其中使用语法糖**---->**表示向外部发送消息，包含发送该消息使用的Session，InstKey，EventId和负责校验的Asserter类构造
- Asserter类负责实现具体的校验细节，其可能通过组装的方式，将消息的不同内容封装成不同的Asserter类，然后使用组装的方式来实现用例的具体校验要求
### 例子
HandoverHoFreeRspAsserterCommon对handover_admit_response的消息进行校验
```cpp
struct HandoverHoFreeRspAsserterCommon : GenericMsgAsserter<UeMessage>
{
    HandoverHoFreeRspAsserterCommon(U32 ueId, U32 dmrsScramblingId, U32 scramblingId0, U32 scramblingId1,  bool checkInitalBwp, Result result = Result::success, bool partDrbAdmit = false)
    : ueId(ueId)
    , dmrsScramblingId(dmrsScramblingId)
    , scramblingId0(scramblingId0)
    , scramblingId1(scramblingId1)
    , checkInitalBwp(checkInitalBwp)
    , result(result)
    , partDrbAdmit(partDrbAdmit)
    {}

    OVERRIDE(void doAssert(UeMessage& msg, FakeSystem& sys) const);

    HandoverHoFreeRspAsserterCommon& setDuration(U32 _duration)
    {
        duration = _duration;
        return *this;
    }

private:
    U32 ueId;
    U32 dmrsScramblingId;
    U32 scramblingId0;
    U32 scramblingId1;
    bool checkInitalBwp;
    Result result;
    bool partDrbAdmit;
    U32 duration {2};

};

void HandoverHoFreeRspAsserterCommon::doAssert(UeMessage& msg, FakeSystem& sys) const
{
    USING_NS_PROTO_UE
     const auto& rsp = msg.handover_admit_response();
     ASSERT_EQ(rsp.has_header(), true);
     ASSERT_EQ(rsp.header().bpf_ue_id(), ueId);
     ASSERT_EQ(rsp.result(), result);

     if(result != Result::success)
     {
         return;
     }

     if(partDrbAdmit)
     {
        ASSERT_TRUE(rsp.successful_setup_drb_list_size() >  0);
        ASSERT_TRUE(rsp.failed_setup_drb_list_size() > 0);
     }
     ASSERT_FALSE(rsp.has_reconfiguration_with_sync());

    const auto& phyCellGroupCfg = rsp.physical_cellgroup_config();

    ASSERT_FALSE(phyCellGroupCfg.has_dcp_config());
    ASSERT_FALSE(phyCellGroupCfg.has_tpc_pusch_rnti());

    ASSERT_TRUE(rsp.has_serving_cell_config_dedicated());
    const auto& serCell = rsp.serving_cell_config_dedicated();

    if(checkInitalBwp)
    {
        const auto& initDlBwp = serCell.inital_downlink_bwp();
        const auto& pdcchCfg = initDlBwp.pdcch_config();
        const auto& pdcchSetUp = pdcchCfg.setup();
        U32 coresetNum = pdcchSetUp.control_resource_set_add_mod_list_size();
        ASSERT_TRUE(coresetNum > 0);
        for(U32 idx = 0; idx < coresetNum; idx++)
        {
            const auto& coreset = pdcchSetUp.control_resource_set_add_mod_list(idx);
            U32 dmrsScrambId = coreset.pdcch_dmrs_scrambling_id().value();
            EXPECT_EQ(dmrsScrambId, dmrsScramblingId);
            ASSERT_TRUE(coreset.duration() == duration);
            auto& cceRegType = coreset.cce_reg_mapping_type();
            EXPECT_TRUE(cceRegType.has_non_interleave());
        }

        const auto& pdschCfgSetup = initDlBwp.pdsch_config().setup();
        EXPECT_TRUE(pdschCfgSetup.has_dmrs_dl_for_pdsch_mapping_type_a());
        auto& dmrs = pdschCfgSetup.dmrs_dl_for_pdsch_mapping_type_a();
        EXPECT_TRUE(dmrs.has_setup());
        auto& dmrsSetup = dmrs.setup();
        EXPECT_EQ(dmrsSetup.srcambling_id0().value(), scramblingId0);
        EXPECT_EQ(dmrsSetup.scrambling_id1().value(), scramblingId1);

        return;
     }

     ASSERT_TRUE((U32)serCell.downlink_bwp_add_mod_list_size() ==  1);
     for(U32 idx = 0; idx < (U32)serCell.downlink_bwp_add_mod_list_size(); idx++)
     {
         const auto& downLink = serCell.downlink_bwp_add_mod_list(idx);
         const auto& downlinkDedicated = downLink.bwp_downlink_dedicated();
         const auto& pdcchCfg = downlinkDedicated.pdcch_config();
         const auto& pdcchSetUp = pdcchCfg.setup();

         U32 coresetNum = pdcchSetUp.control_resource_set_add_mod_list_size();
         ASSERT_TRUE(coresetNum > 0);
         for(U32 idx = 0; idx < coresetNum; idx++)
         {
             const auto& coreset = pdcchSetUp.control_resource_set_add_mod_list(idx);
             U32 dmrsScrambId = coreset.pdcch_dmrs_scrambling_id().value();
             EXPECT_EQ(dmrsScrambId, dmrsScramblingId);
             ASSERT_TRUE(coreset.duration() == duration);
             auto& cceRegType = coreset.cce_reg_mapping_type();
             EXPECT_TRUE(cceRegType.has_non_interleave());
         }

         const auto& pdschCfg = downlinkDedicated.pdsch_config();
         const auto& pdschSetup = pdschCfg.setup();
         ASSERT_TRUE(pdschSetup.has_dmrs_dl_for_pdsch_mapping_type_a());
         const auto& dmrsDownlinkCfg = pdschSetup.dmrs_dl_for_pdsch_mapping_type_a();
         const auto& dmrsDownlinkSetup = dmrsDownlinkCfg.setup();

         U32 srcambling_id0 = dmrsDownlinkSetup.srcambling_id0().value();
         U32 srcambling_id1 = dmrsDownlinkSetup.scrambling_id1().value();
         EXPECT_EQ(srcambling_id0, scramblingId0);
         EXPECT_EQ(srcambling_id1, scramblingId1);

     }
}
// 用法
servRrm ----> fakeUc (UCS_LRRM_UE_SESSION, instKey, ID_VALUE(UeMessage::kHandoverAdmitResponse), HandoverHoFreeRspAsserterCommon);
```

## 业务流程消息构造
- 模块在业务流程收到外部模块发送的消息，再FT中进行构造后模拟收到外部模块发送的消息，用例需要构造符合预期的消息内容
- 消息的交互都封装在用例的Action方法中，其中使用语法糖**<----**表示收到外部发送消息，包含发送该消息使用的Session，InstKey，EventId和负责构造的Builder类实现
- Builder类负责实现具体的构造细节，其可能通过组装的方式，将消息的不同内容封装成不同的Builder类，然后使用组装的方式来实现用例的具体构造要求，在该类中使用doBuild接口
### 例子
HandoverAdmitHoFreeBuilderCommon 声明、定义、用法
```cpp
struct HandoverAdmitHoFreeBuilderCommon : GenericMsgBuilder<UeMessage>
{
    HandoverAdmitHoFreeBuilderCommon(Gid gid, U32 crnti = 25000, U8 multiCsiId = INVALID_U8, bool isIntraGnb = false, bool hasCandCell = false)
    : gid(gid)
    , crnti(crnti)
    , multiCsiId(multiCsiId)
    , isIntraGnb(isIntraGnb)
    , hasCandCell(hasCandCell)
    {}

    OVERRIDE(Status doBuild(UeMessage&, FakeSystem& system) const);

private:
    Gid gid;
    U32 crnti{25000};
    U8 multiCsiId {INVALID_U8};
    bool isIntraGnb {false};
    bool hasCandCell {false};

};

Status HandoverAdmitHoFreeBuilderCommon::doBuild(UeMessage& msg, FakeSystem& sys) const
{
    auto* req = msg.mutable_handover_admit_request();
    req->mutable_header()->set_phy_cell_l3_id(Identity::CELL_1);
    req->mutable_header()->set_bpf_ue_id(gid);
    if(crnti != INVALID_U32)
    {
        req->mutable_non_ho_src_crnti()->set_value(crnti);
    }

    if(multiCsiId != INVALID_U8)
    {
        auto* srcUeInfo = req->mutable_src_ue_context_info();
        RRM_USI_ASSERT_VALID_PTR_BOOL(srcUeInfo);
        auto* bwpUplink = srcUeInfo->add_bwp_uplink_list();
        bwpUplink->set_bwp_id(1);
        auto* bwpUplinkDec = bwpUplink->mutable_bwp_uplink_dedicated();
        RRM_USI_ASSERT_VALID_PTR_BOOL(bwpUplinkDec);
        auto* pucchConfig = bwpUplinkDec->mutable_pucch_config();
        RRM_USI_ASSERT_VALID_PTR_BOOL(pucchConfig);
        auto* pucchConfigSetup = pucchConfig->mutable_setup();
        RRM_USI_ASSERT_VALID_PTR_BOOL(pucchConfigSetup);
        pucchConfigSetup->add_multi_csi_pucch_resource_list(multiCsiId);
    }

    req->mutable_header()->set_time_stamp(getCurrentSystTimeUsU64());

    auto* ncgi = req->mutable_ncgi();
    ncgi->set_gnb_id(Identity::GNB_100);
    ncgi->set_l3_cell_id(Identity::CELL_1);
    ncgi->set_gnb_id_length(Identity::GNB_ID_LEN);

    auto* ncgiPlmn = ncgi->mutable_plmn();
    BYTE mcc[]={4,6,0};
    BYTE mnc[]={1,1};
    ncgiPlmn->set_mcc(mcc,3);
    ncgiPlmn->set_mnc(mnc,2);

    auto* serPlmn = req->mutable_serving_plmn();
    serPlmn->set_mcc(mcc,3);
    serPlmn->set_mnc(mnc,2);

    if( isIntraGnb )
    {
        auto* srcncgi = req->mutable_src_ue_context_info()->mutable_ncgi();
        srcncgi->set_gnb_id(Identity::GNB_100);
        srcncgi->set_l3_cell_id(Identity::CELL_1);
        srcncgi->set_gnb_id_length(Identity::GNB_ID_LEN);

        auto* ncgiPlmn1 = srcncgi->mutable_plmn();
        BYTE mcc1[]={4,6,0};
        BYTE mnc1[]={1,1};
        ncgiPlmn1->set_mcc(mcc1,3);
        ncgiPlmn1->set_mnc(mnc1,2);
    }

    auto srb1 = req->add_srb_to_be_setup_list();
    srb1->set_srb_identity(1);
    auto srb2 = req->add_srb_to_be_setup_list();
    srb2->set_srb_identity(2);

    for(U32 idx = 1; idx < 3; idx++)
    {
        auto *drbToBeSetup = req->add_drb_to_be_setup_list();
        RRM_USI_ASSERT_VALID_PTR_BOOL(drbToBeSetup);

        drbToBeSetup->set_drb_identity(idx);

        auto *drbQosCap = drbToBeSetup->mutable_drb_qos_cap();
        RRM_USI_ASSERT_VALID_PTR_BOOL(drbQosCap);
        drbQosCap->set_qci(9);

        auto* arp = drbQosCap->mutable_arp();
        RRM_USI_ASSERT_VALID_PTR_BOOL(arp);
        arp->set_priority_level(3);

        auto* nssai = drbToBeSetup->mutable_nssai();
        RRM_USI_ASSERT_VALID_PTR_BOOL(nssai);
        nssai->set_slice_service_type(1);
        auto* sliceDiff = nssai->mutable_slice_diff();
        RRM_USI_ASSERT_VALID_PTR_BOOL(sliceDiff);
        sliceDiff->set_value(111111);

        auto* qosFlow = drbToBeSetup->add_qos_flow_info_list();
        RRM_USI_ASSERT_VALID_PTR_BOOL(qosFlow);
        auto* nonDynamic5qiDesc = qosFlow->mutable_non_dynamic_5qi_desc();
        RRM_USI_ASSERT_VALID_PTR_BOOL(nonDynamic5qiDesc);
        nonDynamic5qiDesc->set_nr_5qi(9);
        auto* qosArp = qosFlow->mutable_alloc_retention_pri();
        RRM_USI_ASSERT_VALID_PTR_BOOL(qosArp);
        qosArp->set_priority_level(3);

        auto* rlcMode = drbToBeSetup->mutable_rlc_mode();
        RRM_USI_ASSERT_VALID_PTR_BOOL(rlcMode);
        rlcMode->set_value( lrrm_ue_if::DrbToBeSetup_RlcMode_Type_am);
    }

    if(hasCandCell)
    {
        auto *candidate_cell_info = req->mutable_candidate_cell_info();
        RRM_USI_ASSERT_VALID_PTR_BOOL(candidate_cell_info);
        auto * rs_idx_result = candidate_cell_info->mutable_rs_index_result();
        RRM_USI_ASSERT_VALID_PTR_BOOL(rs_idx_result);
        auto * result_per_ssb_index = rs_idx_result->add_results_per_ssb_index_list();
        RRM_USI_ASSERT_VALID_PTR_BOOL(result_per_ssb_index);
        result_per_ssb_index->set_ssb_index(0);
    }

    return DCM_SUCCESS;
}

//用法
servRrm <---- fakeUc (UCS_LRRM_UE_SESSION, instKey, ID_VALUE(UeMessage::kHandoverAdmitRequest), HandoverAdmitHoFreeBuilderCommon(ueId, crnti, INVALID_U8, isIntraGnb));
```


# 建议的实现步骤

1. 先了解目标测试用例的各个测试点,学习每类测试点的修改方法
2. 阅读相似用例和给出的代码片段，了解各个代码之间的调用关系和业务语义
3. 逐条分析每个测试点，分析那些代码片段需要修改或新增,然和结合修改方法进行修改
4. 将**相似用例FT代码**完全作为你的基础用例,在该用例上进行修改和新增,你仅需要在基础用例上实现目标测试用例描述即可

# 输出格式

- 测试用例的命名必须按照TEST_F(测试套名称, should_{测试内容}_when_{预置条件}_then_{测试步骤})进行命名，{测试步骤}、{预置条件}和{测试内容}采用小写驼峰式命名并且禁止出现中文。

例子：TEST_F(InitOrcheTest, should_attach_success_when_uecap_switch_is_open)

新增或修改的测试代码

- path:路径信息
- type:变更类型 add_test|modify_code|add_code
- step_describe:修改涉及步骤标识，例如["STEP1"]
```cpp
```
例如：
- path:ft/uc_new/ftest/src/ft/feature/ft-suspend/FtSuspendAction.cpp
- type:modify_code
- step_describe:["STEP1"]
```cpp
完整的修改代码函数
```

# 输出要求
- 输出无关的代码不要做省略处理,你需要输出完整的函数片段

# 目标测试用例描述
## 基础数据建立小区
- STEP1: 预置条件：

## 业务流程消息校验
- STEP2:
UE为免切用户,构造lrrm收到DrbAdmitRequest消息携带drb_to_be_setup_list;
其中携带的drb_to_be_setup_list包含的部分drb接纳失败;

和普通UE部分DRB接纳失败流程保持一致,lrrm回复Rsp消息携带result=success,填写successful_setup_drb_list和failed_setup_drb_list信元；
返回result=success,不输出reconfiguration_with_sync信元

**ANSWER**

//STEPS_DESCRIBE_START
- STEP1: 预置条件：

- STEP2:
UE为免切用户,构造lrrm收到DrbAdmitRequest消息携带drb_to_be_setup_list;
其中携带的drb_to_be_setup_list包含的部分drb接纳失败;

和普通UE部分DRB接纳失败流程保持一致,lrrm回复Rsp消息携带result=success,填写successful_setup_drb_list和failed_setup_drb_list信元；
返回result=success,不输出reconfiguration_with_sync信元
//STEPS_DESCRIBE_END

- path:ft/ft-test/ft-common/src/ft-common/test/handoverAdmit/FtHandoverAdmitTest.cpp
- type:add_code
```cpp
//STEP1_START
struct FtHandoverAdmitHoFreeCase : FtHoFreeFixture
{
    void doSetUp()
    {
        CellSetupRelease release;
        release.release();

        json modata = json::parse("{\"result\":{\"NRRadioInfrastructure.NRCarrierGroup\":[{\"moId\":\"1\",\"nrCarrierGroupId\":1,"
                "\"refNRCarrier\":[\"NRRadioInfrastructure=1,NRCarrier=1\"],\"testState\":\"1\"}],\"NRRadioInfrastructure.rntiAllocStrategy\":[\"0\"],\"NRRadioInfrastructure.rntiAllocSegmentIndex\":0}}");
        addHoFreeCfg(128, modata);
        release.sendNRRadioInfraToRrmMain(modata);

        HoFreeMoRspBuilder hoFreeMoRspBuilder("1", 128, "1", 2);
        release.setup(Identity::GNB_100,Identity::CELL_1, hoFreeMoRspBuilder.build());
    }
private:
    autoVariableResetter(stubRlc, 0);
#ifdef LRRM_SUB1G_CODE
    autoVariableResetter(ftStubNoAllocPucchHoFreeRes, 1);
#endif
};
//STEP1_END
```

- path:ft/ft-test/ft-common/src/ft-common/test/prm/dl/FtPdcchConfigTestComm.cpp
- type:add_test
```cpp
//STEP2_START
{
    U32 ueId = Identity::UE_GID1;
    U32 crnti = 22538;

    bool checkInitalBwp = true;
    U32 dmrsScramblingId = 22666;
    U32 scramblingId0 = 128;
    U32 scramblingId1 = 22666;

#ifdef LRRM_SUB1G_CODE
    checkInitalBwp = false;
    dmrsScramblingId = 128;
    scramblingId0 = 128;
    scramblingId1 = 22666;
#endif

    HandoverHoFreeRspAsserterCommon handoverRspAsserter{ueId, dmrsScramblingId, scramblingId0, scramblingId1, checkInitalBwp, Result::success};
    handoverAdmit(ueId, crnti, handoverRspAsserter);

    servRrmMain <---- fakeMo (S_BCS_MO_NOTIFY_SESSION, "#.DrbNumAdmission.drbNumACThrd"
                    , NS_RRM(MO_CHANGE_NOTIFY), DrbNumAcThrdMoBuilder(4, 0));
   DrbReqInfo drbReqInfo[2];
   drbReqInfo[0].gid       = ueId;
   drbReqInfo[0].qai       = Identity::DRB_QCI_8;
   drbReqInfo[0].qi5       = Identity::DRB_QCI_9;
   drbReqInfo[0].drbId     = Identity::DRB_IDENTITY_4;

   drbReqInfo[1].qai       = Identity::DRB_QCI_8;
   drbReqInfo[1].qi5       = Identity::DRB_QCI_9;
   drbReqInfo[1].drbId     = Identity::DRB_IDENTITY_5;

    ueDrbAdd(drbReqInfo, 2, Result::success, true);
}
//STEP2_END
```

