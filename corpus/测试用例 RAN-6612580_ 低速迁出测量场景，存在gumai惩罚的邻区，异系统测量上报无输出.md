**RdcInfo**
- rdc_id:RAN-6612580
- repo_name:huc_alpha
- gerrit_link: https://gerrit.zte.com.cn/#/c/21847693/
- final_test:huc,FtSpeedMigRptHandle,ran_6612580_6651364_ho_punish
- compile_script:ft/drm/lf/build.sh incr cjson
- compile_command_json_path:ft/drm/lf/compile_commands
- date:2025-04-10
## TestInfo
### 测试标题
测试用例 RAN-6612580: 低速迁出测量场景，存在gumai惩罚的邻区，异系统测量上报无输出
### 预置条件
【切换准备失败惩罚开关(hoPrepareFailPunishSwch)】：打开

【功能频点以及邻区信息】：异系统f1:cell3和cell4

【功能执行方式】：切换+重定向

【reserved731】：打开

【HOPrepareFailPunish信息】：cell2惩罚定时器正在运行

【保存的PunishInfoId信息(小区、plmn、惩罚原因、guami信息)】：cell2（NR小区）、plmn1、Unknown target ID、guami1
cell3（LTE小区）、plmn1、Unknown target ID、guami2
cell2（NR小区）、plmn1、Handover failure in target 5GC/ NG-RAN node or target system、guami1
cell3（LTE小区）、plmn1、Handover failure in target 5GC/ NG-RAN node or target system、guami2

【测试场景】：UE接入cell1，选择plmn1和guami1，低速迁出异系统测量上报cell3
### TC步骤
无输出
### TC预期结果
无输出
### 预期结果
低速迁出测量场景，存在gumai惩罚的邻区，异系统测量上报无输出
### 验收准则
低速迁出测量场景，存在gumai惩罚的邻区，异系统测量上报无输出


## Tag Identification
### business_content_scence_tag
- 参数配置
- protobuf消息构造
- protobuf消息校验

### code_modify_scence_tag
- add_test_function

**QUESTION**
作为5G通信协议开发专家和C++高级工程师，您需要根据**目标测试用例描述**，根据**代码生成要求**,生成**目标测试用例描述**测试代码

# 目标用例生成可能依赖代码
代码路径: ft/drm/lf/src/test/mrRptDecision/speepMigMeas/FtSpeedMigRptHandle.cpp
```cpp
struct FtSpeedMigRptHandle : FtLmFixture
{
#define PRIORITY_255 (255)
#define PRIORITY_0 (0)
#define ANR_CLOSE (false)
#define ANR_OPEN (true)
    virtual void setUp()
    {
        ROLE(FtLm).init();

        ROLE(FtLm).activeMoChg(R"({
                                "path":"@:GNBCUCPFunction[moId='1'].NRCellCU[cellLocalId=1].PublicPrivateNetworkMigCtrl[moId='default']",
                                "serialNumber":12,
                                "value":{
                                           "lowSpeedNCellMigSwch":"1",
                                           "lowSpeedInterFNCellMigMethod":"0"
                                        }
                            })");
        moker.mockAPI("getCurrentSystTimeUsU64", getCurrentSystTimeUsU64).
                   stubs()
                  .with(any())
                  .will(returnValue(U64(85000000)));
    }
    void tearDown()
    {
        kpiReportHandle();
        ROLE(FtLm).abort();
        moker.~Mocker();
    }

    void setRedCapUe(U32 cpfUeId, U8 supportRxType = INVALID_U8)
    {
        json ueNrCapInfo;
        ueNrCapInfo["support_of_redcap_r17"] = true;
        ueNrCapInfo["eventb_meas_and_report"] = false;
        ueNrCapInfo["supported_band_list_nr"][0]["band_nr"] = 77;
        ueNrCapInfo["supported_band_list_nr"][0]["supported_rate_matching_LTE"]["value"]= false;
        auto& nrFeatureSet = ueNrCapInfo["feature_set_combination_list"][0]["feature_sets_per_band_list"][0]["feature_set_list"][0]["nr_feature_set"];
        nrFeatureSet["feature_set_downlink_id"] = 1;
        nrFeatureSet["feature_set_uplink_id"] = 1;

       FeatureSetsNRCap featureSets_option;
        U32 mimoLayersDl = 1;
        if (supportRxType == 1)
        {
            mimoLayersDl = INVALID_U32;
        }
        else if (supportRxType == 2)
        {
            mimoLayersDl = 0;
        }
        featureSets_option.insert(FeatureSetDownlink({1}, 0)).insert(FeatureSetDownlinkPerCC("fr1", 10, 0, true, mimoLayersDl));
        ueNrCapInfo["feature_sets"] = featureSets_option.data;
        modUeNrCapInfo(cpfUeId, ueNrCapInfo);
    }

    void configMeasObject(U32 freqPriority)
    {
        auto nrFreq = R"([{"mo_id":"1", "ssb_frequency_arfcn":65000, "freq_band_indicator_nr":[77]},
                          {"mo_id":"2", "ssb_frequency_arfcn":66000, "freq_band_indicator_nr":[77]},
                          {"mo_id":"3", "ssb_frequency_arfcn":67000, "freq_band_indicator_nr":[77]}])";
        auto eutranFreq = R"([{"mo_id":"1", "frequency_arfcn":3450, "freq_band_list":[43]}])";
        servLm <---- fakeCms (S_CMS_CELL_INFO_NOTIFY, cmsKey,
                cms_common::Message::kCellInfoModifyIndication,
                getCellModify(nrFreq, eutranFreq,cms_common::NRCellScene::HighWay).c_str());
        servLm <---- fakeCms (S_CMS_CELL_INFO_NOTIFY, cmsKey
                , cms_common::Message::kCellInfoAddIndication
                , getCellAdd(CELL_4).c_str());
        servLm <---- fakeCms (S_CMS_CELL_INFO_NOTIFY, cmsKey
                , cms_common::Message::kCellInfoAddIndication
                , getCellAdd(CELL_5).c_str());
        servLm <---- fakeCms (S_CMS_CELL_INFO_NOTIFY, cmsKey
                , cms_common::Message::kCellInfoAddIndication
                , getCellAdd(CELL_6).c_str());
        servLm <---- fakeCms (S_CMS_CELL_INFO_NOTIFY, cmsKey
                , cms_common::Message::kCellInfoAddIndication
                , getCellAdd(CELL_7).c_str());
        setMREventType(MREventType::A5);
        ROLE(FtLm).activeMoChg(
                buildSsbMeasObjInterF(addNRInterFMeasObject, "1", "1",freqPriority).c_str());


        ROLE(FtLm).activeMoChg(R"({
                "path":"+:GNBCUCPFunction[moId='1'].NRCellCU[cellLocalId=1].PublicPrivateNetworkMigCtrl[moId='default'].LowSpeedInterFMigMeasCfg[refInterFMeasObject='GNBCUCPFunction=1,NRCellCU=1,MeasObjectConfig=1,InterFMeasObject=1']",
                "serialNumber":12,
                "value":{
                    "moId":"1",
                    "refInterFMeasObject":"GNBCUCPFunction=1,NRCellCU=1,MeasObjectConfig=1,InterFMeasObject=1",
                    "interFLowSpeedNCellMigPrio":100,
                    "refLowSpeedNCellMigRptCfg":"GNBCUCPFunction=1,NRCellCU=1,PublicPrivateNetworkMigCtrl=default,LowSpeedNCellMigRptCfg=1"
                }
                })");
    }

    void setInterFNbr()
    {
        auto plmnList = R"([{"mcc": "343630","mnc": "3031"},
                            {"mcc": "343630","mnc": "3033"},
                            {"mcc": "343630","mnc": "3034"}])";
        NbrPara nrNbr1;
        nrNbr1.cellId = 2;
        nrNbr1.ssbArfcn = 65000;
        nrNbr1.plmnList = plmnList;
        nrNbr1.highSpeedRailCellInd = false;
        NbrPara nrNbr2;
        nrNbr2.cellId = 3;
        nrNbr2.ssbArfcn = 66000;
        nrNbr2.plmnList = plmnList;
        nrNbr2.highSpeedRailCellInd = false;
        servLm <---- fakeNcs (NCS_CELL_NOTIFY_SESSION, cellNcsNotifyKey, ncs::Message::kNeighborRelationChangeNotify, getNrNbrAdd(CELL_1, {nrNbr1}).c_str());
        servLm <---- fakeNcs (NCS_CELL_NOTIFY_SESSION, cellNcsNotifyKey, ncs::Message::kNeighborRelationChangeNotify, getNrNbrAdd(CELL_1, {nrNbr2}, 2).c_str());
    }

    void anrEnable(bool enable)
    {
        servLm <---- fakeAnr (SON_ANR_CELL_NOTIFY_SESSION, InstKey::getCellInstKey(CELL_1), AnrMessage::kAnrDiscoverNbrPolicyChangeNotify, getAnrNotify(CELL_1, enable).c_str());
    }

    void initNbrAndPara(){
        auto nrFreq = R"([{"mo_id":"1", "ssb_frequency_arfcn":65000, "freq_band_indicator_nr":[77]}])";
        auto eutranFreq = R"([{"mo_id":"1", "frequency_arfcn":3450, "freq_band_list":[43]}])";
        servLm <---- fakeCms (S_CMS_CELL_INFO_NOTIFY, cmsKey,
                cms_common::Message::kCellInfoModifyIndication,
                getCellModify(nrFreq, eutranFreq,cms_common::NRCellScene::HighWay).c_str());

        ROLE(FtLm).activeMoChg( buildSsbMeasObjInterF(addNRInterFMeasObject,  "1", "1",255, "enable", "kHz15").c_str());
        setMREventType(MREventType::A5);
        ROLE(FtLm).activeMoChg( R"({
                        "path":"@:GNBCUCPFunction[moId='1'].NRCellCU[cellLocalId=1].MobilityCtrl[moId='default']",
                        "serialNumber":12,
                        "value":{ "interPLMNIdHOSwch":true}
                    })");
        ROLE(FtLm).activeMoChg( R"({
                        "path":"@:GNBCUCPFunction[moId='1'].NRCellCU[cellLocalId=1].MobilityCtrl[moId='default']",
                        "serialNumber":12,
                        "value":{"hoCandiCellNum":8}
                            })");
        ROLE(FtLm).activeMoChg(R"({
                         "path": "@:GNBCUCPFunction[moId='1'].NRCellCU[cellLocalId=1].MobilityCtrl[moId='default']",
                         "serialNumber":12,
                         "value":{
                                     "measReportReorderSwch": "Open"
                                 }
                    })");
        constexpr auto plmnList1 = R"([{"mcc":"343630", "mnc":"3031"}])";
        std::list<NbrPara> nbrs{};
        for(int i = 2; i < 8; i++)
        {
            NbrPara nbr;
            nbr.ssbArfcn = 65000;
            nbr.plmnList = plmnList1;
            nbr.cellId = i;
            nbr.gnbId = 100;
            nbrs.push_back(nbr);
        }
        servLm <---- fakeNcs (NCS_CELL_NOTIFY_SESSION, cellNcsNotifyKey, ncs::Message::kNeighborRelationChangeNotify, getNrNbrAdd(CELL_1, {nbrs}).c_str());
   }
    void configHoFailPunishPara()
    {
        ROLE(FtLm).activeMoChg(
                R"({
                          "path":"@:GNBCUCPFunction[moId='1'].MobilityCtrlGNBCU[moId='1'].HOFailPunish[moId='1']",
                          "serialNumber":12,
                          "value":{
                                   "moId":"1",
                                   "nrHoExeFailPunishSwch":"1",
                                   "nrHoExeFailPunishCellNum": 2,
                                   "nrHoExeFailDetectTimer":10,
                                   "nrHoExeFailDetectNumber":20,
                                   "nrHoExeFailPunishTimer":1}
                       })");
    }
    void addTwoInterNbr()
    {
        auto plmnList = R"([{"mcc": "343630","mnc": "3031"},
                                       {"mcc": "343630","mnc": "3033"},
                                       {"mcc": "343630","mnc": "3034"}])";
        NbrPara nrNbr1;
        nrNbr1.cellId = 2;
        nrNbr1.ssbArfcn = 65000;
        nrNbr1.plmnList = plmnList;
        nrNbr1.highSpeedRailCellInd = false;
        nrNbr1.pci = 29;
        NbrPara nrNbr2;
        nrNbr2.gnbId = EXT_GNB_102;
        nrNbr2.cellId = 3;
        nrNbr2.ssbArfcn = 65000;
        nrNbr2.plmnList = plmnList;
        nrNbr2.highSpeedRailCellInd = false;
        nrNbr2.pci = 30;
        servLm <---- fakeNcs (NCS_CELL_NOTIFY_SESSION, cellNcsNotifyKey, ncs::Message::kNeighborRelationChangeNotify, getNrNbrAdd(CELL_1, {nrNbr1}).c_str());
        servLm <---- fakeNcs (NCS_CELL_NOTIFY_SESSION, cellNcsNotifyKey, ncs::Message::kNeighborRelationChangeNotify, getNrNbrAdd(CELL_1, {nrNbr2}, 2).c_str());
    }

    void addTwoIntraNbr()
    {
        auto plmnList = R"([{"mcc": "343630","mnc": "3031"},
                                      {"mcc": "343630","mnc": "3033"},
                                      {"mcc": "343630","mnc": "3034"}])";
        NbrPara nrNbr1;
        nrNbr1.cellId = 2;
        nrNbr1.pci = 29;
        nrNbr1.plmnList = plmnList;
        nrNbr1.highSpeedRailCellInd = false;
        NbrPara nrNbr2;
        nrNbr2.gnbId= EXT_GNB_102;
        nrNbr2.cellId = 3;
        nrNbr2.plmnList = plmnList;
        nrNbr2.pci = 30;
        nrNbr2.highSpeedRailCellInd = false;
        servLm <---- fakeNcs (NCS_CELL_NOTIFY_SESSION, cellNcsNotifyKey, ncs::Message::kNeighborRelationChangeNotify, getNrNbrAdd(CELL_1, {nrNbr1}).c_str());
        servLm <---- fakeNcs (NCS_CELL_NOTIFY_SESSION, cellNcsNotifyKey, ncs::Message::kNeighborRelationChangeNotify, getNrNbrAdd(CELL_1, {nrNbr2}, 2).c_str());
    }
    void config()
    {
        setMREventType(MREventType::A3, MRNbrNum::NbrInvalid, MRPlmnType::plmn3);
        servLm <---- fakeAnr (SON_ANR_CELL_NOTIFY_SESSION, cellAnrNotifyKey, AnrMessage::kAnrDiscoverNbrPolicyChangeNotify, getAnrNotify(CELL_1, false, false, false).c_str());
        NbrPara nbr1;
        servLm <---- fakeNcs (NCS_CELL_NOTIFY_SESSION, cellNcsNotifyKey, ncs::Message::kNeighborRelationChangeNotify, getNrNbrDel(CELL_1, {nbr1}, 2).c_str());
        ROLE(FtLm).activeMoChg(R"({
                                   "path":"@:GNBCUCPFunction[moId='1'].NRCellCU[cellLocalId=1].PublicPrivateNetworkMigCtrl[moId='default']",
                                   "serialNumber":12,
                                   "value":{
                                              "lowSpeedNCellMigSwch":"1",
                                              "lowSpeedInterFNCellMigMethod":"0"
                                           }
                               })");
        auto nrFreq = R"([{"mo_id":"1", "ssb_frequency_arfcn":600000, "freq_band_indicator_nr":[77]}])";
        auto eutranFreq = R"([{"mo_id":"1", "frequency_arfcn":3450, "freq_band_list":[43]}])";
        servLm <---- fakeCms (S_CMS_CELL_INFO_NOTIFY, cmsKey
                , cms_common::Message::kCellInfoModifyIndication
                , getCellModify(nrFreq, eutranFreq).c_str());
        ROLE(FtLm).activeMoChg( buildSsbMeasObjInterF(addNRInterFMeasObject,  "1", "1",255, "enable", "kHz15").c_str());
    }

    void configXnSctp(std::list<XnSctp>& xnSctp)
    {
        setMREventType(MREventType::A5);
        auto nrFreq = R"([{"mo_id":"1", "ssb_frequency_arfcn":660000, "freq_band_indicator_nr":[77]}])";
        auto eutranFreq = R"([{"mo_id":"1", "frequency_arfcn":3450, "freq_band_list":[43]}])";
        servLm <---- fakeCms (S_CMS_CELL_INFO_NOTIFY, cmsKey, cms_common::Message::kCellInfoModifyIndication, getCellModify(nrFreq, eutranFreq, cms_common::NRCellScene::HighWay).c_str());
        ROLE(FtLm).activeMoChg( buildMeasObjInterF(addNRInterFMeasObject, "+", CELL_1, "1", 660000, "kHz30").c_str());
        ROLE(FtLm).activeMoChg( R"({
                        "path":"@:GNBCUCPFunction[moId='1'].NRCellCU[cellLocalId=1].MobilityCtrl[moId='default']",
                        "serialNumber":12,
                        "value":{"interPLMNIdHOSwch":"1;0;0"}
                    })");
        NbrPara nbr;
        servLm <---- fakeNcs (NCS_CELL_NOTIFY_SESSION, cellNcsNotifyKey, ncs::Message::kNeighborRelationChangeNotify, getNrNbrDel(CELL_1, {nbr}).c_str());
        XnSctp xnSctp1, xnSctp2;
        xnSctp1.gnbId = EXT_GNB_102;
        xnSctp1.assoid = 1;
        xnSctp.emplace_back(xnSctp1);
        xnSctp2.gnbId = EXT_GNB_103;
        xnSctp2.assoid = 2;
        xnSctp.emplace_back(xnSctp2);
    }

    void configUpConfidentProtec()
    {
        ROLE(FtLm).activeMoChg(
                  R"({
                        "path":"@:GNBCUCPFunction[moId='1'].MobilityGlobalConfig[moId='1'].HOFailPunish[moId='1'].HOPrepareFailPunish[moId='1']",
                        "serialNumber":12,
                        "value":{
                                    "moId":"1",
                                    "hoPrepareFailCauseList":["10"],
                                    "hoPrepareFailPunishMethod":"1"
                               }
                    })");
        HandoverResultReqBuild req(p2s::drm::HandoverResultRequest_HoResult_ho_preparation_fail, p2s::drm::HoPreparationFailCause_Cause_up_confidentiality_protection_not_possible);
        req.set(TargetNcgiBuild());
        MsgBuilder msg;
        msg.set(req);
        ASSERT_TRUE(UumAccess(DrmBuilder(msg()), HandoverResultRspNullAssert()));
        drm ----> fakeHrrm (S_HRRM_DRM, hucHrrmKey, HrrmDrmMessage::kNbrPunishDataNotification
                , NbrPunishDataNotificationAssert(::hrrm_drm::NbrPunishDataNotification_Action_add, ::hrrm_drm::punish_timer
                        , ::hrrm_drm::up_confidentiality_protection_not_possible,::hrrm_drm::handover_not_allowed, AuxiliaryFailureInformationBuild(), FailCount(0), IsNrNbr{true}, 85000000));
        drm <---- fakeHrrm (S_HRRM_DRM_BROADCAST, "load_transfer", HrrmDrmMessage::kHoPunishDataNotification , getHoPunishDataNotify(::hrrm_drm::TimestampType::punish_timer
                , ::hrrm_drm::HoPreparationFailCause::up_confidentiality_protection_not_possible, ::hrrm_drm::handover_not_allowed
                , AuxiliaryFailureInformationBuild(), FailCount(0), IsNrNbr(true)).c_str());
        ASSERT_TRUE(lmDataRepo().timers[castEnum(oss_timer::OssTimerType::Second)].find(TimerId(castEnum(DrmTimerType::HoPreparePunish), 1)));
    }

    void configPrepareHoFail()
    {
        ROLE(FtLm).activeMoChg(
                  R"({
                        "path":"@:GNBCUCPFunction[moId='1'].MobilityGlobalConfig[moId='1'].HOFailPunish[moId='1']",
                        "serialNumber":12,
                        "value":{
                                 "moId":"1",
                                 "hoPrepareFailPunishSwch":"1"
                                }
                    })");
        ROLE(FtLm).activeMoChg(
                  R"({
                        "path":"+:GNBCUCPFunction[moId='1'].MobilityGlobalConfig[moId='1'].HOFailPunish[moId='1'].HOPrepareFailPunish[moId='1']",
                        "serialNumber":12,
                        "value":{
                                 "moId":"1",
                                 "punishTimer":100,
                                 "deepPunishTimer":1,
                                 "hoFailNumber": 3,
                                 "deepDetectTimer":200,
                                 "hoPrepareFailPunishMethod":"0",
                                 "hoPrepareFailCauseList":["0"]
                               }
                    })");
        moker.mockAPI("getCurrentSystTimeUsU64", getCurrentSystTimeUsU64).stubs().with(any()).will(returnValue(U64(22000000)));
    }

    void handleCommonConfig()
    {
        ROLE(FtLm).activeMoChg(R"({
                        "path":"+:GNBCUCPFunction[moId='1'].NRCellCU[cellLocalId=1].PublicPrivateNetworkMigCtrl[moId='default'].LowSpeedInterFMigMeasCfg[refInterFMeasObject='GNBCUCPFunction=1,NRCellCU=1,MeasObjectConfig=1,InterFMeasObject=1']",
                        "serialNumber":12,
                        "value":{
                            "moId":"1",
                            "refInterFMeasObject":"GNBCUCPFunction=1,NRCellCU=1,MeasObjectConfig=1,InterFMeasObject=1",
                            "interFLowSpeedNCellMigPrio":100,
                            "refLowSpeedNCellMigRptCfg":"GNBCUCPFunction=1,NRCellCU=1,PublicPrivateNetworkMigCtrl=default,LowSpeedNCellMigRptCfg=1"
                        }
                        })");
        ROLE(FtLm).activeMoChg(R"({
                                     "path":"@:GNBCUCPFunction[moId='1'].NRCellCU[cellLocalId=1].PublicPrivateNetworkMigCtrl[moId='default']",
                                     "serialNumber":12,
                                     "value":{
                                                "lowSpeedNCellMigSwch":"1",
                                                "lowSpeedInterFNCellMigMethod":"0"
                                             }
                                 })");
        ROLE(FtLm).activeMoChg( R"({
                        "path":"@:GNBCUCPFunction[moId='1'].NRCellCU[cellLocalId=1].MobilityCtrl[moId='default']",
                        "serialNumber":12,
                        "value":{"hoAllowPartAdmSwch":"Open", "hoAssureServiceList":"2", "hoCandiCellNum":8}
                    })");
        ROLE(FtLm).activeMoChg( R"({
                "path":"@:GNBCUCPFunction[moId='1'].GlobalSwitchInformation",
                "serialNumber":12,
                "value":{"nbCellFiltBasedSliceSwch":"1"}
            })");
        auto nrFreq = R"([{"mo_id":"1", "ssb_frequency_arfcn":660000, "freq_band_indicator_nr":[77]}])";
        auto eutranFreq = R"([{"mo_id":"1", "frequency_arfcn":3450, "freq_band_list":[43]}])";

        setMREventType(MREventType::A5);

        servLm <---- fakeCms (S_CMS_CELL_INFO_NOTIFY, cmsKey, cms_common::Message::kCellInfoModifyIndication, getCellModify(nrFreq, eutranFreq,cms_common::NRCellScene::HighWay).c_str());
        ROLE(FtLm).activeMoChg( buildMeasObjInterF(addNRInterFMeasObject, "+", CELL_1, "1", 660000, "kHz15").c_str());

        NbrPara nbr2;
        nbr2.gnbId = EXT_GNB_102;
        nbr2.cellId = CELL_2;
        nbr2.pci = 29;
        nbr2.tac = 2;
        nbr2.ssbArfcn = 660000;
        nbr2.nrHoState = ncs::nr_support_handover;
        json notify = nlohmann::json::parse(getNrNbrMod(CELL_1, {nbr2}, 1));
        notify["neighbor_relation_change_notify"]["nbr_change_info"]["neighbor_cell_mod_list"][0]["tac"] = 2;
        servLm <---- fakeNcs (NCS_CELL_NOTIFY_SESSION, cellNcsNotifyKey, ncs::Message::kNeighborRelationChangeNotify, notify.dump(4).c_str());
        servLm <---- fakeXnm (S_UCS_GIS_XN_INFO_NOTIFY, xnSctpApKey, XnMessage::kXnSctpApInfoNotify, getXnApInd(GNB_100, {}, 1).c_str());
    }

    void recvHighSpeedTrainComingNotify(U32 trainCoingFlag)
    {
        constexpr auto notify =  R"(
               {
                  "high_speed_train_coming_notify":{
                    "cell_list":
                  [{
                    "ncgi":{
                      "plmn": {
                        "mcc": "343630",
                        "mnc": "3031"
                       },
                       "gnb_id": 100,
                       "gnb_length": 24,
                       "nr_cell_id": 1
                    },
                    "high_speed_train_coming_flag": 1
                  }]
               }}
            )";

        json notifyJson = json::parse(notify);
        notifyJson["high_speed_train_coming_notify"]["cell_list"][0]["high_speed_train_coming_flag"] = trainCoingFlag;
        servLm <---- fakeHrrm (S_HRRM_DRM_BROADCAST, hrrmDrmKey,
                HrrmDrmMessage::kHighSpeedTrainComingNotify, notifyJson.dump(4).c_str());
    }

protected:
    SetFunc<PlmnSnssaiNr5qi> partFailInfo;
    SetFunc<PLmnNssai> notSupportSlice;
    SetFunc<U32> notSupport5qi;
    std::list<BuildUeSecurityCapabilities> ueSecuCap;
    Mocker moker;
};
```

# 相似代码信息
## 相似文本测试用例描述
### 测试标题
测试用例 RAN-1143636: 执行方式为切换和重定向，上报多PCI，可切换小区大于候选小区个数验证
### 预置条件
1、NR站上cell1工作正常；
### TC步骤
小区场景（nrCellScene）:打开lowSpeedInterFNCellMigMethod（低速用户向异频普通小区迁移执行方式）:切换和重定向候选小区个数:1.0邻接小区是否属于高铁小区(highSpeedRailCellInd):f1(cell2~3:否)测试场景:UE接入，下发频点f1的业务测量，触发cell2~cell3上报

### TC预期结果
测试结果:输出到cell2的切换，并且输出重定向频点f1

### 预期结果
执行方式为切换和重定向，上报多PCI，可切换小区大于候选小区个数时，输出切换和重定向

### 验收准则
执行方式为切换和重定向，上报多PCI，可切换小区大于候选小区个数时，输出切换和重定向

## 相似用例FT代码
代码路径: ft/drm/lf/src/test/mrRptDecision/speepMigMeas/FtSpeedMigRptHandle.cpp
```cpp
TEST_F(FtSpeedMigRptHandle, TFS7480661_speedmig_base_hoandrd_with_candicellnum_is_one_then_put_targetcell_and_rdfreq)
{
    configMeasObject(PRIORITY_255);
    ROLE(FtLm).activeMoChg(buildSsbMeasObjInterF(addNRInterFMeasObject, "2", "2", 253, "enable", "kHz15").c_str());
    auto servingPlmn = R"({"mcc": "343630", "mnc": "3031"})";
    auto equivalentPlmns = R"([{"mcc": "343630","mnc":"3035"},
                               {"mcc": "343630","mnc":"3036"}])";
     std::vector<int> rsrp_option = {20, 21};
     initNbrAndPara();
     ROLE(FtLm).activeMoChg(R"({
                             "path":"@:GNBCUCPFunction[moId='1'].NRCellCU[cellLocalId=1].PublicPrivateNetworkMigCtrl[moId='default']",
                             "serialNumber":12,
                             "value":{
                                        "lowSpeedNCellMigSwch":"1",
                                        "lowSpeedInterFNCellMigMethod":"1"
                                     }
                         })");
     ROLE(FtLm).activeMoChg( R"({
                             "path":"@:GNBCUCPFunction[moId='1'].NRCellCU[cellLocalId=1].MobilityCtrl[moId='default']",
                             "serialNumber":12,
                             "value":{"hoCandiCellNum":1}
                         })");
     std::vector<U32> cells {CELL_3};
     ASSERT_TRUE(UumAccess(MeasRptBuilder(getMeasRptNbrs(rsrp_option), getMeasReportCtxWithUeSpeedInd(servingPlmn,equivalentPlmns,p2s::drm::low_speed_interf_mig, 5)),
             SpeedMigHoAndRedirectAssert(cells,65000)));
}
```

## 相似用例代码依赖
代码路径: ft/drm/lf/ft-code/src/ft/domain/dm5/uc/builder/MeasurementReportJsonBuilder.cpp
```cpp
string getMeasReportCtxWithUeSpeedInd(const string& restrictionServingPlmn, const string& restrictionForEquivalentPlmns,p2s::drm::RptPurpose purpose, U8 eventType, U8 ueSpeedInd,p2s::drm::UeType ueType
        , bool isAtu, bool nrCgiReport, bool longDrxPresent, bool isVonr, bool before5qi1Setup, ReportInterval_Root rptInterval, bool hasUeScellInfo, U32 servCellId, const string& mrl)
{
    json servingPlmn = nlohmann::json::parse(restrictionServingPlmn);
    json equivalentPlmns = nlohmann::json::parse(restrictionForEquivalentPlmns);
    json ctx = nlohmann::json::parse(measReportReq);

    ctx["measurement_report_decision_request"]["serving_plmn"]=servingPlmn;
    ctx["measurement_report_decision_request"]["ue_meas_context"]["meas_config"]["meas_objects"][0]["meas_object_nr"]["mo_id"] = "1";
    ctx["measurement_report_decision_request"]["ue_meas_context"]["meas_config"]["report_configs"][0]["purpose"] = purpose;
    ctx["measurement_report_decision_request"]["ue_meas_context"]["meas_config"]["report_configs"][0]["report_interval"] = rptInterval;
    ctx["measurement_report_decision_request"]["ue_meas_context"]["meas_config"]["report_configs"][0]["event_type"] = eventType;
    ctx["measurement_report_decision_request"]["ue_meas_context"]["meas_config"]["report_configs"][0]["mo_id"] = "1";
    ctx["measurement_report_decision_request"]["ue_cap_context"]["ue_speed_ind"]["speed_ind"] = ueSpeedInd;
    json ueNrCapInfo;
    ueNrCapInfo["nr_cgi_reporting"] = nrCgiReport;
    ueNrCapInfo["long_drx_cycle"] = longDrxPresent;
    ueNrCapInfo["handover_interf"] = true;
    ueNrCapInfo["handover_fdd_tdd"] = true;
    modUeNrCapInfo(ctx["msg_header"]["cpf_ue_id"], ueNrCapInfo, true);
    ctx["measurement_report_decision_request"]["mobility_restriction_list"]["serving_plmn"] = servingPlmn;
    ctx["measurement_report_decision_request"]["mobility_restriction_list"]["equivalent_plmn_list"] = equivalentPlmns;
    ctx["measurement_report_decision_request"]["ue_type"] = ueType;
    ctx["measurement_report_decision_request"]["ue_meas_context"]["meas_config"]["ping_pong_prevention_info"]["trigger_functional_ho_cause"] = p2s::drm::TriggerFunctionalHoCause_Cause_based_high_speed_public_private_network;
    ctx["measurement_report_decision_request"]["ue_meas_context"]["meas_config"]["ping_pong_prevention_info"]["time_stamp"] = 84000000;
    ctx["msg_header"]["ue_data"]["atu_flag"]["value"] = isAtu;
    if(isVonr)
    {
        json ueQosContext = nlohmann::json::parse(R"({
                    "pdu_session_resource_list": [{
                        "pdu_session_id":1,
                        "qos_flow_list":[
                            {"qos_flow_indicator": 1, "non_dynamic_5qi_desc": { "nr_5qi": 5}},
                            {"qos_flow_indicator": 2, "non_dynamic_5qi_desc": {"nr_5qi": 1}}
                ]}]})");
        ctx["measurement_report_decision_request"]["ue_qos_context"] = ueQosContext;
    }
    if(before5qi1Setup)
    {
        ctx["measurement_report_decision_request"]["before_5qi1_setup"]["value"] = true;
    }
    if(hasUeScellInfo)
    {
        ctx["measurement_report_decision_request"]["ue_scell_info"]["scell_list"][0]["serving_cell_index"] = servCellId;
        ctx["measurement_report_decision_request"]["ue_scell_info"]["scell_list"][0]["ncgi"]["cell_id"] = 3;
        ctx["measurement_report_decision_request"]["ue_scell_info"]["scell_list"][0]["ncgi"]["gnb_id"] = 102;
        ctx["measurement_report_decision_request"]["ue_scell_info"]["scell_list"][0]["freq_info"]["arfcn"] = 630000;
        ctx["measurement_report_decision_request"]["ue_scell_info"]["scell_list"][0]["freq_info"]["sub_carrier_spacing"] = 1;
        ctx["measurement_report_decision_request"]["ue_scell_info"]["scell_list"][0]["freq_info"]["band_indicator"] = 78;
    }
    if(mrl != "{}")
    {
        json mobilitylist = nlohmann::json::parse(mrl);
        ctx["measurement_report_decision_request"]["mobility_restriction_list"] = mobilitylist;
    }
    return ctx.dump(4);
}
```



# 代码生成要求
请根据提供的相关信息和相似信息，遵循以下原则进行测试代码生成：
1. 完整性原则
  - 必须输出完整的函数实现，包括函数声明、函数体和结束花括号
  - 不允许使用省略号或只显示修改部分
  - 保持函数的完整上下文

2. 参数化设计原则
  - 新增逻辑应通过函数参数控制
  - 避免硬编码固定值
  - 保持函数接口的灵活性

4. 测试用例适配
  - 确保测试用例覆盖新增功能
  - 维护测试用例的正确性
  - 补充必要的测试场景

5. 代码修改文件生成方法
  -对于测试套和测试代码，按照相似用例文件名输出
  -对于消息请求构造、校验代码，按照原有代码文件名输出

# 建议的实现步骤
## 生成策略如下：将用例分别拆分为不同内容，每块内容分别按照对应的方式来生成:
1. 网管参数配置：包含各种参数配置，由参数路径和参数值组成，可以调用参数配置函数或使用原始json字符串配置；
2. 邻区配置：判断邻区中的配置数量和邻区类型，调用邻区配置结构体或函数生成，可以参考邻区配置相似代码配置
3. 测量对象配置，判断用例场景是否需要下发测量以及下发的测量类型和数量，调用测量对象配置函数生成
4. 收发消息配置，判断用例中是否有消息交互，以及交互的模块和消息内容，需要分别模拟构造这些消息
5. 判断用例的接纳消息接口，参考历史接纳消息的构造方式进行消息构造
6. 判断用例中的校验响应消息名称，根据响应消息结构体校验响应内容

# 各类用例内容代码修改方法示例
## STEP1：网管参数配置
- 构造网管参数配置信息，可以两种方式生成，选择一种方式生成即可（优先方式一）：
方式一：通过调用网管参数配置函数生成，
方式二：通过构造传入原生的json字符串配置。
### 示例
#### 方式一：通过调用网管参数配置函数生成
- 用例描述：配置CoverMobilityMeasCfg节点
- 网管参数节点详情：
{
    "MOC名": "CoverMobilityLTEFreqMeasCfg",
    "父节点": "CoverMobilityMeasCfg",
    "MOC类型": "List",
    "MOC属性": "",
    "MOC中文名称": "NR CU小区基于覆盖的异系统测量",
    "MOC英文名称": "NR CU cell coverage-based LTE-frequency measurement configuration",
    "MOC中文详解": "该MO节点用于配置NR CU小区基于覆盖移动性功能EUTRAN异系统测量配置的相关参数。基站在该节点下配置用户执行EUTRAN异系统切换或重定向流程的触发条件和测量优先级顺序，在服务小区覆盖变差时通过优先级顺序进行EUTRAN异系统频点测量，为用户选择合适的EUTRAN异系统邻区触发切换或重定向流程，降低UE掉线脱网的可能。",
    "MOC英文详解": "This MO is used for adding inter-RAT measurement configurations in coverage-based mobility control at the NR CU cell level. It specifies the conditions for triggering the procedures for inter-RAT handover or redirection and the measurement priorities. After this MO is configured, if the coverage of the serving cell deteriorates, a UE measures the EUTRAN frequencies based on the configured priorities and the gNodeB can select a suitable EUTRAN neighbor cell or frequency, so that the UE can initiate a procedure for handover to the cell or a procedure for redirection to the frequency. In this way, it is less likely for the UE to be disconnected from the network.",
    "MOC记录数范围": "[0..16]",
    "领域": "CPA",
    "MOC全路径": "ManagedElement.GNBCUCPFunction.NRCellCU.CoverMobilityCtrl.CoverMobilityMeasCfg.CoverMobilityLTEFreqMeasCfg"
}
- 网管配置函数：
```cpp
string addCoverMobilityLTEFreqMeasCfg(string type, string measCfgMoid, string LTEFreqMeasCfgMoid, BYTE hoPri, BYTE rdPri, string LTEFreqCovHoMoid, string lteFreqA1A2ThresholdOffset, string hSLTEFreqA1A2ThrdOffset)
{
    auto measCfg = R"({"path": ")" + type + R"(:GNBCUCPFunction[moId='1'].NRCellCU[cellLocalId=1].CoverMobilityCtrl[moId='default'].CoverMobilityMeasCfg[moId=')"+measCfgMoid+R"('].CoverMobilityLTEFreqMeasCfg[refMeasObjEUTRA='GNBCUCPFunction=1,NRCellCU=1,MeasObjectConfig=1,MeasObjEUTRA=)" + LTEFreqMeasCfgMoid + R"(']",
               "serialNumber":12,
               "value":{
                       "moId": ")" + LTEFreqMeasCfgMoid + R"(",
                       "refMeasObjEUTRA": "GNBCUCPFunction=1,NRCellCU=1,MeasObjectConfig=1,MeasObjEUTRA=)" + LTEFreqMeasCfgMoid + + R"(",
                       "refLTEFreqCovHo": "GNBCUCPFunction=1,NRCellCU=1,CoverMobilityCtrl=default,CoverMobilityRptCfg=1,LTEFreqCovHo=)" + LTEFreqCovHoMoid + R"("
                       }})";
    json LTEMeasCfg = nlohmann::json::parse(measCfg);
    json& LTEMeasCfgValue = LTEMeasCfg["value"];
    LTEMeasCfgValue["lteFreqCovHoPrio"] = hoPri;
    LTEMeasCfgValue["lteFreqBlindRedPrio"] = rdPri;
    LTEMeasCfgValue["lteFreqA1A2ThresholdOffset"] = lteFreqA1A2ThresholdOffset;
    LTEMeasCfgValue["hSLTEFreqA1A2ThrdOffset"] = hSLTEFreqA1A2ThrdOffset;

    return LTEMeasCfg.dump(4);
}
```
- 生成网管配置代码：
```cpp
ROLE(FtLm).activeMoChg(addCoverMobilityLTEFreqMeasCfg("+", "default", "1", 255, 255, "1").c_str());
```
#### 方式二：通过构造传入原生的json字符串配置
- 用例描述：NRDC功能开关：打开
- 参数路径：ManagedElement.GNBCUCPFunction.NRCellCU.NRDCCoverage.nrdcSwitch
- 参数取值："1;1"
- 参数详情:
{
    "参数全路径": "ManagedElement.GNBCUCPFunction.NRCellCU.NRDCCoverage.nrdcSwitch",
    "父节点和参数名": "NRDCCoverage.nrdcSwitch",
    "参数名": "nrdcSwitch",
    "是否数组": "array",
    "数组元素个数": "[2..2]",
    "参数中文名称": "NR-DC功能开关",
    "参数英文名称": "NR-DC function",
    "取值范围中文": "{0:关闭,1:打开}",
    "取值范围英文": "{0:Close,1:Open}",
    "数据类型": "enum",
    "默认值": "0;0",
    "引用路径": "",
    "iszkey": "否",
    "取值类型补充说明（pattern正则）": "",
    "取值示例": "",
    "中文详解": "该参数为NR-DC功能开关。该参数为一个数组，包含两个元素。当数组元素0取值为打开时，基站可以为接入当前小区且支持NR-DC的终端配置NR双连接，使得终端可以同时获取2个基站的流量服务；当数组元素0取值为关闭时，基站不会为接入当前小区的终端配置NR双连接。当数组元素1取值为打开，则表示当前小区可以作为NR-DC下的PSCell，使得终端在使用其他小区的服务同时，使用当前小区的服务。如果数组元素1取值为关闭，则表示当前小区不能作为NR-DC下的PSCell。"
}
- 生成网管配置代码：
```cpp
ROLE(FtLm).activeMoChg(R"({
        "path": "@:GNBCUCPFunction[moId='1'].NRCellCU[cellLocalId=1].NRDCCoverage[moId='default']",
        "serialNumber": 12,
        "value": {
            "nrdcSwitch": "1;0"
        }})");
```


## STEP2：邻区配置
- 构造配置用例中描述的邻区数据和字段，可以调用函数或使用代码片段配置邻区，如果不需要配置或已经配置过，则输出为空
### 示例：
- 用例描述：添加异频测量报告，包含多个小区
- 邻区配置类型和数量：
{
    "need_nbr": "Y",
    "nbr_function":"",
    "intra_nbr_num": "0",
    "inter_nbr_num": "5",
    "inter_system_nbr_num": "0"
}
- 生成代码：
```cpp
std::list<NbrPara> nbrs{};
for(int i = 1; i <= 5; i++)
{
    NbrPara nbr;
    nbr.cellId = 1 + i;
    nbr.gnbId = 101;
    nbr.ssbArfcn = 630000 + i*10000;
    nbrs.push_back(nbr);
}
servLm <---- fakeNcs (NCS_CELL_NOTIFY_SESSION, cellNcsNotifyKey, ncs::Message::kNeighborRelationChangeNotify, getNrNbrAdd(CELL_1, {nbrs}, 2).c_str());
```


    ## STEP3：测量对象配置
- 构造并配置用例描述的测量对象，可以调用函数或使用代码片段配置邻区
### 示例：
- 用例描述：下发异频和异系统测量成功
- 测量对象数量和类型：
{
    "need_meas": "Y",
    "meas_function":"",
    "intra_freq_meas_num": "0",
    "inter_freq_meas_num": "1",
    "inter_system_meas_num": "1"
}
- 生成代码：
```cpp
auto nrFreq = R"([{"mo_id":"1", "ssb_frequency_arfcn":600000, "freq_band_indicator_nr":[77]}])";
auto eutranFreq = R"([{"mo_id":"1", "frequency_arfcn":3450, "freq_band_list":[43]}])";
servLm <---- fakeCms (S_CMS_CELL_INFO_NOTIFY, cmsKey
        , cms_common::Message::kCellInfoModifyIndication
        , getCellModify(nrFreq, eutranFreq).c_str());
```


## STEP4：收发消息配置
- 以DRM模块为被测模块，需要分别判断DRM模块发送给其他模块和接收到其他模块的消息，对于发送给其他模块的消息需要在测试用例代码中模拟构造这部分消息内容
### 示例：
- 用例描述：DRM模块接收到CF1消息，携带data_quality_migrate_rsp，且req_serial_number为无效值
- 相关接口：
```proto
message Message
{
    oneof Messages
    {
        PrivateContainerInStdMsg         private_container_in_std_msg                               = 1;
        PrivateMsg                       private_msg                                                = 2;
        PrivateInfoContainer             private_info_container                                     = 3;  //to del
    }

}

message PrivateMsg
{
    oneof Messages
    {
        ResumeTimeOptimizeContainer resume_time_optimize_container                                  = 1;
        CompSelfConfigConfigInfoSyncNotify comp_self_config_config_info_sync_notify                 = 2;
        CompSelfConfigConfigInfoSyncNotifyAck comp_self_config_config_info_sync_notify_ack          = 3;
        GsmFreqNotify gsm_freq_notify                                                               = 4;
        CoAauIdentifyRequest co_aau_identify_request                                                = 5;
        CoAauIdentifyResponse co_aau_identify_response                                              = 6;
        MobilityQualityHoContainer  mobility_quality_ho_container                                   = 7;
        KpiInfoTransfer  kpi_info_transfer                                                          = 8;
        PlmnEncodeModeRequest plmn_encode_mode_request                                              = 9;
        PlmnEncodeModeResponse plmn_encode_mode_response                                            = 10;
        PlmnEncodeModeChangeNotify plmn_encode_mode_notify                                          = 11;
        F1UnavilableIndication f1_unavilable_indication                                             = 12;
        OIUlInformation  oi_ul_information                                                          = 13;
        OIDlInformation  oi_dl_information                                                          = 14;
        F1CuDuPowerOnNotify f1_cu_du_power_on_notify                                                = 15;
        UeUlPowerControlNotify ue_ul_power_control_notify                                           = 16;
        CmpClockInfoContainer  cmp_clock_info_container                                             = 17;
        UeSnDelIndication   ue_sn_del_indication                                                    = 19;
        F1CuCellStatusNotify f1_cu_cell_status_notify                                               = 20;
        AtuStateSyncNotify  atu_state_sync_notify                                                   = 21;
        PositionConfigContainer position_config_container                                           = 22;
        BplLoadStateNotification bpl_load_state_notification                                        = 23;
        MRBInfoContaioner              mrb_info_container                                           = 24;
        EutranRelationDelNotify eutran_relation_del_notify                                          = 25;
        EutranRelationDelNotifyAck eutran_relation_del_notify_ack                                   = 26;
        RsiBlackListNotify rsi_blacklist_notify                                                     = 27;
        DUToCUStatelessCellInfoNotify du_to_cu_stateless_cell_info_notify                           = 28;
        DUToCUStatelessCellInfoNotifyAck du_to_cu_stateless_cell_info_notify_ack                    = 29;
        DUStatelessCellInfoQueryInd     du_stateless_cell_info_query_ind                            = 30;
        RrcSetupMtOptEnquiryRequest rrc_setup_mt_opt_enquiry_request                                = 31;
        RrcSetupMtOptEnquiryResponse rrc_setup_mt_opt_enquiry_response                              = 32;
        UlLargePacketIdentityRequest    ul_large_packet_identity_request                            = 33;
        UlLargePacketIdentityResponse   ul_large_packet_identity_response                           = 34;
        UlLargePacketIdentityReport     ul_large_packet_identity_report                             = 35;
        SpecialMsgWithEmptyInfo                es_cell_self_config_policy_query_request             = 36;
        EsCellNrSelfConfigPolicyModifyNotify   es_cell_nr_self_config_policy_modify_notify          = 37;
        CellOperationOnlyWithNcgiList          es_cell_nr_self_config_policy_delete_notify          = 38;
        EsCellIratSelfConfigPolicyModifyNotify es_cell_irat_self_config_policy_modify_notify        = 39;
        CellOperationOnlyWithNcgiList          es_cell_irat_self_config_policy_delete_notify        = 40;
        CBFUeNumInfoNotify      cpf_ue_num_info_notify                                              = 41;
        CBFUeMeasResultNotify   cbf_ue_meas_result_notify                                           = 42;
        VswUpLoadCollectReq     vsw_up_load_collect_req                                             = 43;
        VswUpLoadCollectRsp     vsw_up_load_collect_rsp                                             = 44;
        VswUpLoadReport     vsw_up_load_report                                                      = 45;
        SpecialMsgWithEmptyInfo                 es_cell_self_config_policy_query_response           = 46;

        KpiCollectRequest                   kpi_collect_request                                     = 47;
        KpiCollectResponse                  kpi_collect_response                                    = 48;
        KpiReportNotify                     kpi_info_report                                         = 49;

        StopKpiCollectNotify                stop_kpi_collect_notify                                 = 50;
        F1DuCuContainerResetNotify          f1_du_cu_container_reset_notify                         = 51;

        AwareNetworkNotify                  aware_network_notify                                    = 52;
        AwareNetworkNotifyAck               aware_network_notify_ack                                = 53;
        GuaranteeParamsNotify               guarantee_params_notify                                 = 54;
        GuaranteeParamsNotifyAck            guarantee_params_notify_ack                             = 55;
        GuaranteeEffectivenessNotify        guarantee_effectiveness_notify                          = 56;
        GuaranteeEffectivenessNotifyAck     guarantee_effectiveness_notify_ack                      = 57;
        UeGridReportRequest                 ue_grid_report_request                                  = 58;
        AtuNbrCellNotify                    atu_nbr_cell_notify                                     = 59;

        FunctionLinkChooseRequest        function_link_choose_request      = 60;
        FunctionLinkChooseResponse       function_link_choose_response     = 61;
        LinkAbnormalNotify               link_abnormal_notify              = 62;
        NrCarrierToLteCarrierNotify      nr_carrier_to_lte_carrier_notify  = 63;
        LteCarrierToNrCarrierNotify      lte_carrier_to_nr_carrier_notify  = 64;

        CarrierEsLoadEvaluateRequest            carrier_es_load_evaluate_request     = 65;
        CarrierEsOperationResult                carrier_es_load_evaluate_response    = 66;
        CarrierEsOperationResult                carrier_es_load_evaluate_notify      = 67;
        CarrierEsCellEsRequest                  carrier_es_cell_es_request           = 68;
        CarrierEsOperationResult                carrier_es_cell_es_response          = 69;
        CellOperationOnlyWithNcgi               carrier_es_cell_activate_notify      = 70;
        CellOperationOnlyWithNcgi               carrier_es_policy_query_request      = 71;
        CarrierActivateLoadEvaluatePolicy       carrier_es_policy_query_response     = 72;
        CarrierActivateLoadEvaluatePolicy       carrier_es_policy_add_notify         = 73;
        CarrierActivateLoadEvaluatePolicy       carrier_es_policy_mod_notify         = 74;
        CellOperationOnlyWithNcgi               carrier_es_policy_del_notify         = 75;
        CellOperationOnlyWithNcgi               es_special_cell_state_query_request  = 76;
        CellSpeedTestInfo                       es_special_cell_state_query_response = 77;
        CellSpeedTestInfo                       es_special_cell_state_notify         = 78;

        CuCellRelNotify                      cu_cell_rel_notify                      = 79;
        LteDssCellLoadCollectRequest         lte_dss_cell_load_collect_request       = 80; /*LTE载波组负荷画像采集请求 */
        LteDssCellLoadCollectResponse        lte_dss_cell_load_collect_response      = 81; /*LTE载波组负荷画像采集响应 */
        LteDssCellLoadProfileReport          lte_dss_cell_load_profile_report        = 82; /*LTE载波组负荷画像上报接口 */
        LteDssCellLoadPredictResultNotify    lte_dss_cell_load_predict_result_notify = 83; /*NR TO LTE负荷预测结果通知接口 */
        LteCellDssStateNotify                lte_cell_dss_state_notify               = 84; /*LTE TO 5G 载波状态通知 */
        LteCellDssStateAck                   lte_cell_dss_state_ack                  = 85; /*5G TO LTE 载波状态通知响应 */
        LteDssCellAwakeRequest               lte_dss_cell_awake_request              = 86; /*NR TO LTE载波唤醒通知 */
        LteDssCellAwakeResponse              lte_dss_cell_awake_response             = 87; /*LTE载波唤醒通知响应 */
        NrCarrierLogicCellNotify             nr_carrier_logic_cell_notify            = 88; /*频谱关系协商通知 */
        NrCarrierLogicCellAck                nr_carrier_logic_cell_ack               = 89; /*频谱关系协商响应 */
        XeapInfoRequest                      xeap_info_request                       = 90; /*XEAP链路状态请求 */
        XeapInfoResponse                     xeap_info_response                      = 91; /*XEAP链路状态响应 */
        XeapInfoIndication                   xeap_info_change_notify                 = 92; /*XEAP链路状态变更通知 */

        CellPhysicalRelationRequest          cell_physical_relation_request          = 93;
        CellPhysicalRelationResponse         cell_physical_relation_response         = 94;
        NrSonPciBlackListNotify              nr_son_pci_black_list_notify            = 95;
        CompUeMeasResultNotify               comp_ue_meas_result_notify              = 96;

        CaConfigQueryRequest                ca_config_query_request                  = 97;
        CaConfigQueryResponse               ca_config_query_response                 = 98;
        CaConfigModifyNotify                ca_config_modify_notify                  = 99;
        FAPdcpThroughputQueryRequest        fa_pdcp_throughput_query_request         = 100;
        FAPdcpThroughputQueryResponse       fa_pdcp_throughput_query_response        = 101;
        FAPdcpThroughputReport              fa_pdcp_throughput_report                = 102;
        FAStopKpiCollectNotify              fa_stop_kpi_collect_notify               = 103;
        FACuPowerOnNotify                   fa_cu_power_on_notify                    = 104;
        FACuDtsStateRequest                 fa_cu_dts_state_request                  = 105;
        FACuDtsStateNotify                  fa_cu_dts_state_notify                   = 106;

        MmicPrbScheduleAdjust               mmic_prb_schedule_adjust                 = 107;
        UeAtuSubTypeIndication              ue_atu_sub_type_indication               = 108;

        CellESStateQueryRequest             cell_es_state_query_request              = 109;
        CellESStateQueryResponse            cell_es_state_query_response             = 110;

        TrpAacMeasurementReport             trp_aac_measurement_report               = 111; //定位相关：双载波需求新增，按TRP按频点分组,COS->MIM
        TrpProcDelayMeasurementReport       trp_proc_delay_measurement_report        = 112; //定位相关：无空口校准，上报TRP处理时延
        UEPayLoadTaskRequest                ue_payload_task_request                  = 113;
        UEPayLoadTaskResponse               ue_payload_task_response                 = 114;
        UEPayLoadInfoRequest                ue_payload_info_request                  = 115;
        UEPayLoadInfoResponse               ue_payload_info_response                 = 116;
        CuMsRestartNotify                   cu_ms_restart_notify                     = 117;

        LogicCellInfo                       cu_cell_state_notify                     = 118;
        EsAdaptiveTaskReq                   es_adaptive_task_req                     = 119;
        EsAdaptiveTaskRsp                   es_adaptive_task_rsp                     = 120;
        EsAdaptiveLoadForecastNotify        es_adaptive_load_forecast_notify         = 121;
        EsAdaptiveLoadForecastResult        es_adaptive_load_forecast_result         = 122;
        EsCellAccuracyRequest               es_cell_accuracy_request                 = 123;
        EsCellAccuracyResponse              es_cell_accuracy_response                = 124;
        EsCellAccuracyNotify                es_cell_accuracy_notify                  = 125;

        UlCongestDetectRequest              ul_congest_detect_request                = 126;
        UlCongestDetectResponse             ul_congest_detect_response               = 127;
        UlCongestDetectReport               ul_congest_detect_report                 = 128;
        UlMinThrdDetectContainer            ul_min_thrd_detect_container             = 129;

        TrpAacMeasurementReportAcknowledge  trp_aac_measurement_report_acknowledge   = 130;//定位相关,MIM->COS
        TrpAacMeasurementRequest            trp_aac_measurement_request              = 131;//定位相关,MIM->COS
        TrpAacMeasurementReport             trp_aac_measurement_response             = 132;//定位相关,COS->MIM
        DuFailUeRelOpt                      du_fali_ue_rel_Opt                       = 133;
        CuParamerReq                        cu_param_req                             = 134;
        CuParamerRsp                        cu_param_rsp                             = 135;
        ShMasterControlReport               sh_master_control_report                 = 136;

        PositioningMeasurementReset             positioning_measurement_reset              = 137;
        PositioningMeasurementResetAcknowledge  positioning_measurement_reset_acknowledge  = 138;


        CoordinateCellConfigRequest         coordinate_cell_config_request           = 139;  /* CU -> DU */
        CoordinateCellConfigResponse        coordinate_cell_config_response          = 140;

        GnbSwVersionInfoQueryRequest        gnb_sw_version_info_query_request        = 141;
        GnbSwVersionInfoIndication          gnb_sw_version_info_query_response       = 142;
        GnbSwVersionInfoIndication          gnb_sw_version_info_indication           = 143;
        LucmToHucmIndication                lucm_to_hucm_indication                  = 144;
        AwareNetworkSyncPolicyNotice        aware_network_sync_policy_notice         = 145;
        AwareNetworkSyncPolicyAck           aware_network_sync_policy_ack            = 146;
        AwareNetworkAddNcgiNotice           aware_network_add_ncgi_notice            = 147;
        AwareNetworkAddNcgiAck              aware_network_add_ncgi_ack               = 148;

        CellPhysicalRelationNotify          cell_physical_relation_notify            = 149;
        AtuInfoRequest                      atu_info_req                             = 150;//LUCM发送；
        AtuInfoResponse                     atu_info_rsp                             = 151;//HUCM回复响应；
        AtuInfoIndication                   atu_info_ind                             = 152;//HUCM发送;

        HsrCellComeLeaveTrainNotify    hsr_carrier_come_leave_train_notify           = 153;
        HsrCellInfoReq                      hsr_cell_info_req                        = 154;
        HsrCellInfoRsp                      hsr_cell_info_rsp                        = 155;
        HsrCellInfoNotify                   hsr_cell_info_notify                     = 156;
        HsrComeLeaveTrainNotify             hsr_cell_come_leave_train_notify         = 157;

        CarrierEsCellEsTypeChangeNotify     carrier_es_cell_es_type_change_notify    = 158;
        EsFrequencyInfoQueryRequest         es_frequency_info_query_request          = 159;
        EsFrequencyInfoChangeNotify         es_frequency_info_change_notify          = 160;
        EsCellOutOfServiceStatisticNotify   es_cell_out_of_service_statistic_notify  = 161;
        CellLoadEvaluateReq                 cell_load_evaluate_req                   = 162;
        CellLoadEvaluateRsp                 cell_load_evaluate_rsp                   = 163;

        UeUlEndcPowerShareInd               ue_ul_endc_power_share_ind               = 164;
        NrMaxTbsizeInd                      nr_max_tb_size_ind                       = 165;
        ShLogReportSwitchTransfer           sh_log_report_switch_transfer            = 166;
        ShLogReportSwitchTransfer           sh_log_report_switch_request             = 167;
        ShLogReportSwitchTransfer           sh_log_report_switch_response            = 168;

        IntentEsCellOptimizationReq         intent_es_cell_optimization_req       = 169;
        IntentEsCellOptimizationRsp         intent_es_cell_optimization_rsp       = 170;

        IntentEsCellOptimizationResultReq      intent_es_cell_optimization_result_req       = 171;
        IntentEsCellOptimizationResultRsp      intent_es_cell_optimization_result_rsp   = 172;

        IntentEsCellOptimizationResultRpt         intent_es_cell_optimization_result_rpt       = 173;
        IntentEsCellOptimizationResultRptRsp      intent_es_cell_optimization_result_rpt_rsp   = 174;


        IntentEsCoverageCellOptimizationReq         intent_es_coverage_cell_optimization_req       = 175;
        IntentEsCoverageCellOptimizationRsp         intent_es_coverage_cell_optimization_rsp       = 176;

        IntentEsCoverageCellOptimizationResultReq      intent_es_coverage_cell_optimization_result_req       = 177;
        IntentEsCoverageCellOptimizationResultRsp      intent_es_coverage_cell_optimization_result_rsp   = 178;

        IntentEsCoverageCellOptimizationResultRpt         intent_es_coverage_cell_optimization_result_rpt       = 179;
        IntentEsCoverageCellOptimizationResultRptRsp      intent_es_coverage_cell_optimization_result_rpt_rsp   = 180;

        IntentEsCoverageCellAdjustNotify                  intent_es_coverage_cell_adjust_notify       = 181;
        IntentEsCoverageCellAdjustNotifyRsp               intent_es_coverage_cell_adjust_notify_rsp   = 182;

        DiaRestartNotify                                  dia_restart_notify = 183;
        AwareNetworkSyncPolicyStateNotice                 aware_network_sync_policy_state_notice = 184;

        HsrCellComeTrainStateReq            hsr_cell_come_train_sate_req = 185;
        HsrCellComeTrainStateRsp            hsr_cell_come_train_sate_rsp = 186;

        KpiCollectUpdateRequest             kpi_collect_update_request               = 187;
        KpiCollectUpdateResponse            kpi_collect_update_response              = 188;
        ExtendKpiReportNotify               extend_kpi_report_notify                 = 189;
        AtuCellInfoNotify                   atu_cell_info_notify                     = 190;

        InterBBUXCDelayQueryRequest         inter_bbu_xc_delay_query_request        = 191;
        InterBBUXCDelayQueryResponse        inter_bbu_xc_delay_query_response       = 192;

        XcPrivateMsgTransferToNbrGnb        xc_private_msg_transfer_to_nbr_gnb      = 193;
        XcPrivateMsgTransferFromNbrGnb      xc_private_msg_transfer_from_nbr_gnb    = 194;

        IntentEsCellOptDecreaseReq         intent_es_cell_opt_decrease_req          = 195;
        IntentEsCellOptDecreaseRsp         intent_es_cell_opt_Decrease_rsp          = 196;

        SsbPowerAdjustConfigRequest        ssb_power_adjust_config_request       = 197;
        SsbPowerAdjustConfigUpdate         ssb_power_adjust_config_update        = 198;
        SsbPowerAdjustConfigUpdateAck      ssb_power_adjust_config_update_ack    = 199;

        CellUeMigratableRequest            cell_ue_migratable_request            = 200;
        CellUeMigratableResponse           cell_ue_migratable_response           = 201;

        SsbPowerAdjustLoadRequest          ssb_power_adjust_load_request         = 202;
        SsbPowerAdjustLoadResponse         ssb_power_adjust_load_response        = 203;

        SsbPowerAdjustEvaluateRequest      ssb_power_adjust_evaluate_request     = 204;
        SsbPowerAdjustEvaluateResponse     ssb_power_adjust_evaluate_response    = 205;
        SsbPowerAdjustEvaluateReport       ssb_power_adjust_evaluate_report      = 206;

        CounterCollectRequest              counter_collect_request               = 207;
        CounterCollectResponse             counter_collect_response              = 208;
        CounterReportNotify                counter_report_notify                 = 209;
        ExtendCounterReportNotify          extend_counter_report_notify          = 210;
        CounterCollectUpdateRequest        counter_collect_update_request        = 211;
        CounterCollectUpdateResponse       counter_collect_update_response       = 212;

        CuKpiCollectResetNotify            cu_kpi_collect_reset_notify           = 213;

        CellOperationOnlyWithNcgi          cell_ssb_frequency_query_request      = 214;
        CellSsbFrequencyQueryResponse      cell_ssb_frequency_query_response     = 215;

        MultiBlerNbrIfEvaluate            multi_bler_nbr_if_evaluate             = 216;
        MultiBlerNbrIfEvaluateRsp         multi_bler_nbr_if_evaluate_rsp         = 217;
        LteUePowerInfo                    lte_ue_power_info                      = 218;
        HighWayNeighbourCellResourceNotify  high_way_neighbour_cell_resource_notify    = 219;
        HighWayServedCellListResourceNotify high_way_served_cell_list_resource_notify  = 220;

        BwpLoadBalanceStateNotify          bwp_load_balance_state_notify         = 221;

        BwpLoadBalanceStateRequest         bwp_load_balance_state_request        = 222;
        BwpLoadBalanceStateResponse        bwp_load_balance_state_response       = 223;

        IndoorOutdoorNetworkIdentifyTransfer indoor_outdoor_network_identify_transfer    = 224;

        CUCellESStateQueryRequest          cu_cell_es_state_query_request        = 225;
        CUCellESStateQueryResponse         cu_cell_es_state_query_response       = 226;
        MeasResultNotify                   meas_result_notify                    = 227;

        RachInterferAvoidNotify            rach_interfer_avoid_notify            = 228;
        CuCellNoExistNotify                cu_cell_no_exist_notify               = 229;

        HsrServCellInfoRequest             hsr_serv_cell_info_request            = 230;
        HsrServCellInfoResponse            hsr_serv_cell_info_response           = 231;
        HsrIntraNbrCellInfoRequest         hsr_intra_nbr_cell_info_request       = 232;
        HsrIntraNbrCellInfoResponse        hsr_intra_nbr_cell_info_response      = 233;

        EsParamOptDecreaseNotify        es_param_opt_decrease_notify          = 234;
        EsParamOptDecreaseNotifyAck     es_param_opt_decrease_notify_ack          = 235;
        UeUdtTraceStart                 ue_udt_trace_start                       = 236;

        UESEInfoQueryReq                        ue_se_info_query_req             = 237;
        UESEInfoQueryRsq                        ue_se_info_query_rsq             = 238;
        EdgeUserInfoNotify                      edge_user_info_notify            = 239;
        UeInformationNotify                 ue_information_notify                = 240; //CPA，用于CU、DU间传递UE相关信息
        SleepCellCoGnbInfoQueryReq          sleep_cell_co_gnb_query_req             = 241;
        SleepCellCoGnbInfoQueryRsp          sleep_cell_co_gnb_query_rsp             = 242;
        ZeroUserSleepCellEvaluateNotify     zero_user_sleep_cell_evaluate_notify    = 243;
        ZeroUserSleepCellEvaluateResult     zero_user_sleep_cell_evaluate_result    = 244;
        CellActiveBwpStatusRequest          cell_active_bwp_status_request          = 245;
        CellActiveBwpStatusResponse         cell_active_bwp_status_response         = 246;
        CellActiveBwpStatusNotify           cell_active_bwp_status_notify           = 247;
        HighwayCoordinateCellConfigRequest   highway_coordinate_cell_config_request   = 248;
        HighwayCoordinateCellConfigResponse  highway_coordinate_cell_config_response  = 249;

        NrPhyCellDuCfgRequest                nr_phycell_du_cfg_request          = 250;
        NrPhyCellDuCfgResponse               nr_phycell_du_cfg_response         = 251;
        NrPhyCellDuCfgNotify                 nr_phycell_du_cfg_notify           = 252;
        UeInfDetectionNotify                 ue_inf_detection_notify            = 253;
        HighWayUeMeasResultNotify            highway_ue_meas_result_notify      = 254;
        UeMainTrafficDirectRequest           ue_main_traffic_direct_request     = 255;
        UeMainTrafficDirectResponse          ue_main_traffic_direct_response    = 256;
        UeMainTrafficDirectReport            ue_main_traffic_direct_report      = 257;
        ZeroFlowSleepCellLoadDetectReq      zero_flow_sleep_cell_load_detect_req    = 258;
        ZeroFlowSleepCellLoadDetectRsp      zero_flow_sleep_cell_load_detect_rsp    = 259;
        ZeroFlowSleepCellEvaluateReq        zero_flow_sleep_cell_evaluate_req       = 260;
        ZeroFlowSleepCellEvaluateRsp        zero_flow_sleep_cell_evaluate_rsp       = 261;

        SleepCellDetectNotify               sleep_cell_detect_notify                = 262;
        SleepCellResultNotify               sleep_cell_result_notify                = 263;
        XnLinkChooseRequest                 xn_link_choose_request                  = 264;
        XnLinkChooseResponse                xn_link_choose_response                 = 265;
        XnLinkStatusChangeNotify            xn_link_status_change_notify            = 266;
        NrPrivateMessageTransfer      nr_private_message_transfer_from_inter_bbu    = 267;
        NrPrivateMessageTransfer      nr_private_message_transfer_to_inter_bbu      = 268;

        XcNbrStatisticRequest                xc_nbr_statistic_request               = 269;
        XcNbrStatisticResponse               xc_nbr_statistic_response              = 270;
        XcNbrRelationRequest                 xc_nbr_relation_request                = 271;
        XcNbrRelationResponse                xc_nbr_relation_response               = 272;
        XcNbrRelationSync                    xc_nbr_relation_sync                   = 273;
        CuXcStatisticContainerResetNotify    cu_xc_statistic_container_reset_notify = 274;
        InterRackXcSetupRequest              inter_rack_xc_setup_request            = 275;
        InterRackXcSetupResponse             inter_rack_xc_setup_response           = 276;
        NeighborCellInfoNotify               neighbor_cell_Info_notify              = 277; //cu->du
        CoEquipmentIdentifyRequest           co_equipment_identify_request          = 278;
        CoEquipmentIdentifyResponse          co_equipment_identify_response         = 279;
        UeLargeTAIdentityRequest             ue_large_ta_identity_request           = 280;
        UeLargeTAIdentityResponse            ue_large_ta_identity_response          = 281;
        UeTaStatusReport                     ue_ta_status_report                    = 282;

        SleepCellConfigNotify                sleep_cell_config_notify               = 283;
        SleepCellHisKpiDataReq               sleep_cell_his_kpi_data_req            = 284;
        SleepCellHisKpiDataRsp               sleep_cell_his_kpi_data_rsp            = 285;
        GeneralUeNotification                general_ue_notification                = 286;
        NormalUeResultNotification           normal_ue_result_notify                = 287;
        CuCapabilityProxy                    cu_capability_proxy                    = 288;
        ScellDelIndication                   scell_del_indication                   = 289;
        IntentEsCentraOptResultNotify        intent_es_centra_opt_result_notify     = 290;
        IntentEsCentraOptResultNotifyAck     intent_es_centra_opt_result_notify_ack = 291;
        SameAAUNRCellAtuNotify               same_aau_nrcell_atu_notify             = 292;
        HotStandbyStateIndication            hot_standby_state_indication           = 293;

        DuKpiEvalRegReq                      kpi_eval_reg_req                       = 294;
        DuKpiEvalRegRsp                      kpi_eval_reg_rsp                       = 295;
        DuKpiEvalReq                         kpi_eval_req                           = 296;
        DuKpiEvalRsp                         kpi_eval_rsp                           = 297;
        DuKpiPeriodEvalStopReq               kpi_period_eval_stop_req               = 298;
        DuKpiPeriodEvalStopRsp               kpi_period_eval_stop_rsp               = 299;

        PositioningDeactivationAck           positioning_deactivation_ack           = 300;
        InactivePosSrsReleaseIndication      inactive_pos_srs_release_indication    = 301;
        HighSpeedTrainContainer              high_speed_train_container             = 302;
        UeInformationNotifyToCU              ue_information_notify_to_cu            = 303;

        HrrmToLrrmServiceReq                 hrrm_to_lrrm_service_req               = 304;
        HrrmToLrrmServiceRsp                 hrrm_to_lrrm_service_rsp               = 305;
        UnionSpaMeasConfigNotification       union_spa_meas_config_notification     = 306;
        UnionSpaMeasResultNotify             union_spa_meas_result_notify           = 307;

        CompSCTaskRequest                    comp_sc_task_request                       = 308;
        CompSCTaskResponse                   comp_sc_task_response                      = 309;
        CompSCCellCollaborationInfoRequest   comp_sc_cell_collaboration_info_request    = 310;
        CompSCCellCollaborationInfoResponse  comp_sc_cell_collaboration_info_response   = 311;
        CompSCDuContainerRestartNotify       comp_sc_du_container_restart_notify        = 312;
        CompSCCarrierStateModNotify          comp_sc_carrier_state_mod_notify           = 313;
        HotStandbyStateConfirm               hot_standby_state_confirm                  = 314;
        AccessAllowAcknowledge               access_allow_acknowledge                   = 315;
        CuLoadDetectRequest                  cu_load_detect_request                 = 316;
        CuLoadDetectResponse                 cu_load_detect_response                = 317;
        CuLoadDetectDataReport               cu_load_detect_report                  = 318;
        CuLoadManagerResetNotify             cu_load_manageer_reset_notify          = 319;

        SleepCellDetectMetricsReq            sleep_cell_detect_metrics_req          = 320;
        NcrUeInfoDlTransfer                  ncr_ue_Info_dl_transfer                = 321;
        NbnSmartHandoverRequest              nbn_smart_handover_request             = 322;
        NbnSmartHandoverResponse             nbn_smart_handover_response            = 323;

        PrbOptCounterCollectReq              prb_opt_counter_collect_req            = 324;
        PrbOptCounterCollectRsp              prb_opt_counter_collect_rsp            = 325;
        PrbOptCounterSync                    prb_opt_counter_sync                   = 326;
        PrbOptDuContainerRestartNotify       prb_opt_du_container_restart_notify    = 327;
        PrbOptCuContainerRestartNotify       prb_opt_cu_container_restart_notify    = 328;

        SentinelPrruSCNotify                 sentinel_prru_sc_notify                = 329;
        SentinelPrruSCReq                    sentinel_prru_sc_req                   = 330;
        SentinelPrruSCRsp                    sentinel_prru_sc_rsp                   = 331;
        SentinelPrruSCHandoverNotify         sentinel_prru_sc_handover_notify       = 332;

        SleepCellIdentifyResult              sleep_cell_identify_result             = 333;

        HsrCellWatchDogPenaltyStateChangeNotify hsr_cell_watch_dog_penalty_state_notify = 334;
        NtnUeGpsInfoNotify                  ntn_ue_gps_info_notify                     = 335;
        CU2DUDssCellInfo                    cu2du_dss_cell_info                     = 336;
        NRMultiModelEsOptReq              nr_multi_model_es_opt_req           = 337;
        NRMultiModelEsOptRsp              nr_multi_model_es_opt_rsp           = 338;

        XcBusinessStatisticReq              xc_business_statistic_req               = 339;
        XcBusinessStatisticRsp              xc_business_statistic_rsp               = 340;
        XcInterGnbQuery                     xc_inter_gnb_query                      = 341;
        XcInterGnbRsp                       xc_inter_gnb_rsp                        = 342;
        XcCaStatisticsNotify                xc_ca_statistics_notify                 = 343;
        SleepCellDetectNotifyToPhy          sleep_cell_detect_notify_to_phy         = 344;
        SleepCellResultNotifyToPhy          sleep_cell_result_notify_to_phy         = 345;

        PrecisionPrruEsDrxStateRequest      precision_prru_es_drx_state_request     = 346; //CU->DU
        PrecisionPrruEsDrxStateResponse     precision_prru_es_drx_state_response    = 347; //DU->CU
        PrecisionPrruEsDrxStateNotify       precision_prru_es_drx_state_notify      = 348; //DU->CU

    }
}

message MobilityQualityHoContainer
{
    message PlmnLevelSwch
    {
        Plmn          plmn                   = 1;
        uint32        switch                 = 2;  // 0--close;1--open
        uint32        indoor_outdoor_coordination_switch = 3;  // 0--close;1--open
    }

    message BlockThrdBasedTraffic
    {
        uint32    ue_type_based_traffic          = 1;// 0--UlPrimarySevUE; 1--DlPrimarySevUE; 2--OtherUE
        uint32    ul_sev_blk_thrd                = 2; //上行重度堵塞门限
        uint32    ul_mild_blk_thrd               = 3; //上行中度堵塞门限
    }
    message DataQualityMigrateReq
    {
        Ncgi ncgi                                = 1;
        repeated PlmnLevelSwch   plmn_level_swch = 2;
        uint32    dl_detect_ind                  = 3;  // 0--不检测下行;1--检测下行
        uint32    ul_service_block_ind           = 4;
        uint32    ul_data_blk_num_thrd           = 8;
        uint32    ul_ext_data_blk_num_thrd       = 9;
        uint32     req_serial_number             = 13;    // 请求的标识，需要在response中携带回cu，cu进行校验。
        uint32    ul_qual_judge_based_traffic_stgy              = 14;  // 0--no; 1---yes
        repeated BlockThrdBasedTraffic block_thrd_based_traffic = 15;
        uint32    qual_eva_based_qos_charac                     = 16;  // 0--close; 1---open
    }
    message DataQualityMigrateRsp
    {
        Ncgi      ncgi                           = 1;
        uint32    result                         = 2; // 0--success;1--fail
        uint32    req_serial_number              = 3;    // 将请求中的标识，反填回response中携带回cu，cu进行校验。
    }

    message OneUeInfo
    {
        UeF1apIdPair    ue_id            = 1;
        uint32          dl_quality       = 2; //0-good; 1-poor
        uint32          ul_quality       = 3; //0-good; 1-poor
        google.protobuf.UInt32Value     ul_poor_quality_type  = 4;// 0--轻度质差，1--中度质差，2--重度质差;仅在ul_quality为1-poor时携带该字段
        google.protobuf.Int32Value      ul_single_rb_sinr_diff_based_ue_traffic     = 5; //ue主业务类型的质差类型门限与singleRbSinr之差;仅在ul_quality为1-poor时携带该字段
        google.protobuf.Int32Value      ul_single_rb_sinr_diff_based_other_traffic  = 6; //other业务类型的重度质差类型门限与singleRbSinr之差;仅在ul_quality为1-poor且主业务类型为ul业务时携带该字段
        google.protobuf.Int32Value      ul_single_rb_sinr            = 7;  //上行SingleRbSinr,上报值为实际值扩大4倍，无效值为0x7FFF
        google.protobuf.UInt32Value     dl_channel_evaluation_se     = 8;  //下行SE,上报值为实际值扩大10000倍，无效值为0xFFFFFFFF
        google.protobuf.UInt32Value     main_traffic_direction       = 9; /* 主要业务方向：0--ul，1--dl，2--other */
        uint32     quality_rpt_ind                                   = 10; //从左往右，第一位bit标志PlmnLevelSwch.switch使能上报，第二位bit标志  PlmnLevelSwch.indoor_outdoor_coordination_switch 使能上报 。
    }
    message UeQualityInfo
    {
        Ncgi ncgi                                      = 1;
        repeated OneUeInfo one_ue_info                 = 2;
    }

    message NsaPlmnLevelSwch
    {
        message UlServiceBlockInfo
        {
            uint32      ul_service_block_ind           = 1;
            uint32      ul_sev_blk_thrd                = 2;
            uint32      ul_data_blk_num_thrd           = 3;
            uint32      ul_ext_data_blk_num_thrd       = 4;

        }
        Plmn                               plmn                     = 1;
        google.protobuf.UInt32Value        nsa_del_switch           = 2;  // 0--close;1--open;  如果不携带，不处理SN 删除上行
        google.protobuf.UInt32Value        nsa_change_switch        = 3;  // 0--close;1--open;  如果不携带，不处理SN PScellChange上行
        google.protobuf.UInt32Value        nsa_del_dl_switch        = 4;  // 0--close;1--open;  如果不携带，不处理SN 删除下行
        google.protobuf.UInt32Value        nsa_change_dl_switch     = 5;  // 0--close;1--open;  如果不携带，不处理SN PScellChange下行
        UlServiceBlockInfo                 ul_service_block_info    = 6; // 仅在SN PScellChange上行时携带

    }

    message NsaDataQualityMigrateReq
    {
        Ncgi     ncgi                                = 1;
        repeated NsaPlmnLevelSwch   plmn_level_swch  = 2;
    }

    message NsaDataQualityMigrateRsp
    {
        Ncgi                               ncgi                 = 1;
        google.protobuf.UInt32Value        nsa_del_result       = 2;   //0--success, 1--fail, 2--noCfgPara; 如果不携带，不处理该任务
        google.protobuf.UInt32Value        nsa_change_result    = 3;   //0--success, 1--fail, 2--noCfgPara; 如果不携带，不处理该任务
    }

    message OneNsaUeInfo
    {
        UeF1apIdPair    ue_id            = 1;
        uint32          ul_quality       = 2; //0-good; 1-poor
        uint32          dl_quality       = 3; //0-good; 1-poor
    }

    message NsaUeQualityInfo
    {
        Ncgi                    ncgi                   = 1;
        repeated OneNsaUeInfo   one_ho_ue_info         = 2;
        repeated OneNsaUeInfo   one_del_ue_info        = 3;
    }

    oneof Messages
    {
        DataQualityMigrateReq              data_quality_migrate_req        = 1;  // cu to du
        DataQualityMigrateRsp              data_quality_migrate_rsp        = 2;  // du to cu
        UeQualityInfo                      ue_quality_info                 = 3;  // du to cu
        NsaDataQualityMigrateReq           nsa_data_quality_migrate_req    = 4;  // cu to du
        NsaDataQualityMigrateRsp           nsa_data_quality_migrate_rsp    = 5;  // du to cu
        NsaUeQualityInfo                   nsa_ue_quality_info             = 6;  // du to cu
    }
}
```

- 接口层级关系：
Message->PrivateMsg->MobilityQualityHoContainer->Messages->data_quality_migrate_rsp->req_serial_number

- 相关构造函数：
string getCf1DataQualRsp(U32 cellId, U32 gnbId, U32 result)
{
    json rsp = nlohmann::json::parse(DataQualRsp);
    auto& dataQualRsp = rsp["private_msg"]["mobility_quality_ho_container"]["data_quality_migrate_rsp"];
    auto& ncgi = dataQualRsp["ncgi"];
    ncgi["gnb_id"] = gnbId;
    ncgi["cell_id"] = cellId;
    ncgi["gnb_id_length"] = 24;
    ncgi["plmn"]["mnc"] = (gnbId == Identity::GNB_100)? "3031":"303233";
    dataQualRsp["result"] = result;
    NRcgi nrcgi(gnbId, cellId, (gnbId == Identity::GNB_100)?uc_drm::lm::model::Plmn("460", "01", 2):uc_drm::lm::model::Plmn("460", "023", 3), 24);
    auto* moNrCell = uc_drm::lm::lmDataRepo().getMoNrCell(nrcgi);
    U32 serialNumber = moNrCell->getDataQualityMigrateReqSerialNumber();
    dataQualRsp["req_serial_number"] = serialNumber;
    return rsp.dump(4);
}

- 生成代码：
```cpp
auto rsp = getCf1DataQualRsp(CELL_1, GNB_100);
json rspJson = nlohmann::json::parse(rsp);
rspJson["private_msg"]["mobility_quality_ho_container"]["data_quality_migrate_rsp"]["req_serial_number"] = INVALID_U32;
servLm <---- fakeCf1 (S_CF1M_MULTICAST, getSingleInstKey(DU_ID_1), (uc_drm::MOBILITY_QUAL_HO_CONTAINER),
        CF1DataQualReqBuilder(rspJson.dump(4).c_str()));
```


## STEP5：接纳消息配置
- 接纳消息是指构造是指uc向drm模块发送的接纳请求，drm进行处理后回复响应的相关接口和内容，其中包括下发测量，测量上报等。
### 示例：
- 用例描述：DRM收到NcrMeasAdmitRequest，携带：
radioMode == 16：16：LTE_FDD
cellStats == 0:正常
NcrFreqbandCapablity：1830-1880

- 相关接口：
```proto
message Message
{
    MsgHeader msg_header = 1;

    oneof Messages
    {
        NcrMeasAdmitRequest                ncr_meas_admit_request                         = 332;
        NcrMeasAdmitResponse               ncr_meas_admit_response                        = 333;

    }
}

message NcrMeasAdmitRequest
{
    UeCapContext    ue_cap_context                     = 1;
    Plmn            serving_plmn                       = 2;
    UeMeasContext   ue_meas_context                    = 3;
    uint32          radio_mode                         = 4; /*1：UMTS;16：LTE_FDD;32：LTE_TDD;8192：5GNR（不区分TDD/FDD）*/
    uint32          cell_stats                         = 5; /*0:正常；1：异常*/
    NcrFreqbandCapablity ncr_freqband_capablity        = 6;
    NcrWorkFreqband      ncr_work_freqband             = 7;


    message NcrFreqbandCapablity
    {
        uint32          freq_range_dl_start        = 1; /*NCR 当前频段支持的起始频点，单位KHz*/
        uint32          freq_range_dl_end          = 2; /*NCR 当前频段支持的结束频点，单位KHz*/
        uint32          freq_band                  = 3; /*NCR 频段指示*/
    }


    message NcrWorkFreqband
    {
        uint32          ul_centrol_freq                      = 1; /* NCR 工作带宽信息,单位KHZ，如3MHz带宽填3000，依次类推 */
        uint32          dl_centrol_freq                      = 2; /* NCR 工作带宽信息,单位KHZ，如3MHz带宽填3000，依次类推 */
        uint32          band_width                           = 3; /* NCR 工作带宽信息,单位KHZ，如3MHz带宽填3000，依次类推 */
    }
}
```

- 接口层级关系：
Message->Messages->NcrMeasAdmitRequest->radio_mode
Message->Messages->NcrMeasAdmitRequest->cell_stats
Message->Messages->NcrMeasAdmitRequest->NcrFreqbandCapablity

- 相关构造函数：
```cpp
constexpr auto ncrAdmitMeasReq = R"(
{
    "msg_header":{
        "cell_id":1,
        "cpf_ue_id":0,
        "gnb_id":100,
        "gnb_id_length":24,
        "plmn":{
            "mcc":"343630",
            "mnc":"3031"
        },
        "ue_data":{"atu_flag":{"value":false}}
    },
    "ncr_meas_admit_request":{
        "serving_plmn":{
              "mcc":"343630",
              "mnc":"3031"
        },
        "ue_cap_context":{},
        "radio_mode":16,
        "cell_stats":0

    }
}
)";
```
- 生成代码：
```cpp
auto req = nlohmann::json::parse(ncrAdmitMeasReq);
req["ncr_meas_admit_request"]["radio_mode"] = 16;
req["ncr_meas_admit_request"]["cell_stats"] = 0;
req["ncr_meas_admit_request"]["ncr_freqband_capablity"]["freq_range_dl_start"] = 1830;
req["ncr_meas_admit_request"]["ncr_freqband_capablity"]["freq_range_dl_end"] = 1880;
req["ncr_meas_admit_request"]["ncr_freqband_capablity"]["freq_band"] = 3;
```


## STEP6：校验接纳响应
- 校验接纳响应是指，drm对接纳消息处理后回复的响应内容进行校验。
### 示例：
- 用例描述：DRM发送NcrMeasAdmitResponse，输出meas_reconfig，先清除测量，再下发测量：

- 相关接口
```proto
message Message
{
    MsgHeader msg_header = 1;

    oneof Messages
    {
        NcrMeasAdmitRequest                ncr_meas_admit_request                         = 332;
        NcrMeasAdmitResponse               ncr_meas_admit_response                        = 333;

    }
}

message NcrMeasAdmitResponse
{
    oneof action
    {
        NoOutPut               no_output          = 1;
        MeasReconfig           meas_reconfig      = 2;
    }
    UeCapabilityContext ue_capability_context  = 3;
}

message MeasReconfig
{
    message GapInd
    {
        bytes f1_meas_config = 1;
        bytes f1_sftd_config = 2;
        NeedForGapsInfoNr  need_for_gaps_info_nr    = 3;
        bytes f1_location_measurement_info          = 4;
    }

    message AtuIntraFInterferenceMeasInd
    {
        enum Type
        {
            release = 0;
            setup   = 1;
        }
        Type value = 1;
    }

    message SonOverLapCoverageMeasInd
    {
        enum Type
        {
            release = 0;
            setup   = 1;
        }
        Type value = 1;
    }

    message UlPdcpMeasInd
    {
        enum Type
        {
            del = 0;//删除UE的时延测量
            start   = 1;//启动UE的时延测量
        }
        Type value = 1;
    }

    bytes          meas_config_buffer  = 1;
    uint32         result              = 2;
    GapInd         gap_ind             = 3;
    google.protobuf.UInt32Value serving_cell_mo = 4;//PCELL or PSCELL OBJ ID
    NsaGapInd      nsa_gap_ind         = 5;
    ScellMos       scell_mos           = 6; //Xn、Ng、IntraDuHo、Pscell Change、SN Change场景当主小区切换带着辅小区一起切时，或者resume恢复辅载波时，A2和初始测量一起下，ServingCellMo需要和A2一起下。
    AtuIntraFInterferenceMeasInd  atu_intrafreq_interference_meas_ind = 7;
    SonOverLapCoverageMeasInd  son_overlap_coverage_meas_ind = 8;
    UlPdcpMeasInd     ul_pdcp_meas_ind        = 9;
    google.protobuf.BoolValue    cbf_ue_a4_meas_flag  = 10; //true：配置A4测量，false删除A4测量
    NonCdSsbMeasResult   non_cd_ssb_meas_result       = 11;//uc保存在稳态，后续移动性携带
    google.protobuf.BoolValue scell_non_cd_ssb_result_flag = 12; // 有携带且为true，则不重配du
    google.protobuf.BoolValue   has_freq_priority_meas     =13;  // 有携带且为true，则测量中包含频点优先级测量
    DataQualMeasInfo            data_qual_meas_info        = 14;
    NcdServingCellMo            ncd_serving_cell_mo        = 15;//仅redcap终端携带
}

message UeCapabilityContext
{
	message UeNrCapabilityContext
	{
		bool            handover_lte                        = 1;
		bool            inactive_state_supported            = 2;  //UE_NR_Capability.nonCriticalExtension.inactiveState = 0时，该字段填写为true，否则为false;
		bool            logged_meas_bt                      = 3;
		bool            logged_measurements                 = 4;
		bool            logged_meas_wlan                    = 5;
		uint32          band_combination_list_v1560_size    = 6; //repeated BandCombinationV1560 band_combination_list_v1560 size
        AccessStratumRelease  access_stratum_release        = 7;
        bool            rach_report_r16                     = 8;
        bool            support_of_redcap_r17               = 9;
        bool            support_ul_rrc_segment              = 10;
        bool            support_nrdc                        = 11;
        bool            nonTerrestrialNetwork_r17           = 12;
        bool            support_of_eredcap_r18              = 13;
	}

    UeNrCapabilityContext ue_nr_capability_context      = 1;
    UeCaSupportCapa        ue_ca_support_capa           = 2;
    repeated UeSpecFuncInfo ue_spec_func_info_list      = 3; // F1口需要携带的特殊业务功能信息
    SpecUeTypeIdList        spec_ue_type_id_list        = 4; // 根据配置匹配到的所有specUeTypeId
    UeCaSupportCapa        ue_ca_support_band_capa      = 5; // 具备CA协同关系的频段的CA能力
    SpecUeTypeIdList       bwp_ho_ue_type_id_list       = 6;//待删除
    UePagingCapa           ue_paging_capa               = 7;
    google.protobuf.UInt32Value  ue_nrcap_hashid        = 8; // 根据UE的NR能力码流映射生成的hashid，基站内只第一次获取到NR 能力时生成
}
```
- 相关构造函数
无

- 生成代码：
```cpp
struct NcrMeasAdmitRspMeasAssert: AssertPbMsg
{
    NcrMeasAdmitRspMeasAssert(std::vector<U32> removedMeasIdList, std::vector<U32> addMeasIdList)
            : removedMeasIdList(removedMeasIdList), addMeasIdList(addMeasIdList){}
    void doAssert();
    NcrMeasAdmitRspMeasAssert &operator()()
    {
        return *this;
    }
    std::vector<U32> removedMeasIdList{};
    std::vector<U32> addMeasIdList{};
};

void NcrMeasAdmitRspMeasAssert::doAssert()
{
    ASSERT_TRUE(actualMsg.has_ncr_meas_admit_response());
    auto rsp = actualMsg.ncr_meas_admit_response();

    USI_MEM_SET_PEEK(measCfg);
    USI_MEM_SET_PEEK(buff);
    auto measConfig_option = rsp.meas_reconfig().meas_config_buffer();
    Decoder decoder(measConfig_option.c_str(), measConfig_option.length(), buff, MAX_DCCH_MSG_LENGTH);
    decoder.decode(PerDeMeasConfig, measCfg);
    ASSERT_TRUE(!removedMeasIdList.empty() == measCfg.tOptFlags.measIdToRemoveList_optionPresent);
    ASSERT_TRUE(!addMeasIdList.empty() == measCfg.tOptFlags.measIdToAddModList_optionPresent);
    if(!removedMeasIdList.empty())
    {
        ASSERT_TRUE(measCfg.tOptFlags.measIdToRemoveList_optionPresent == 1);
        ASSERT_TRUE(measCfg.measIdToRemoveList_option.n == removedMeasIdList.size());
    }
    if(!addMeasIdList.empty())
    {
        ASSERT_TRUE(getDeCodeMeasCfg().report_configs(0).purpose() == p2s::drm::ncr_inter_rat_meas);
        ASSERT_TRUE(measCfg.measIdToAddModList_option.n == addMeasIdList.size());
        ASSERT_TRUE(measCfg.tOptFlags.measObjectToAddModList_optionPresent == 1);
        ASSERT_TRUE(measCfg.tOptFlags.reportConfigToAddModList_optionPresent == 1);
        ASSERT_TRUE(measCfg.measObjectToAddModList_option.elem[0].measObject.u.measObjectEUTRA->tOptFlags.cellsToAddModListEUTRAN_optionPresent == 0);

    }
}
```



# 输出格式
- 测试用例的命名必须按照TEST_F(测试套名称, RAN_{用例编号}_should_{测试内容}_when_{预置条件}_then_{测试步骤})进行命名，{测试步骤}、{预置条件}和{测试内容}采用小写驼峰式命名并且禁止出现中文。

例子: TEST_F(InitOrcheTest, RAN_12345678_should_attach_success_when_uecap_switch_is_open)

新增或修改的测试代码

- path:路径信息
- type:变更类型 add_test|modify_code|add_code
- step_describe:修改涉及步骤标识，例如["STEP1"]
```cpp
```
例如：
- path:ft/drm/lf/src/test/meas/handover/FtMigrateUeRequestTaBased.cpp
- type:modify_code
- step_describe:["STEP1"]
```cpp
完整的修改代码函数
```
# 输出要求
- 输出无关的代码不要做省略处理,你需要输出完整的函数片段
# 目标测试用例描述
## STEP1: 网管参数配置
### 用例需要配置参数列表
- 父节点路径:
ManagedElement.GNBCUCPFunction.MobilityGlobalConfig.HOFailPunish
- 父节点下参数配置:
```json
[
  {
    "ManagedElement.GNBCUCPFunction.MobilityGlobalConfig.HOFailPunish": {
      "hoPrepareFailPunishSwch": "1"
    }
  }
]
```
- 父节点路径:
ManagedElement.GNBCUCPFunction
- 父节点下参数配置:
```json
[
  {
    "ManagedElement.GNBCUCPFunction": {
      "reserved731": 1,
      "pLMNId": "plmn1"
    }
  }
]
```
- 父节点路径:
ManagedElement.GNBCUCPFunction.MobilityGlobalConfig.HOFailPunish.HOPrepareFailPunish
- 父节点下参数配置:
```json
[
  {
    "ManagedElement.GNBCUCPFunction.MobilityGlobalConfig.HOFailPunish.HOPrepareFailPunish": {
      "punishTimer": 65536
    }
  }
]
```
### 涉及参数详情
- 用例描述:
低速迁出.低速迁出
- 参数类型:
para
- 参数全路径:
ManagedElement.GNBCUCPFunction.NRCellCU.PublicPrivateNetworkMigCtrl.lowSpeedNCellMigSwch
- 参数详情:
```json
{
  "参数全路径": "ManagedElement.GNBCUCPFunction.NRCellCU.PublicPrivateNetworkMigCtrl.lowSpeedNCellMigSwch",
  "父节点和参数名": "PublicPrivateNetworkMigCtrl.lowSpeedNCellMigSwch",
  "参数名": "lowSpeedNCellMigSwch",
  "是否数组": "否",
  "数组元素个数": "",
  "参数中文名称": "低速用户向普通小区迁移开关",
  "参数英文名称": "Switch of migrating low-speed UEs to normal cells",
  "取值范围中文": "{0:关闭,1:全时段迁移策略,2:无车时段迁移策略}",
  "取值范围英文": "{0:Close,1:All time period migration policy,2:Migration policy in the period without high-speed trains}",
  "数据类型": "enum",
  "默认值": "0",
  "引用路径": "",
  "iszkey": "否",
  "取值类型补充说明（pattern正则）": "",
  "取值示例": "",
  "中文详解": "该参数是低速用户向普通小区触发迁移的功能开关。当参数配置为“2:无车时段迁移策略”时，高速小区在无车状态下对低速用户触发向普通小区的切换或重定向流程；当参数配置为“1:全时段迁移策略”时，高速小区在有车和无车状态下都会对低速用户触发向普通小区的切换或重定向流程；当参数配置为“0:关闭”时，高速小区对低速用户不进行特殊迁移处理。",
  "ManagedElement.GNBCUCPFunction.NRCellCU.PublicPrivateNetworkMigCtrl.lowSpeedNCellMigSwch": "低速用户向异频普通小区迁移执行方式",
  "para_type": "para",
  "用例描述": "低速迁出.低速迁出"
}
```
- 用例描述:
HOPrepareFailPunish信息.HOPrepareFailPunish信息
- 参数类型:
para
- 参数全路径:
ManagedElement.GNBCUCPFunction.MobilityGlobalConfig.HOFailPunish.hoPrepareFailPunishSwch
- 参数详情:
```json
{
  "参数全路径": "ManagedElement.GNBCUCPFunction.MobilityGlobalConfig.HOFailPunish.hoPrepareFailPunishSwch",
  "父节点和参数名": "HOFailPunish.hoPrepareFailPunishSwch",
  "参数名": "hoPrepareFailPunishSwch",
  "是否数组": "否",
  "数组元素个数": "",
  "参数中文名称": "切换准备失败惩罚开关",
  "参数英文名称": "Whether to enable punishment for handover preparation failures",
  "取值范围中文": "{0:关闭,1:打开}",
  "取值范围英文": "{0:Close, 1:Open}",
  "数据类型": "enum",
  "默认值": "1",
  "引用路径": "",
  "iszkey": "否",
  "取值类型补充说明（pattern正则）": "",
  "取值示例": "",
  "中文详解": "该参数是系统内系统间切换准备失败惩罚开关。当开关打开时，根据某个UE的目标小区在切换准备失败时携带的原因，对后续其他UE禁止向该目标小区进行切换/重定向；当开关关闭时，不会禁止其他UE向该切换准备失败的目标小区切换/重定向。",
  "ManagedElement.GNBCUCPFunction.MobilityGlobalConfig.HOFailPunish.hoPrepareFailPunishSwch": "HOPrepareFailPunish.punishTimer",
  "para_type": "para",
  "用例描述": "HOPrepareFailPunish信息.HOPrepareFailPunish信息"
}
```
- 用例描述:
reserved731.reserved731
- 参数类型:
para
- 参数全路径:
ManagedElement.GNBCUCPFunction.reserved731
- 参数详情:
```json
{
  "参数全路径": "ManagedElement.GNBCUCPFunction.reserved731",
  "父节点和参数名": "GNBCUCPFunction.reserved731",
  "参数名": "reserved731",
  "是否数组": "否",
  "数组元素个数": "",
  "参数中文名称": "保留参数731",
  "参数英文名称": "Reserved parameter 731",
  "取值范围中文": "[-32768..32767]",
  "取值范围英文": "[-32768..32767]",
  "数据类型": "int16",
  "默认值": "1",
  "引用路径": "",
  "iszkey": "否",
  "取值类型补充说明（pattern正则）": "",
  "取值示例": "",
  "中文详解": "该参数为基于GUAMI的切换目标小区准备失败惩罚功能开关，本参数在“切换准备失败惩罚开关（HOFailPunish.hoPrepareFailPunishSwch）”配置为“打开”时生效。当本参数配置为1时，基站针对携带“Unknown target ID”或“Handover failure in target 5GC/ NG-RAN node or target system”原因值的切换准备失败场景，可记录该失败过程中对应的原因值+PLMN+GUAMI信息，确定其对应的惩罚方式，启动后续对应相关UE的目标小区惩罚机制。当本参数配置为非1时，基站针对携带“Unknown target ID”或“Handover failure in target 5GC/ NG-RAN node or target system”原因值的切换准备失败场景，只记录该失败过程中对应的原因值+PLMN信息，确定其对应的惩罚方式，启动后续对应相关UE的目标小区惩罚机制。",
  "ManagedElement.GNBCUCPFunction.reserved731": "GNBCUCPFunction.reserved731",
  "para_type": "para",
  "用例描述": "reserved731.reserved731"
}
```
- 用例描述:
HOPrepareFailPunish.PunishInfoId信息
- 参数类型:
para
- 参数全路径:
ManagedElement.GNBCUCPFunction.MobilityGlobalConfig.HOFailPunish.HOPrepareFailPunish.punishTimer
- 参数详情:
```json
{
  "参数全路径": "ManagedElement.GNBCUCPFunction.MobilityGlobalConfig.HOFailPunish.HOPrepareFailPunish.punishTimer",
  "父节点和参数名": "HOPrepareFailPunish.punishTimer",
  "参数名": "punishTimer",
  "是否数组": "否",
  "数组元素个数": "",
  "参数中文名称": "惩罚定时器",
  "参数英文名称": "Punishment timer",
  "取值范围中文": "[1..65536]",
  "取值范围英文": "[1..65536]",
  "数据类型": "uint32",
  "默认值": "",
  "引用路径": "",
  "iszkey": "否",
  "取值类型补充说明（pattern正则）": "",
  "取值示例": "",
  "中文详解": "该参数是惩罚定时器，当某个UE向目标小区切换，发生切换准备失败时，若失败原因属于当前记录的“切换准备失败原因列表（HOPrepareFailPunish.hoPrepareFailCauseList）”，则会启动本参数的定时器，在定时器时长内后续其他同类UE按照“切换准备失败惩罚方式（HOPrepareFailPunish.hoPrepareFailPunishMethod）”对相应的邻区执行惩罚策略。当定时器超时后，会启动“深度惩罚检测定时器（HOPrepareFailPunish.deepDetectTimer）”。其中，取值65536表示定时器无穷大。上述中提到的“其他同类UE”的判定根据切换准备失败原因值不同，结果不同，例如：针对切换准备失败原因值为“Resources not available for the slice(s)”的场景，“其他同类UE”是指建立的切片仅包含切换失败UE的业务类型子集的UE。针对切换准备失败原因值为“Encryption And/Or Integrity Protection Algorithms Not Supported”的场景，“其他同类UE”是包含的安全能力仅是切换失败UE的安全算法能力子集的UE。",
  "ManagedElement.GNBCUCPFunction.MobilityGlobalConfig.HOFailPunish.HOPrepareFailPunish.punishTimer": "",
  "para_type": "para",
  "用例描述": "HOPrepareFailPunish.PunishInfoId信息"
}
```
- 用例描述:
plmn1
- 参数类型:
para
- 参数全路径:
ManagedElement.GNBCUCPFunction.pLMNId
- 参数详情:
```json
{
  "参数全路径": "ManagedElement.GNBCUCPFunction.pLMNId",
  "父节点和参数名": "GNBCUCPFunction.pLMNId",
  "参数名": "pLMNId",
  "是否数组": "否",
  "数组元素个数": "",
  "参数中文名称": "PLMN",
  "参数英文名称": "PLMN",
  "取值范围中文": "[6..7]",
  "取值范围英文": "[6..7]",
  "数据类型": "string",
  "默认值": "",
  "引用路径": "",
  "iszkey": "否",
  "取值类型补充说明（pattern正则）": "[0-9]{3,3}-[0-9]{2,3}",
  "取值示例": "",
  "中文详解": "该参数是用于配置运营商信息，由运营商的移动国家码和移动网络码组成。根据协议基站分为CU和DU两部分，该参数为基站CU参数。与之相对应的参数是GNBDUFunction.PLMN。",
  "ManagedElement.GNBCUCPFunction.pLMNId": "PLMN",
  "para_type": "para",
  "用例描述": "plmn1"
}
```
## STEP2: 邻区配置
- 邻区功能名称:异系统测量上报
- 异系统邻区配置数量:1
## STEP3: 测量对象配置
- 测量功能名称:异系统测量
- 异系统测量对象配置数量:1
## STEP4: 模块收发消息配置
无
## STEP5: 接纳请求消息配置
UE接入cell1，选择plmn1和guami1，低速迁出异系统测量上报cell3

接口路径: 5g_nr_v3/interface/Details/CPF/ucs/drm-interface.proto
```proto
message MeasurementReportDecisionRequest
{
    Plmn                         serving_plmn           = 1;
    bytes                        ue_meas_report         = 2;
    UeMeasContext                ue_meas_context        = 3;
    UeScellInfo                  ue_scell_info          = 4;
    cpf_common_if.HoRestricList  ho_restriction_list    = 5;//NSA填写
    UeType                       ue_type                = 6;
    Guami                        guami                  = 7; //废弃，通过消息头携带
    google.protobuf.UInt64Value  amf_ue_ng_ap_id        = 8;
    bytes                        ue_record_id           = 9; //废弃，通过消息头携带
    UeHistoryContext             ue_history_context     = 10;
    uint32                       du_ue_id               = 11;
    UeCapContext                 ue_cap_context         = 12;
    bytes                        meas_result_report_cgi = 13;//CG-ConfigInfo-v1540-IEs.measResultReportCG
    cpf_common_if.MobilityRestrictionList      mobility_restriction_list = 14;//SA填写
    Ecgi                         ue_mn_ecgi             = 15;//NSA填写
    google.protobuf.BoolValue    drive_test_ue          = 17;//携带该字段表示路测终端
    google.protobuf.Int32Value  redirection_for_voice_epsfb          = 18;
    DrmAdmitInfo                 admit_info             = 19;
    UeQosContext                 ue_qos_context         = 20;
    google.protobuf.UInt64Value  trace_id               = 21;
    uint32                      cho_current_user_number = 25;//UE当前驻留小区启动CHO的用户数，新启动CHO的UE携带，否则带无效值0xFFFFFFFF。
    repeated Ncgi               cho_success_candidate_cell = 26;//当前准备成功的CHO候选小区列表，如果没有不携带。
    PerceptionStatus            perception_status          = 27;
    UeGridTaskInfo               ue_grid_task_info         = 28; //UE的栅格相关任务信息
    GridInfo                     grid_info                 = 29;
    UeSecurityCapabilities       ue_security_capabilities  = 30;
    uint64                       ue_select_ind             = 31;
    google.protobuf.BoolValue   before_5qi1_setup          = 32;
    AggreModeChooseContext      aggre_mode_choose_context  = 33;
    google.protobuf.BoolValue    is_in_delay_ho            = 34;//是否处于延迟切换过程中
    google.protobuf.BoolValue   invite_receive_ind         = 35;   //HUC仅在UE为初始态，收到invite后，携带该字段。
    google.protobuf.BoolValue    is_vonr_ue                = 36; //指示是否VoNR用户,仅MR接纳优化场景携带
    UEAggregateMaximumBitRate    ue_aggregate_max_bit_rate = 37;
    MnServedCellInfo             mn_served_cell_info       = 38;//携带，就要判断同band
    repeated UeDrbQosTypeInfo    ue_drb_qos_type_info      = 39;
    google.protobuf.BoolValue    dl_se_predict_collect_swch    = 40;
    google.protobuf.BoolValue    is_ho_in_ue               = 41; //是否切换入的UE
    repeated  bytes              ue_ip                     = 42; //UE对应IP地址
    UeLargeSmallPacketInd        ue_large_small_packet_ind = 43;//删除
    UeCongestInd                 ue_congest_ind            = 44;
    MeNBServedCellInfo           menb_served_cell_info     = 45;
    ConfigRestrictInfoSCG        config_restrict_info      = 46;
    Ncgi                         ue_mn_ncgi                = 47;//NR-DC 填写
    AtuServiceTypeInfo           atu_service_type_info     = 48;
    uint32                       meas_id                   = 49;
}
```

测量上报类型：low_speed_interf_mig
```proto
enum RptPurpose
{
    intra_freq_ho                  = 0;
    intra_freq_pscell_change       = 1;
    pscell_release                 = 2;
    scell_release                  = 3;
    scell_add                      = 4;
    inter_freq_pscell_change       = 5;
    open_inter_freq_pscell_change  = 6;
    close_inter_freq_pscell_change = 7;
    report_cgi                     = 8;
    inter_freq_ho                  = 9;
    open_inter_freq_ho             = 10;
    close_inter_freq_ho            = 11;
    report_cmcc                    = 12;
    cell_release                   = 13;
    intra_freq_period_mr           = 14;
    cmcc_csi_rs                    = 15;
    epsfb_ho                       = 16;
    open_inter_rat_ho              = 17;
    close_inter_rat_ho             = 18;
    inter_rat_ho                   = 19;
    intra_freq_ho_csi_rs           = 20;
    load_balance                   = 21;
    inter_rat_period_mr            = 22;
    inter_freq_period_mr           = 23;
    inter_freq_cmcc                = 24;
    inter_freq_service_ho          = 25;
    open_interF_rat_ho             = 26;
    close_interF_rat_ho            = 27;
    blind_a2                       = 28;
    macro_macro_intra_freq_ho      = 29;
    micro_micro_intra_freq_ho      = 30;
    macro_micro_intra_freq_ho      = 31;
    aapc                           = 32;
    dl_weak_detection_a1           = 33;
    dl_weak_detection_a2           = 34;
    dl_weak_detection_init_a2      = 35;
    dl_comp                        = 36;
    ul_comp                        = 37;
    inter_freq_voice_qual          = 38;
    nsa_ping_pong_meas             = 39;
    lte_freq_service_ho            = 40;
    lte_freq_voice_qual            = 41;
    mmic                           = 42;
    close_ca_a4_meas               = 43; //已废弃，改用scell_add
    open_ca_a4_meas                = 44; //已废弃，改用scell_add
    vonr_forbid_ho                 = 45;
    report_ecgi                    = 46;
    scell_ho_a6                    = 47;
    emergency_epsfb_ho             = 48;
    ue_grid                        = 49;
    lte_freq_powerpilot_ho         = 50;
    ca_pcc_sel_ho                  = 51;
    intra_freq_pscell_change_rsrq  = 52;
    intra_freq_pscell_change_sinr  = 53;
    inter_freq_pscell_change_rsrq  = 54;
    inter_freq_pscell_change_sinr  = 55;
    open_inter_freq_pscell_change_rsrq  = 56;
    open_inter_freq_pscell_change_sinr  = 57;
    close_inter_freq_pscell_change_rsrq = 58;
    close_inter_freq_pscell_change_sinr = 59;
    pscell_release_rsrq                 = 60;
    pscell_release_sinr                 = 61;
    low_speed_intraf_mig = 62;
    low_speed_interf_mig = 63;
    low_speed_hcell_to_ncell_cov_ho = 64;
    lte_freq_data_qual_ho               = 65;
    intra_freq_powerpilot_ho            = 66;
    inter_freq_powerpilot_ho            = 67;
    ncgi_detect_period                  = 68; //新增ncgi_detect_period指示之前是否下过周期性测量
    pci_detect_ncgi                     = 69;
    lte_freq_dss_ho                     = 70;
    inter_freq_data_qual_ho             = 71;
    dl_quality_detection_a1             = 72;
    dl_quality_detection_a2             = 73;
    data_quality_ho_retry               = 74;
    intra_freq_load_balance             = 75;
    inter_freq_load_balance             = 76;
    power_control_ul                    = 77;
    intra_freq_interference_detection   = 78;
    cell_energy_saving                  = 79;
    nsa_inter_freq_data_qual_ho         = 80;
    nsa_data_quality_ho_retry           = 81;
    mn_sn_add                           = 82;
    mn_sn_del                           = 83;
    cell_release_inter_freq             = 84;
    cell_release_inter_rat              = 85;
    cell_energy_saving_inter_freq       = 86;
    cell_energy_saving_inter_rat        = 87;
    nrdc_pscell_release                 = 88;
    nrdc_intra_freq_pscell_change       = 89;
    epsfb_wait_voice_setup              = 90;
    epsfb_wait_better_cell              = 91;
    abnormal_open_inter_freq_ho         = 92;
    abnormal_open_inter_rat_ho          = 93;
    abnormal_open_interF_rat_ho         = 94;
    abnormal_blind_a2                   = 95;
    abnormal_dl_weak_detection_a2       = 96;
    abnormal_dl_quality_detection_a2    = 97;
    ul_pdcp_delay                = 98;          //QoS Monitor ul pdcp
    ca_add_hw_load                      = 99;
    nsa_intra_freq_load_balance         = 100;
    nsa_inter_freq_load_balance         = 101;
    inter_rat_congest_ctrl_mig          = 102;
    service_ho_detection_a1             = 103;
    service_ho_detection_a2             = 104;
    service_ho_delete_a1a2              = 105;
    service_ho_retry                    = 106;
    coveroverlap_intra_freq_period_mr   = 107;
    coveroverlap_inter_rat_period_mr    = 108;
    coveroverlap_inter_freq_period_mr   = 109;
    pdsch_cbf_dl                        = 110;
    intra_freq_start_cho                = 111;
    intra_freq_exec_cho                 = 112;
    nsa_dl_quality_detection_a1         = 113;
    nsa_dl_quality_detection_a2         = 114;
    pscell_release_based_dl_quality     = 115;
    intra_freq_period_a2                = 116;
    intra_freq_period_event_a2          = 117;
    high_speed_intraF_mig               = 118;
    high_speed_interF_mig               = 119;
    high_speed_interRat_mig             = 120;
    ue_grid_inter_freq                  = 121;
    ue_grid_inter_rat                   = 122;
    non_cd_ssb                          = 123;
    mdt_ul_pdcp_delay                   = 124;  //MDT ul pdcp
    mdt_qm_ul_pdcp_delay                = 125;  //MDT && QoS Monitor ul pdcp
    intra_freq_csirs_period_mr          = 126;
    vonr_bound_det                      = 127;
    interf_freq_ul_big_packet_ho        = 128;
    interf_freq_dl_big_packet_ho        = 129;
    large_pakect_ho_retry               = 130;
    cdt_intra_freq_period_mr            = 131;
    intra_freq_if_boundary_detection_a3 = 132;
    inter_freq_boundary_mig             = 133;
    lte_grid_ho                         = 134;
    nr_grid_ho                          = 135;
    ca_noncdssb_hw_load_retry           = 136;
    pingpong_sup_data_qual              = 137;
    pingpong_sup_voice_qual             = 138;
    pingpong_sup_public_private_network = 139;
    pingpong_sup_large_packet           = 140;
    pingpong_sup_ca_pcc                 = 141;
    intra_freq_mts_period_mr            = 142; //待删除
    spec_ue_inter_rat_mig               = 143;
    pci_confusion_ncgi_ho               = 144;
    pci_confusion_ecgi_ho               = 145;
    scell_del_interval                  = 146;
    scell_add_interval                  = 147;
    inter_freq_ncgi_detect_period       = 148;
    inter_freq_pci_detect_ncgi          = 149;
    intra_freq_interfrence_source_position      = 150;
    intra_freq_mr_interfrence_source_position   = 151;
    epsfb_detect_servcell_a3            = 152;
    epsfb_detect_servcell_a2_rsrp       = 153;
    epsfb_detect_servcell_a2_rsrq       = 154;
    epsfb_detect_servcell_a2_sinr       = 155;
    mn_sn_add_retry                     = 156;
    intra_freq_period_cooperate         = 157;
    ue_grid_event_meas                  = 158;
    invite_rd_weak_detection_a1         = 159;
    invite_rd_weak_detection_a2         = 160;
    invite_rd_weak_detection_init_a2    = 161;
    abnormal_invite_rd_weak_detection_a2= 162;
    gnb_version_upgrade                 = 163;
    gnb_version_upgrade_inter_freq      = 164;
    gnb_version_upgrade_inter_rat       = 165;
    lte_freq_ul_big_packet_ho           = 166;
    lte_freq_dl_big_packet_ho           = 167;
    aggre_mode_choose                   = 168;  //非测量purpose CcHo启动最优小区选择定时器
    multi_cc_ho                         = 169;  //NR-DC锚定切换测量purpose
    inter_freq_cc_ho                    = 170;
    inter_rat_cc_ho                     = 171;
    cc_ho_retry                         = 172;  //非测量purpose CcHo启动功能重试定时器
    nrdc_multi_cc_ho_retry              = 173;  //非测量purpose NR-DC锚定切换重试定时器
    interf_rfsp_mig                     = 174;
    rat_rfsp_mig                        = 175;
    pingpong_sup_rfsp_mig               = 176;
    rfsp_mig_retry                      = 177;
    dl_voice_quality_detect_a1          = 178;
    dl_voice_quality_detect_a2          = 179;
    dl_video_quality_detect_a1          = 180;
    dl_video_quality_detect_a2          = 181;
    abnormal_dl_voice_quality_detect_a2 = 182;
    abnormal_dl_video_quality_detect_a2 = 183;
    atu_freq_layer_early_identy         = 184;
    atu_freq_layer_meas_reconfg         = 185;
    ecid_measurement                    = 186;
    lte_freq_boundary_mig               = 187;
    spec_cell_or_nbr_normal_user_clear  = 188;
    spec_cell_normal_user_clear_interF  = 189;
    spec_cell_normal_user_clear_interRat= 190;
    far_position_disable_drx_a1         = 191;
    far_position_disable_drx_a2         = 192;
    abnormal_far_position_disable_drx_a2= 193;
    qostype_large_packet_detect         = 194;
    voice_release_return                = 195;
    dl_week_forbidden_VoNR_state        = 196;
	ecgi_detect_period                  = 197;
    pci_detect_ecgi                     = 198;
    interf_rfsp_hplmn_mig               = 199;
    rat_rfsp_hplmn_mig                  = 200;
    invite_rd_b1_meas                   = 201;
    invite_rd_b1_status_valid           = 202;
    intraf_interference_detect_a3       = 203;
    data_qual_mr_collection             = 204;
    scell_add_a5                        = 205;
    single_user_guarant_normal_user_clear  = 206;
    single_user_guarant_normal_user_clear_interF  = 207;
    single_user_guarant_normal_user_clear_interRat= 208;
    single_user_guarant_interference_detection = 209;
    macro_macro_intra_freq_ho_coord      = 210;
    micro_micro_intra_freq_ho_coord      = 211;
    outdoor_indoor_intra_freq_ho         = 212;
    large_packet_state_wait              = 213;
    large_packet_mr_collection           = 214;
    atu_sub_type_early_identy_ca         = 215;
    speed_test_interference_detection    = 216;
    sleep_cell_detect                    = 217;
    cell_plmn_reserved                   = 218;
    cell_plmn_reserved_inter_freq        = 219;
    cell_plmn_reserved_inter_rat         = 220;
    open_intra_freq_ho                   = 221;
    close_intra_freq_ho                  = 222;
    intraf_ssb_inf_detect                = 223;
    interf_ssb_inf_detect                = 224;
    ssb_inf_detect_retry                 = 225;
    highway_inter_freq_inf_meas          = 226;
    highway_intra_freq_inf_meas          = 227;
    unknown_pci_confusion_ecgi_meas      = 228;
    spec_cell_user_clear_mr_collection   = 229;
    large_packet_state_reset             = 230;
    voice_qual_mr_collection             = 231;
    cover_mobility_mr_collection         = 232;
    inter_freq_ta_based_mig              = 233;
    interRat_freq_ta_based_mig           = 234;
    spec_cell_normal_user_mig_back       = 235;//回迁定时器超时的purpose
    spec_cell_normal_user_mig_back_meas  = 236;//回迁测量定时器超时的purpose
    inter_freq_high_if_ue_mig            = 237;
	lte_freq_high_if_ue_mig              = 238;
    pingpong_sup_high_if_ue_mig          = 239;
    aggre_mode_timer_audit               = 240;
    dl_data_ch_quality_detect_a1         = 241; //新通话
    dl_data_ch_quality_detect_a2         = 242; //新通话
    abnormal_dl_data_ch_quality_detect_a2= 243; //新通话
    normal_ue_intra_freq_period_mr       = 244;
    flexible_orchestration_intraF_meas   = 245; //DRM算法编排
    flexible_orchestration_interF_meas   = 246; //DRM算法编排
    flexible_orchestration_interRat_meas = 247; //DRM算法编排
    inter_freq_beamforming_ue_mig        = 248;
    lte_freq_beamforming_ue_mig          = 249;
    flexible_orchestration_mig_meas_retry= 250;
    dl_large_packet_keep_timeout_ca      = 251;//下行大包保持定时器CA
    dl_large_packet_keep_timeout_nonca   = 252;//下行大包保持定时器non CA
    nrdc_sn_inter_freq_pscell_change     = 253;
    open_nrdc_sn_inter_freq_pscell_change  = 254;
    close_nrdc_sn_inter_freq_pscell_change = 255;
    scell_add_retry                        = 256;
    ul_large_packet_keep_timeout_ca        = 257;//上行大包保持定时器CA
    ul_large_packet_keep_timeout_nonca     = 258;//上行大包保持定时器non CA
    sul_add_a5                             = 259;
    atu_aggre_mode_select_meas_a2          = 260;  //A2测量触发ATU聚合模式优选的测量下发
    atu_aggre_mode_select_meas             = 261; //触发聚合模式优选下的A5测量

    open_inter_freq_ho_rsrp                = 262;
    open_inter_freq_ho_rsrq                = 263;
    open_inter_freq_ho_sinr                = 264;
    close_inter_freq_ho_rsrp               = 265;
    close_inter_freq_ho_rsrq               = 266;
    close_inter_freq_ho_sinr               = 267;
    open_inter_rat_ho_rsrp                 = 268;
    open_inter_rat_ho_rsrq                 = 269;
    open_inter_rat_ho_sinr                 = 270;
    close_inter_rat_ho_rsrp                = 271;
    close_inter_rat_ho_rsrq                = 272;
    close_inter_rat_ho_sinr                = 273;
    open_interF_rat_ho_rsrp                = 274;
    open_interF_rat_ho_rsrq                = 275;
    open_interF_rat_ho_sinr                = 276;
    close_interF_rat_ho_rsrp               = 277;
    close_interF_rat_ho_rsrq               = 278;
    close_interF_rat_ho_sinr               = 279;
    inter_rat_ho_rsrp                   = 280;
    inter_rat_ho_rsrq                   = 281;
    inter_rat_ho_sinr                   = 282;
    blind_a2_rsrp                       = 283;
    blind_a2_rsrq                       = 284;
    blind_a2_sinr                       = 285;
    intra_freq_ho_rsrp                  = 286;
    intra_freq_ho_rsrq                  = 287;
    intra_freq_ho_sinr                  = 288;
    inter_freq_ho_rsrp                  = 289;
    inter_freq_ho_rsrq                  = 290;
    inter_freq_ho_sinr                  = 291;
    macro_macro_intra_freq_ho_rsrp      = 292;
    macro_macro_intra_freq_ho_rsrq      = 293;
    macro_macro_intra_freq_ho_sinr      = 294;
    micro_micro_intra_freq_ho_rsrp      = 295;
    micro_micro_intra_freq_ho_rsrq      = 296;
    micro_micro_intra_freq_ho_sinr      = 297;
    macro_macro_intra_freq_ho_coord_rsrp  = 298;
    macro_macro_intra_freq_ho_coord_rsrq  = 299;
    macro_macro_intra_freq_ho_coord_sinr  = 300;
    micro_micro_intra_freq_ho_coord_rsrp  = 301;
    micro_micro_intra_freq_ho_coord_rsrq  = 302;
    micro_micro_intra_freq_ho_coord_sinr  = 303;
    open_intra_freq_ho_rsrp               = 304;
    open_intra_freq_ho_rsrq               = 305;
    open_intra_freq_ho_sinr               = 306;
    close_intra_freq_ho_rsrp              = 307;
    close_intra_freq_ho_rsrq              = 308;
    close_intra_freq_ho_sinr              = 309;
    low_speed_hcell_to_ncell_cov_ho_rsrp = 310;
    low_speed_hcell_to_ncell_cov_ho_rsrq = 311;
    low_speed_hcell_to_ncell_cov_ho_sinr = 312;
    abnormal_open_inter_freq_ho_rsrp     = 313;
    abnormal_open_inter_freq_ho_rsrq     = 314;
    abnormal_open_inter_freq_ho_sinr     = 315;
    abnormal_open_inter_rat_ho_rsrp      = 316;
    abnormal_open_inter_rat_ho_rsrq      = 317;
    abnormal_open_inter_rat_ho_sinr      = 318;
    abnormal_open_interF_rat_ho_rsrp     = 319;
    abnormal_open_interF_rat_ho_rsrq     = 320;
    abnormal_open_interF_rat_ho_sinr     = 321;
    abnormal_blind_a2_rsrp               = 322;
    abnormal_blind_a2_rsrq               = 323;
    abnormal_blind_a2_sinr               = 324;
    public_locate_detect_stgy1_A2_meas   = 325;//公共远近点检测策略1的A2测量
    public_locate_detect_stgy1_A1_meas   = 326;//公共远近点检测策略1的A1测量
    public_locate_detect_stgy2_A2_meas   = 327;//公共远近点检测策略2的A2测量
    public_locate_detect_stgy2_A1_meas   = 328;//公共远近点检测策略2的A1测量
    pept_pred_data_collection            = 329;//AI感知预估同频周期测量
    data_qual_ai_thrpt_pred_timeout      = 330;
    large_packet_ai_thrpt_pred_timeout   = 331;
    union_spa_intra_period               = 332;
    near_position_spec_drx_a1            = 333;
    near_position_spec_drx_a2            = 334;
    abnormal_near_position_spec_drx_a2   = 335;
    nbn_smart_ho_a3_intraf               = 336;
    nbn_smart_ho_a5_intraf               = 337;
    nbn_smart_ho_a5_interf               = 338;
    nbn_smart_ho_mr_collection           = 339;
    inter_freq_mbs_ue_mig                = 340;
    pingpong_sup_mbs_ue_mig              = 341;
    ntn_period_gps_info                  = 342;
    ntn_cho_a4                           = 343;
    ntn_cho_t1                           = 344;
    rb_num_wait_base_intra_freq_cov      = 345;
    rb_num_wait_base_interF_rat_cov      = 346;
    voice_quality_ho_retry               = 347;
    pingpong_sup_service_ho              = 348;
    rb_num_wait_base_large_packet        = 349;
    rb_num_wait_base_data_qual           = 350;
    rb_num_wait_base_aggremode_meas      = 351;
    rb_num_wait_base_aggremode_grid      = 352;
    load_balance_mr_collection           = 353;
    master_app_period_meas               = 354;
    inter_freq_dss_ho                    = 355;
    nrdc_pscell_release_rsrq             = 356;
    nrdc_pscell_release_sinr             = 357;
    scell_release_rsrq                   = 358;
    scell_release_sinr                   = 359;
    nsa_load_balance_mr_collection       = 360;
    ncr_inter_rat_meas                   = 361;
    mcptt_forbid_ho                      = 362;
    atg_scell_add_retry                  = 363;
    l3_ai_ran_predict_wait               = 364;
    xw_a4                                = 365;
    xw_d2                                = 366;
    xw_cho_d2                            = 367;
    inter_freq_small_ta_based_mig        = 368;
    interRat_freq_small_ta_based_mig     = 369;
    small_ta_ho_retry                    = 370;
    ncr_interF_meas                      = 371;
    ncr_intraF_meas                      = 372;
    l3_ai_ran_cover_predict_wait         = 373;
    l3_ai_ran_data_qual_predict_wait     = 374;
    l3_ai_ran_large_packet_predict_wait  = 375;
    lte_dss_overload                     = 376;
    lte_dss_overload_inter_freq          = 377;
    lte_dss_overload_inter_rat           = 378;
}
```
## STEP6: 期望响应消息配置
测量上报输出：无输出NoOutPut
```proto
message MeasurementReportDecisionResponse
{
    oneof action
    {
        NoOutPut                no_output                   = 1;
        HandoverIndicate        handover_indicate           = 2;
        SnPscellChangeIndicate  sn_pscell_change_indicate   = 3;
        SnPscellReleaseIndicate sn_pscell_release_indicate  = 4;
        ReconfigIndicate        reconfig_indicate           = 7;
        NcgiInfoIndicate        ncgi_info_indicate          = 8;
        MrTriggerEpsfbInd       epsfb_ind                   = 9;
        HoAndRedirectIndicate   ho_and_redirect_indicate    = 10;
        RedirectIndicate        redirect_indicate           = 11;
        DlCompIndicate          dl_comp_indicate            = 12;
        UlCompIndicate          ul_comp_indicate            = 13;
        ScellIndicate           scell_indicate              = 14;
        EcgiInfoIndicate        ecgi_info_indicate          = 15;
        UeGridInfoIndicate      ue_grid_info_indicate       = 16;
        UlPowerControlInd       ul_power_control_indicate   = 17;
        SnAddIndicate           sn_add_indicate             = 18;
        QosMonitorUlDelayIndicate    qos_monitor_ul_delay_indicate    = 19;
        CBFDlCompIndicate       cbf_dl_comp_indicate        = 20;
        UeGridHandleIndicate    ue_grid_handle_indicate     = 21;
        SAPreAdmitIndicate      sa_preadmit_indicate        = 22;
        AggreModeChooseContext  aggre_mode_choose_context   = 23;
        EcidMeasuredResults     ecid_measurement_results    = 24;
        IntraFreqInterferenceInd     intra_freq_interference_ind      = 25;
        HighwayUeInterfMeasInfo    highway_ue_interf_meas_info = 26;
        AggreModeResultInd          aggre_mode_ind             = 27;
        UnionSpaMeasInfo            union_spa_meas_info        = 28;
        ServCellMeasInd             serv_cell_meas_ind         = 29;
        NtnPeriodGpsInfo            ntn_period_gps_info        = 30;
    }

    message NtnPeriodGpsInfo
    {
        int32 degrees_latitude  = 2; //-8388608..8388607正数表示北纬，负数表示南纬。
        int32 degrees_longitude = 3; //-8388608..8388607
    }


    message SystemTimeInfo
    {
        uint64 system_time  = 1;
    }

    EventMrInfo      event_mr_info           = 100;
    AdmitRptPurpose  admit_rpt_purpose       = 101;
    SystemTimeInfo   system_time_info        = 102;//二次接纳不填写
    FailHoInfo       fail_ho_info            = 103;

    message PeriodMrHandleStrategy
    {
        enum DeduplicateStrategy  //多条MR对应相同Purpose下且输出相同Action的去重策略
        {
            discard_new                               = 0;
            keep_new                                  = 1;
        }
        uint32 mr_action_index                        = 1;
        DeduplicateStrategy strategy                  = 2;
    }
    PeriodMrHandleStrategy   handler_strategy_info   =104; // DRM仅在非稳态下输出
	UeCapabilityContext      ue_capability_context  = 105;
    google.protobuf.BoolValue     delay_ho_dl_quality_ind            = 106;     //语音起呼阶段禁止切换下行质差指示。true-下行质差，false-下行非质差，不携带代表状态无变更
    google.protobuf.BoolValue     voice_qual_ind     = 107;  // 根据UPA丢包、A3测量、A1A2测量和基带评估的语音或视频质差。true-语音质差，false-语音非质差，不携带代表质量状态不变化
    google.protobuf.BoolValue     data_qual_ind      = 108;  // 根据SPA、A1A2测量评估的质差。true-数据质差，false-数据非质差，不携带代表质量状态不变化
    EdgeUserInd             edge_user_ind            = 109;
    NextActionInd                next_action_ind     = 110; //指示当前流程处理完后，下一步到DRM的接纳动作
    RedCapFilterInfo        redcap_meas_filter_info       = 111;
    NormalUeInd            normal_ue_ind                  = 112;
    message RptDecisionStatistics
    {
        RptPurpose    purpose                = 1;
    }
    RptDecisionStatistics       rpt_decision_statistics = 113;
    MetroUeInfo                 metro_ue_info           = 114;
    message MetroUeInfo
    {
        google.protobuf.UInt32Value collect_rsrp = 1; //采集输出同频测量RSRP
        google.protobuf.UInt32Value infer_rsrp   = 2; //推理输出同频测量RSRP
    }
    repeated MeasTypeInfo  meas_type_info   = 115;
    MaintenanceInformation         maintenance_info    = 116; //维测信息,无业务逻辑
}
```


## ANSAWER
**ANSWER**
//STEPS_DESCRIBE_START
STEP1:
【切换准备失败惩罚开关(hoPrepareFailPunishSwch)】：打开
【功能频点以及邻区信息】：异系统f1:cell3和cell4
【功能执行方式】：切换+重定向
【reserved731】：打开
【HOPrepareFailPunish信息】：cell2惩罚定时器正在运行
【保存的PunishInfoId信息(小区、plmn、惩罚原因、guami信息)】：cell2（NR小区）、plmn1、Unknown target ID、guami1
cell3（LTE小区）、plmn1、Unknown target ID、guami2
cell2（NR小区）、plmn1、Handover failure in target 5GC/ NG-RAN node or target system、guami1
cell3（LTE小区）、plmn1、Handover failure in target 5GC/ NG-RAN node or target system、guami2
STEP2:异系统邻区配置数量:1
STEP3:- 异系统测量对象配置数量:1
STEP4:无
STEP5:UE接入cell1，选择plmn1和guami1，低速迁出异系统测量上报cell3，测量上报类型：low_speed_interf_mig
STEP6:测量上报输出：无输出NoOutPut
//STEPS_DESCRIBE_END

- path: ft/drm/lf/src/test/mrRptDecision/speepMigMeas/FtSpeedMigRptHandle.cpp
- type: add_test
```cpp
TEST_F(FtSpeedMigRptHandle, ran_6612580_6651364_ho_punish)
{
    //STEP1_START
    //STEP2_START
    //STEP3_START
    configMeasObject(PRIORITY_255);
    ROLE(FtLm).activeMoChg(buildSsbMeasObjInterF(addNRInterFMeasObject, "2", "2", 253, "enable", "kHz15").c_str());
    auto servingPlmn = R"({"mcc": "343630", "mnc": "3031"})";
    auto equivalentPlmns = R"([{"mcc": "343630","mnc":"3035"},
                               {"mcc": "343630","mnc":"3036"}])";
     std::vector<int> rsrp_option = {20, 21};
     initNbrAndPara();
     ROLE(FtLm).activeMoChg(R"({
                             "path":"@:GNBCUCPFunction[moId='1'].NRCellCU[cellLocalId=1].PublicPrivateNetworkMigCtrl[moId='default']",
                             "serialNumber":12,
                             "value":{
                                        "lowSpeedNCellMigSwch":"1",
                                        "lowSpeedInterFNCellMigMethod":"1"
                                     }
                         })");
     ROLE(FtLm).activeMoChg( R"({
                             "path":"@:GNBCUCPFunction[moId='1'].NRCellCU[cellLocalId=1].MobilityCtrl[moId='default']",
                             "serialNumber":12,
                             "value":{"hoCandiCellNum":1}
                         })");
       ROLE(FtLm).activeMoChg(
        R"({
            "path":"@:GNBCUCPFunction[moId='1'].MobilityGlobalConfig[moId='1'].HOFailPunish[moId='1']",
            "serialNumber":12,
            "value":{
                        "moId":"1",
                        "hoPrepareFailPunishSwch":"1"
                    }
        })");
    ROLE(FtLm).activeMoChg(R"({"path":"#:GNBCUCPFunction[moId='1'].reserved731",
                            "serialNumber":12,
                            "value":1
                            })", "#.GNBCUCPFunction.reserved731");
    //STEP3_END
    //STEP2_END
    //STEP1_END

    //STEP5_START
    //STEP6_START
    Mocker mocker;
    mocker.mockAPI("getCurrentSystTimeUsU64", getCurrentSystTimeUsU64).stubs().with(any()).will(returnValue(U64(22000000)));
    drm <---- fakeHrrm (S_HRRM_DRM_BROADCAST, "load_transfer", HrrmDrmMessage::kHoPunishDataNotification
        , getHoPlmnAndGuamiPunishDataNrNotify(3, 100, {"343630", "3031"}, {{"343630", "3031"}, {"343630", "3031"}}, {{"343630", "3031"}, {"343630", "3031"}}).c_str());
    json reqJson = nlohmann::json::parse(getMeasReportCtxWithUeSpeedInd(servingPlmn,equivalentPlmns,p2s::drm::low_speed_interf_mig, 5));
    reqJson["measurement_report_decision_request"]["serving_plmn"]["mcc"] = "343630";
    reqJson["measurement_report_decision_request"]["serving_plmn"]["mnc"] = "3031";
    reqJson["msg_header"]["ue_data"]["guami"]["amf_region_id"] = 11;
    reqJson["msg_header"]["ue_data"]["guami"]["amf_set_id"] = 12;
    reqJson["msg_header"]["ue_data"]["guami"]["amf_pointer"] = 13;
    reqJson["msg_header"]["ue_data"]["guami"]["plmn"]["mcc"] = "343630";
    reqJson["msg_header"]["ue_data"]["guami"]["plmn"]["mnc"] = "3031";
    ASSERT_TRUE(UumAccess(MeasRptBuilder(getMeasRptNbrs(rsrp_option),  reqJson.dump(4))
        , MeasRptWithHoPunishGuamiInfoAssert(p2s::drm::PunishInfo_HoPreparationFailCause_unknown_target_id)));
    //STEP5_END
    //STEP6_END
}
```