**RdcInfo**
- rdc_id:RAN-5946243
- repo_name:luc
- gerrit_link:https://gerrit.zte.com.cn/#/c/19470188/2
- date:Aug 31, 2024
- compile_script:ft/uc/update-compile-cmd.sh
- compile_command_json_path:ft/uc/compileCmdDir

## TestInfo




### 测试标题: 
RRC建立优化场景MSG5的传递路径：LUCM->LUC（UlDcchTranmissionMessage）传递远近点标识；

### 预制条件:
1、gNB1上cell1、cell2工作正常；gNB2上cell3工作正常；

### TC步骤:
测试步骤：校验RRC建立优化场景MSG5的传递路径：LUCM->LUC（UlDcchTranmissionMessage）传递远近点标识；


### TC预期结果:
测试结果：LUC收到远近点信息后,通过F1消息ULRRCMessageTransferIEs传递给HUCM
注意：传递过程中远近点信息字段含义（0: near_position ; 1:far_position），避免填写错误


### 预期结果:
<p>见单步确认结果</p>

### 通过准则:
<p>见单步确认结果</p>



## Tag Identification
### business_content_scence_tag
- asn消息构造
-protobuf消息校验
### code_modify_scence_tag
- type:add_test


**QUESTION**

作为5G通信协议开发专家和C++高级工程师，您需要根据**目标测试用例描述**和**代码生成要求**,基于**相似代码信息**，生成**目标测试用例描述**测试代码
# 目标用例生成可能依赖代码


代码路径：ft/uc/ftest/ftattach/src/action/FtAttachAction.cpp
```cpp
void FtAttachAction::recvMsg5WithMsg4OptAction(WORD32 cpfUeId, bool hasPosition)
{
    serviceLuc <---- fakeUcm (S_LUCM_LUC, __InstKey(ucmUcKey), __EventId(lucm_if::Message::kUlDcchTransferMessage), RrcSetupCmpBuilder(false, cpfUeId, true, false, TIME_FIVE, hasPosition));
}

```

代码路径：ft/uc/ftest/ftattach/inc/domain/ucm/builder/RrcSetupCmpBuilder.h
```cpp
struct RrcSetupCmpBuilder : GenericPbBuilder<lucm_if::Message>
{
    RrcSetupCmpBuilder(bool isCran, WORD32 cpfUeId = CPF_UE_ID, bool isMsg4Opt = false, bool hasStmsi=false, Msg5TimeStamp time = TIME_FIVE)
    : isCran(isCran), cpfUeId(cpfUeId), isMsg4Opt(isMsg4Opt),hasStmsi(hasStmsi), time(time)){}
    OVERRIDE(Status doBuild(lucm_if::Message&, FakeSystem& system) const);
private:
    bool isCran;
    WORD32 cpfUeId;
    bool isMsg4Opt;
    bool hasStmsi;
    Msg5TimeStamp time;
};

```


代码路径：ft/uc/ftest/ftattach/src/domain/ucm/builder/RrcSetupCmpBuilder.cpp
```cpp
Status RrcSetupCmpBuilder::doBuild(lucm_if::Message& msg, FakeSystem& system) const
{
    auto rrcSetupCmp = msg.mutable_ul_dcch_transfer_message();
    FT_ASSERT_VALID_PTR(rrcSetupCmp);

    rrcSetupCmp->set_bpf_ue_id(BPF_UE_ID);
    rrcSetupCmp->set_phy_cell_l3_id(CELL_ID);
    rrcSetupCmp->set_cpf_ue_id(cpfUeId);
    if(GET_SCENCE("NetShare"))
    {
        if(GET_SCENCE("MncLength is three"))
        {
            FT_ASSERT_SUCC_CALL(ns_RrcSetupCmpBuilder::fillNcgiWith3bitMnc(*rrcSetupCmp));
        }
        else
        {
            FT_ASSERT_SUCC_CALL(ns_RrcSetupCmpBuilder::fillNewNcgi(*rrcSetupCmp));
        }
    }
    else if(GET_SCENCE("MncLength is three"))
    {
        FT_ASSERT_SUCC_CALL(ns_RrcSetupCmpBuilder::fillNcgiWith3bitMnc(*rrcSetupCmp));
    }
    else
    {
        FT_ASSERT_SUCC_CALL(ns_RrcSetupCmpBuilder::fillNcgi(*rrcSetupCmp));
    }

    //服务Plmn为根据msg5携带的selectedPLMN_Identity_option在SIB1中找到的plmn
    auto servingPlmn = rrcSetupCmp->mutable_serving_plmn();
    FT_ASSERT_VALID_PTR(servingPlmn);
    servingPlmn->set_mcc(msg5ServingMcc, sizeof(msg5ServingMcc));
    if(GET_SCENCE("MncLength is three"))
    {
        servingPlmn->set_mnc(msg5ServingMnc_3bit, sizeof(msg5ServingMnc_3bit));
    }
    else
    {
        servingPlmn->set_mnc(msg5ServingMnc, sizeof(msg5ServingMnc));
    }

//    rrcSetupCmp->set_uu_asn1_code_stream(RRC_SETUP_CMP_WITH_PDCP, sizeof(RRC_SETUP_CMP_WITH_PDCP));
    rrcSetupCmp->set_uu_asn1_code_stream(RRC_SETUP_CPM, sizeof(RRC_SETUP_CPM));

    if(GET_SCENCE("MoMtUser"))
    {
        if(hasStmsi)
        {
            FT_ASSERT_SUCC_CALL(ns_RrcSetupCmpBuilder::fillTmsi(*rrcSetupCmp));
        }
        else
        {
            FT_ASSERT_SUCC_CALL(ns_RrcSetupCmpBuilder::fillTmsiPart2(*rrcSetupCmp));
        }
    }

    if(isCran)
    {
        ns_RrcSetupCmpBuilder::fillF1SctpInfo(*rrcSetupCmp);
    }

    if (isMsg4Opt)
    {
        std::stringstream msId;
        msId << "msId_" << getServlet();
        rrcSetupCmp->set_phsmsid(msId.str());
    }


    auto lucmSendMsg5Stamp = rrcSetupCmp->mutable_lucm_send_msg5_time();
    FT_ASSERT_VALID_PTR(lucmSendMsg5Stamp);
    if(time == TIME_FIVE)
    {
        lucmSendMsg5Stamp->set_value(getCurrentSystTimeUsU64()-(WORD64)10);
    }
    else if(time == TIME_FIVETOTEN)
    {
        lucmSendMsg5Stamp->set_value(getCurrentSystTimeUsU64()-(WORD64)6000);
    }
    else if(time == TIME_TENTOTWENTY)
    {
        lucmSendMsg5Stamp->set_value(getCurrentSystTimeUsU64()-(WORD64)15000);
    }
    else if(time == TIME_TWENTYTOTHIRTY)
    {
        lucmSendMsg5Stamp->set_value(getCurrentSystTimeUsU64()-(WORD64)26000);
    }
    else if(time == TIME_THIRTY)
    {
        lucmSendMsg5Stamp->set_value(getCurrentSystTimeUsU64()-(WORD64)300000);
    }

    return USI_SUCCESS;
}

```



代码路径：ft/uc/ftest/ftattach/src/action/FtAttachAction.cpp
```cpp
void FtAttachAction::transferMsg5WithMsg4OptAction(bool hasPosition)
{
    serviceLuc ----> fakeHucm(S_UL_DCCH, __InstKey(msg5LucToHucmKey), __EventId(uLRrcMsg), F1RrcSetupCmpAsserter(true, hasPosition));
}

```


代码路径：ft/uc/ftest/ftattach/inc/domain/huc/asserter/F1RrcSetupCmpAsserter.h
```cpp
DECL_FT_MSG_ASSERTER(F1RrcSetupCmpAsserter)
{
    F1RrcSetupCmpAsserter(bool isMsg4Opt = false, bool hasPosition =false):isMsg4Opt(isMsg4Opt),hasPosition(hasPosition){}
   virtual void doAssert(const void* actualMsg, size_t size, FakeSystem& sys) const override;

private:
    bool isMsg4Opt;
    bool hasPosition;
};



```

代码路径：ft/uc/ftest/ftattach/inc/domain/huc/asserter/F1RrcSetupCmpAsserter.h
```cpp
void F1RrcSetupCmpAsserter::doAssert(const void* actualMsg, size_t size, FakeSystem& sys) const
{
    FtAutoMsg<F1ap_F1AP_PDU> F1msg;
    static BYTE buffer[BUFF_LEN_8K];
    Decoder decode(actualMsg , size, buffer, sizeof(buffer), true);
    decode.decode(PerDeF1ap_F1AP_PDU, F1msg.getRef());

    FT_ASSERT_TRUE(F1msg.getRef().t == T_F1ap_F1AP_PDU_initiatingMessage);
    FT_ASSERT_TRUE(F1msg.getRef().u.initiatingMessage->procedureCode == F1AP_id_ULRRCMessageTransfer);
    F1ap_ULRRCMessageTransfer* msg = F1msg.getRef().u.initiatingMessage->union_value.pULRRCMessageTransfer;

    FT_ASSERT_TRUE(msg->protocolIEs.gNB_DU_UE_F1AP_ID == DU_F1AP_UE_ID);
    FT_ASSERT_TRUE(msg->protocolIEs.gNB_CU_UE_F1AP_ID == CU_F1AP_UE_ID);
    FT_ASSERT_TRUE(msg->protocolIEs.sRBID == 1);
    FT_ASSERT_TRUE(msg->protocolIEs.rRCContainer.nocts == sizeof(RRC_SETUP_CPM));
    FT_ASSERT_TRUE(memcmp(msg->protocolIEs.rRCContainer.data, RRC_SETUP_CPM, msg->protocolIEs.rRCContainer.nocts) == 0);

    FT_ASSERT_TRUE(msg->protocolIEs.nRCellIdentity.nbits == 36);
    FT_ASSERT_TRUE(getCellId(msg->protocolIEs.nRCellIdentity.data, sizeof(msg->protocolIEs.nRCellIdentity.data)/sizeof(ASN1OCTET)) == CELL_ID);
    FT_ASSERT_TRUE(getGnbId(msg->protocolIEs.nRCellIdentity.data, sizeof(msg->protocolIEs.nRCellIdentity.data)/sizeof(ASN1OCTET)) == GNB_ID);

    if (isMsg4Opt)
    {
        FT_ASSERT_TRUE(msg->protocolIEs.tOptFlags.privateAsn_optionPresent == USI_PRESENT);
        FT_ASSERT_TRUE(msg->protocolIEs.privateAsn_option.tOptFlags.duPhsAddr_optionPresent == USI_PRESENT);

        std::stringstream msId;
        msId << "msId_" << getServlet();
        FT_ASSERT_TRUE(msg->protocolIEs.privateAsn_option.duPhsAddr_option.nocts == msId.str().length());
        FT_ASSERT_TRUE(memcmp(msg->protocolIEs.privateAsn_option.duPhsAddr_option.data, msId.str().c_str(), msg->protocolIEs.privateAsn_option.duPhsAddr_option.nocts) == 0);
    }

}


```



# 相似代码信息
## 相似文本测试用例描述
无

## 相似用例FT代码
代码路径：ft/uc/ftest/ftattach/src/FtAttach.cpp
```cpp
TEST_F(FtAttachWithMsg4Opt, should_attach_succ_with_fork_wait_msg5)
{
    sendMsg3ReqfromStableOp();
    srbAdmitAction();
    buildAndSendMsg4();
    initULRrcMsgAction();

    ppshUeContextSetupWithMsg4OptAction();
    MccUeContextSrb1CfgReqWithMsg4OptAction();

    receiveMsg4FromCu();
    recvMsg5WithMsg4OptAction(INVALID_CPF_UE_ID);

    MccUeContextSrb1CfgRspAction(true);

    UeIdNotificationAction();
    transferMsg5WithMsg4OptAction();
    rrmSrbAckAction();

    F1UeCtxtSetupAction();

    ppshUeCtxtModifyCfgForNcgiWithMsg4OptAction();
    rrmInitDrbAdmitAction();
    ppshUeCtxtModifyCfgAction();
    mccUeCtxtModifyCfgAction();
    f1UeContextSetupRspAction();

    rrcRecfgCmpIndAction();
    rrmDrbAckAction();
    ppshUeCtxtModifyAck();
    mccUeCtxtModifyAck();
}

```


## 相似用例代码依赖
无


# 代码生成要求
请根据提供的相关信息和相似信息，遵循以下原则进行测试代码生成：
1. 完整性原则
  - 必须输出完整的函数实现，包括函数声明、函数体和结束花括号
  - 不允许使用省略号或只显示修改部分
  - 保持函数的完整上下文

2. 参数化设计原则
  - 新增逻辑应通过函数参数控制
  - 避免硬编码固定值
  - 保持函数接口的灵活性

4. 测试用例适配
  - 确保测试用例覆盖新增功能
  - 维护测试用例的正确性
  - 补充必要的测试场景

5. 代码修改文件生成方法
  -对于测试套和测试代码，按照相似用例文件名输出
  -对于action代码及函数体及修改，按照原有代码文件名输出



# 各类用例内容代码修改方法示例
## 设置参数开关
- 设置参数开关主要实在测试用例的开头预先通过API接口设置某些参数开关或配置某些参数为预期的值，以达到模拟测试场景的效果
### 例子
设置参数开关:打开协议对齐开关
接口信息：static void setProtocolAlignSwch(BYTE swch, const string& moId);
```cpp
TEST_F(FtResume, should_resume_success_when_mo_protocolcompatibilityswch_is_open_transfer_protocol_align_ind)
{
    Mo::setProtocolAlignSwch(1, "0");
    //....
}
```
## 预置稳态数据
- 预置稳态数据主要是在测试用例的开头预先设置流程中需要使用的上下文内容，以达到模拟测试场景的效果
- 预置稳态数据的相关接口声明和实现都在类UeDataUca2CommitBuilder中，在该类中修改现有或新增静态方法后，在测试用例开头调用
### 例子
预置稳态数据：设置AMF信息并启用TP记录开关
```cpp
static Status addAmfInfo(AmfInfo amfInfo = AmfInfo::BOTH, bool isReleaseSence = false);

Status UeDataUca2CommitBuilder::addAmfInfo(AmfInfo amfInfo, bool isReleaseSence)
{
    buildField(addAmfNgInfo, amfInfo, isReleaseSence);
    return USI_SUCCESS;
}

string addAmfNgInfo(string& s, AmfInfo& amfInfo, bool isReleaseSence)
    {
        p2s::AccessMsgRsp rsp;
        rsp.ParseFromString(s);

        PRINT_PB(rsp);

        p2s::ng_proto::Ng ng;
        auto list = rsp.mutable_rsp_data();
        for(int i = 0;i < list->size(); i++)
        {
            if(list->Get(i).type() == p2s::NG)
            {
                ng.ParseFromString(list->Get(i).data());
                if(amfInfo == AmfInfo::BOTH || amfInfo == AmfInfo::BOTH_3MNC)
                {
                    ng.mutable_ue_ngap_id_pair()->mutable_mme_ngap_ueid()->set_mme_ngap_ue_id(AMF_NG1AP_UE_ID);

                    FT_AUTO_PTR(ueNgapIdPair, ng.mutable_ue_ngap_id_pair());
                    FT_AUTO_PTR(mmeNgapUeid, ueNgapIdPair->mutable_mme_ngap_ueid());
                    mmeNgapUeid->set_mme_ngap_ue_id(AMF_NG1AP_UE_ID);

                    FT_AUTO_PTR(guami, ng.mutable_guami());
                    guami->set_amf_pointer(AMF_POINTER_ID);
                    guami->set_amf_region_id(AMF_REGION_ID);
                    guami->set_amf_set_id(AMF_SET_ID);
                    if(isReleaseSence)
                    {
                        if(amfInfo == AmfInfo::BOTH_3MNC)
                        {
                            guami->mutable_plmn()->set_mcc(servingPlmnId_mnc3dig.getMcc(),servingPlmnId_mnc3dig.getMccLen());
                            guami->mutable_plmn()->set_mnc(servingPlmnId_mnc3dig.getMnc(),servingPlmnId_mnc3dig.getMncLen());
                        }
                        else
                        {
                            guami->mutable_plmn()->set_mcc(servicePlmnId.getMcc(),servicePlmnId.getMccLen());
                            guami->mutable_plmn()->set_mnc(servicePlmnId.getMnc(),servicePlmnId.getMncLen());
                        }
                    }
                }
                else if(amfInfo == AmfInfo::ONLY_AMF_UE_ID)
                {
                    ng.clear_guami();
                    ng.mutable_ue_ngap_id_pair()->mutable_mme_ngap_ueid()->set_mme_ngap_ue_id(AMF_NG1AP_UE_ID);

                    FT_AUTO_PTR(ueNgapIdPair, ng.mutable_ue_ngap_id_pair());
                    FT_AUTO_PTR(mmeNgapUeid, ueNgapIdPair->mutable_mme_ngap_ueid());
                    mmeNgapUeid->set_mme_ngap_ue_id(AMF_NG1AP_UE_ID);
                }
                else if(amfInfo == AmfInfo::NOTALL)
                {
                    FT_AUTO_PTR(guami, ng.mutable_guami());
                    ng.clear_guami();
                    if(ng.has_ue_ngap_id_pair() && ng.ue_ngap_id_pair().has_mme_ngap_ueid())
                    {
                        ng.clear_ue_ngap_id_pair();
                    }
                }

                list->DeleteSubrange(i,1);
                PRINT_PB(ng)
            }
        }

        auto* r = rsp.add_rsp_data();
        r->set_type(p2s::NG);
        r->set_data(ng.SerializeAsString());

        return rsp.SerializeAsString();
    }

TEST_F(FtResume, should_resume_success_when_mo_protocolcompatibilityswch_is_close_no_transfer_protocol_align_ind)
{
    UeDataUca2CommitBuilder::addAmfInfo(); 
    //...
}   
```

## 稳态数据校验
- 主要是在业务流程结束后，校验当前流程的指定上下文中的数据是符合预期
- 新增或修改现有的FieldAsserter类，实现校验内容，对外提供校验接口，测试用例在结束时调用
### 例子
校验SpecialUeField的identify type为Trace_Id，type类型为TYPE_7
```cpp
static void doAssertIdentify(const WORD32 ueId, const BYTE identifyType, const BYTE vipType);

void SpecialUeFieldAssert::doAssertIdentify(const WORD32 ueId, const BYTE identifyType, const BYTE vipType)
{
    auto ueDataSharePtr = getUeDataByDataType(ueId, p2s::SPECIAL_UE_INFO);
    dumpUeDataByDataType(ueId, p2s::SPECIAL_UE_INFO);
    auto* specialUeInfo = (p2s::SpecialUeInfo*) ueDataSharePtr.get();
    FT_ASSERT_VALID_PTR(specialUeInfo);
    FT_ASSERT_TRUE(specialUeInfo->identify_type() == identifyType);
    FT_ASSERT_TRUE(specialUeInfo->vip_type() == vipType);
}

TEST_F(FtPduSessionSetupWithAtu, should_atu_pick_succ_for_trace_id_when_pdusessionsetup)
{
    //...
    SpecialUeFieldAssert::doAssertIdentify(CPF_UE_ID,Trace_Id,TYPE_7);
}
```

## 业务流程消息校验
- 模块在业务流程中向外部模块发送消息，用例需要校验该消息的内容是否符合预期
- 消息的交互都封装在用例的Action方法中，其中使用语法糖**---->**表示向外部发送消息，包含发送该消息使用的Session，InstKey，EventId和负责校验的Asserter类构造
- Asserter类负责实现具体的校验细节，其可能通过组装的方式，将消息的不同内容封装成不同的Asserter类，然后使用组装的方式来实现用例的具体校验要求
### 例子
PduSessionResourceSetupReq中携带安全信息信息
```cpp
struct E1PduSessionResourceSetupReqWithSecurityAsserter : E1PduSessionResourceSetupAbsReqAsserter
{
    E1PduSessionResourceSetupReqWithSecurityAsserter( bool isUpCapIntegrityProtectNia0 = false):
        isUpCapIntegrityProtectNia0(isUpCapIntegrityProtectNia0){}
    OVERRIDE(void assertMore(const e1ap::PduSessionResourceSetupRequest& req) const);
private:
    bool isUpCapIntegrityProtectNia0;
};

void E1PduSessionResourceSetupReqWithSecurityAsserter::assertMore(const e1ap::PduSessionResourceSetupRequest& req) const
{
    if(GlobalSwitch::isSecurityCapOn())
    {
       TEncIntInfo_up encIntInfo;
       TEncIntInfo_up* dest = &testKeyInfo;
       if(isUpCapIntegrityProtectNia0)
       {
           NAMESPACE(E1PduSessionResourceSetupReqAsserter)::driveKeyForFt(encIntInfo);
           dest = &encIntInfo;
       }

       FT_ASSERT_TRUE(req.has_security_config());
       auto securityCfg = req.security_config();

       FT_ASSERT_TRUE(securityCfg.security_algorithm().has_cipheing_algorithm());
       FT_ASSERT_TRUE(securityCfg.security_algorithm().cipheing_algorithm().value() == dest->dwEncAlg);

       if(g_drb_no_int)//打桩不配置DRB的完保算法。和空算法有区别
       {
           FT_ASSERT_TRUE(securityCfg.security_algorithm().has_integrity_protect_algorithm() == false);
       }
       else
       {
           FT_ASSERT_TRUE(securityCfg.security_algorithm().has_integrity_protect_algorithm());
           FT_ASSERT_TRUE(securityCfg.security_algorithm().integrity_protect_algorithm().value() == dest->dwInteAlg);
       }

       FT_ASSERT_TRUE(securityCfg.security_key().up_cipheing_key().size()==MAX_ENC_INT_KEY_LEN);
       FT_ASSERT_TRUE(memcmp((BYTE*)securityCfg.security_key().up_cipheing_key().c_str(), dest->aucKUpEnc, MAX_ENC_INT_KEY_LEN) == 0);
       FT_ASSERT_TRUE(securityCfg.security_key().up_integrity_protect_key().size()==MAX_ENC_INT_KEY_LEN);
       FT_ASSERT_TRUE(memcmp((BYTE*)securityCfg.security_key().up_integrity_protect_key().c_str(), dest->aucKUpInt, MAX_ENC_INT_KEY_LEN) == 0);
   }
}

void FtPduSessionSetupAction::dpfPduSessionSetupAction(initializer_list<E1PduSessionResourceSetupAbsReqAsserter*> list,bool isCnPdb)
{
    for(auto upId:FtPDUCfg::getAllUpId(BitInterval::more(__DRB_FAIL_PDU,true)))
    {
        localPduSetupAction(list,upId, isCnPdb);
    }
}

void FtPduSessionSetupAction::localPduSetupAction(initializer_list<E1PduSessionResourceSetupAbsReqAsserter*> list,WORD32 upId, bool isCnPdb)
{
    serviceUc ----> fakeDpf (S_CPF_DPF, __InstKey(dpfPduSetupKey), __EventId(e1ap::DrbMessage::kPduSessionResourceSetupRequest), E1PduSessionResourceSetupMutliReqAsserter(list, DPF_UE_ID, e1ap::ProcedureCode_ProcCode_MN_ERAB_SETUP));
    serviceUc <---- fakeDpf (S_CPF_DPF, __InstKey(dpfPduSetupKey), __EventId(e1ap::DrbMessage::kPduSessionResourceSetupResponse),E1PduSessionResourceSetupRspBuilder(upId, false, e1ap::UpSecurityResult_SecurityResult_REQUIRED, e1ap::UpSecurityResult_SecurityResult_REQUIRED, isCnPdb),P_S_NR_PDUSESSION_SETUP_ADDRESS);
}

TEST_F(FtPduSessionSetupWithAtu, should_atu_pick_succ_for_trace_id_when_pdusessionsetup)
{
    //业务流程省略
    //...
    dpfPduSessionSetupAction({new E1PduSessionResourceSetupReqWithSecurityAsserter(),
            new E1PduSessionResourceSetupReqWithPduListAsserter(INVALID_WORD32,false,1,SGW_TRAN_IP,sizeof(SGW_TRAN_IP),false,
            testRohc,e1ap::ForwardInfo_Type_direct_forward,true,false, 0, false)},false);
    //业务流程省略
    //...
}

```
## 业务流程消息构造
- 模块在业务流程收到外部模块发送的消息，再FT中进行构造后模拟收到外部模块发送的消息，用例需要构造符合预期的消息内容
- 消息的交互都封装在用例的Action方法中，其中使用语法糖**<----**表示收到外部发送消息，包含发送该消息使用的Session，InstKey，EventId和负责构造的Builder类实现
- Builder类负责实现具体的构造细节，其可能通过组装的方式，将消息的不同内容封装成不同的Builder类，然后使用组装的方式来实现用例的具体构造要求
### 例子
收到hucm模块的UeDlLargeSmallPacketInd消息
```cpp
void FtStableAction::recvUeDlLargeSmallPacketInd(hucm_if::Status status)
{
    serviceUc <---- fakeUcm (S_HUCM_HUC, __InstKey(ucmUcKey), __EventId(hucm_if::Message::kUeDlLargeSmallPacketInd), UeDlLargeSmallPacketIndBuilder(status));
}

struct UeDlLargeSmallPacketIndBuilder : GenericPbBuilder<hucm_if::Message>
{
    UeDlLargeSmallPacketIndBuilder(hucm_if::Status status = hucm_if::normal, hucm_if::Status caStatus = hucm_if::normal):status(status), caStatus(caStatus){}
    virtual Status doBuild(hucm_if::Message&, FakeSystem& system) const override;
private:
    hucm_if::Status status;
    hucm_if::Status caStatus;
};

Status UeDlLargeSmallPacketIndBuilder::doBuild(hucm_if::Message& msg, FakeSystem& sys) const
{
    AUTO_PTR(ind, msg.mutable_ue_dl_large_small_packet_ind());
    ind->set_cpf_ue_id(CPF_UE_ID);
    ind->set_status(status);
    ind->set_ca_status(caStatus);

    return USI_SUCCESS;
}

TEST_F(FtStable, should_transfer_ue_dl_large_small_packet_ind_with_normal_and_drm_no_output)
{
    attachAction();
    mockDrmUeLargeSmallPacketDecisionAction(p2s::drm::UeLargeSmallPacketInd_Status_normal,p2s::drm::UeLargeSmallPacketInd_Status_normal, NoOutPut, {}
            , {}, false, p2s::drm::UeLargeSmallPacketInd_Status_normal, p2s::drm::UeLargeSmallPacketInd_Status_normal, false, p2s::drm::LargeSmallPacketInd_Type_normal);
    recvUeDlLargeSmallPacketInd(hucm_if::normal);
    fakeDrm.clear();
}
```

## 标准KPI校验
- 主要是在业务流程的最后校验标注计数器的上报情况
- 使用固定的宏封装来进行标准计数器的校验
### 例子
KPI计数器在测试用例中的实现例子如下:
```cpp
TEST_F(FtAttach, should_rcv_rrc_inter_gnb_resume_req_to_setup_fail)
{{
    receiveResumeOrRnauReqToSetupAction(false,true,CELL_ID,true);
    sendRrcResumeOrRnautoSetupMsg4AndWaitMsg5Timeout();
    ueContextReleaseRequestAction();

    intraRelIndAction(flag, UCS_E_X1_RRC_SETUP_TIMEOUT, 1, 0, not_check_ctxrelkpiflag, false, false, false);
    //校验计数器上报
    EXPECT_KPIS
    ( KPI_TDD(__TTrigPduSessionModStat, byCounterType(PduSessionMod_UnfinishedProce_Conflict).tPlmnInfoCUObject_cellId(htons(CELL_ID)).tPlmnInfoCUObject_gNBplmn(htonl(PLMN_TOOL)).tPlmnInfoCUObject_servingPlmn(htonl(PLMN_TOOL_1)).tPlmnInfoCUObject_gNBIdLength(htons(GNB_ID_LEN))),
      KPI_TDD(__FlexTTrigPduSessionSetupStat,flex_tPlmnInfoCUObject_servingPlmn(htonl(PLMN_TOOL_1)).t_byCounterType(PduSessionSetup_UnfinishedProce_Conflict).flex_tCellSliceObject_gNBId(htonl(GNB_ID)).flex_tCellSliceObject_cellId(htons(CELL_ID)).byFlexBuff(FLEX_KPI_PLMN_1, sizeof(FLEX_KPI_PLMN_1)).flex_tCellSliceObject_gNBLength(htons(26))),
      KPI_TDD(__FlexTTrigQosFlowSetupStat, flex_tPlmnInfoCUObject_servingPlmn(htonl(PLMN_TOOL_1)).t_byCounterType(QosFlowSetup_UnfinishedProce_Conflict).t_byVoiceStgy(VoiceStgy_VoNR_Fail).t_w5qi(htons(2)).flex_tCellSliceObject_cellId(htons(CELL_ID)).flex_tCellSliceObject_gNBplmn(htonl(PLMN_TOOL)).flex_tCellSliceObject_gNBLength(htons(GNB_ID_LEN)).flex_tCellSliceObject_gNBplmn(htonl(GNBPLMN)).flex_tCellSliceObject_sd(htonl(SLICE_DIFF)).flex_tCellSliceObject_sst(htonl(SLICE_SERVICE_TYPE)).flex_tCellSliceObject_groupId(htonl(GROUP_ID)).flex_tCellSliceObject_plmnId(htonl(PLMN_TOOL_1)).byFlexBuff(FLEX_KPI_PLMN_1, sizeof(FLEX_KPI_PLMN_1)))
    );

    //校验计数器不上报
    UNEXPECT_KPIS
    (
        KPI_TDD(__TTrigNrDcMnSnRelStat, byCounterType(SNRelease_Cell_not_Available).byDirection(SgNB_Trig).tPlmnInfoCUObject_gNBId(htonl(GNB_ID))
                       .tPlmnInfoCUObject_gNBIdLength(htons(GNB_ID_LEN)).tPlmnInfoCUObject_cellId(htons(CELL_ID)).tPlmnInfoCUObject_gNBplmn(htonl(PLMN_TOOL))),
        KPI_TDD(__TTrigNrDcMnSnRelStat, byCounterType(SnRelease_gNB_UE_ReEst_or_Resume_succ).byDirection(MgNB_Trig).tPlmnInfoCUObject_gNBId(htonl(GNB_ID))
               .tPlmnInfoCUObject_gNBIdLength(htons(GNB_ID_LEN)).tPlmnInfoCUObject_cellId(htons(CELL_ID)).tPlmnInfoCUObject_gNBplmn(htonl(PLMN_TOOL)))
    );
}}
```
- 注意使用的是EXPECT_KPIS校验计数器上报,使用UNEXPECT_KPIS校验计数器不上报
- 输出整个目标测试用例代码


## 内部计数器校验
- 主要是在业务流程的最后校验内部计数器（Metric）的上报情况
- 使用固定的宏封装来进行内部计数器的校验
### 例子
内部计数器在测试用例中的实现例子如下:
```cpp
TEST_F(FtNtoLHos, should_report_high_speed_hcell_mig_inner_kpi_when_ho_pre_fail)
{
    triggerNtoLHoAction(false, false, Based_Measure, p2s::drm::HandoverIndicate_HandoverCause_ho_based_high_speed_hCell_mig);
    ntoLHosHoRequiredAction(Ngap_CauseRadioNetwork_Root_handover_desirable_for_radio_reason);
    transferUeAssistanceInfoAction();
    handoverPreTimeoutAction();

    EXPECT_INNER_KPIS //当校验上报值大与1是使用EXPECT_INNER_KPIS宏
    ( KPI_INNER(InnerCounterName::PROC_ID::IntraDu_HoOut_PreFail, InnerCounterName::SCEN_ID::Amf_Change, 1, USI_SUCCESS, NRCGI, NRCGI_NEW), //该inner的metric对象为NR邻接关系_V3,检查NRCGI的填写正确情况
      KPI_INNER(InnerCounterName::PROC_ID::NrToLteHoLteCellRsrp, InnerCounterName::SCEN_ID::Range7, 1, USI_SUCCESS, NRCGI, Ecgi(PLMN_TOOL, ENB_ID, ENB_CELL_ID)) //该inner的metric对象为CUCP双连接邻接关系及EutranNr邻接关系_V3,检查NRCGI的填写正确情况
    , KPI_INNER(InnerCounterName::PROC_ID::RrcConnResume_OtherCause_Fail, InnerCounterName::SCEN_ID::LRRM_Preempt_Rel, 1, USI_SUCCESS, NRCGI, Nrcgi())
    );
 
    UNEXPECT_INNER_KPIS//当校验上报值为0或校验不上报是使用UNEXPECT_INNER_KPIS宏,表示不上报某个计数器
    ( 
      KPI_INNER(InnerCounterName::NrdcMn_MnTrigSnDel, InnerCounterName::Handover_Fail, 1, UCS_E_BPF_MOD_UE_CTXT_TIMEOUT, NRCGI, Nrcgi())
    );
}
```
- 注意使用的是EXPECT_INNER_KPIS宏或UNEXPECT_INNER_KPIS
- 如果Metric对象是"NR邻接关系_V3",则使用部分入参为NRCGI, NRCGI_NEW
- 如果Metric对象是"CUCP双连接邻接关系及EutranNr邻接关系_V3",则使用部分入参为NRCGI, Ecgi(PLMN_TOOL, ENB_ID, ENB_CELL_ID)
- 其余的Metric对象,使用部分入参为NRCGI, Nrcgi() 
- 样例中的KPI_INNER中的入参数字1代表上报了1次该计数器

# 建议的实现步骤

1. 先了解目标测试用例的各个测试点,学习每类测试点的修改方法
2. 阅读相似用例和给出的代码片段，了解各个代码之间的调用关系和业务语义
3. 逐条分析每个测试点，分析那些代码片段需要修改或新增,然和结合修改方法进行修改
4. 将**相似用例FT代码**完全作为你的基础用例,在该用例上进行修改和新增,你仅需要在基础用例上实现目标测试用例描述即可

# 输出格式

- 测试用例的命名必须按照TEST_F(测试套名称, should_{测试内容}_when_{预置条件}_then_{测试步骤})进行命名，{测试步骤}、{预置条件}和{测试内容}采用小写驼峰式命名并且禁止出现中文。

例子：TEST_F(InitOrcheTest, should_attach_success_when_uecap_switch_is_open)

新增或修改的测试代码

- path:路径信息
- type:变更类型 add_test|modify_code|add_code
- step_describe:修改涉及步骤标识，例如["STEP1"]
```cpp
```
例如：
- path:ft/uc_new/ftest/src/ft/feature/ft-suspend/FtSuspendAction.cpp
- type:modify_code
- step_describe:["STEP1"]
```cpp
完整的修改代码函数
```

# 输出要求
- 输出无关的代码不要做省略处理,你需要输出完整的函数片段

# 目标测试用例描述


## 业务流程消息构造
- STEP1:STEP1:LUCM->LUC（UlDcchTranmissionMessage）传递远近点标识；
### 涉及接口
message RrcDirectTransmissionMessage
{
    uint32              cpf_ue_id              = 1;
    UeF1apIdPair        ue_apid_pair           = 2;    //是否添加，如果不加，UE发起的detach，HUC之后触发的F1释放如何获取ApId?
    bytes               rrc_payload            = 3;
    uint32              srb_id                 = 4;
    bytes               phsMsId                = 5;
    bytes               f1_payload             = 6; //用于F1口的信令跟踪上报
    google.protobuf.UInt32Value    cho_current_user_number = 7;//逻辑小区级
    repeated QosMonitorCellUeInfo  qosmonitor_cell_ue_info_list  = 8;
    google.protobuf.UInt64Value    hucm_send_msg5_time           = 9;
    Position                       position                   = 10; 

    message Position
    {        
        enum Type
        {
            near_position  = 0;
            far_position   = 1;   
        }

        Type value = 1;
    }
}

### 涉及字段
    position


## 业务流程消息校验
- STEP2:LUC收到远近点信息后,通过F1消息ULRRCMessageTransferIEs传递给HUCM,注意：传递过程中远近点信息字段含义（0: near_position ; 1:far_position），避免填写错误
### 涉及接口
typedef struct F1ap_InitiatingMessage {
    F1ap_ProcedureCode procedureCode;
    union {
        /* t = null */
        void * value;
        /* t = 0 */
        F1ap_Reset * pReset;
        /* t = 1 */
        F1ap_F1SetupRequest * pF1SetupRequest;
        /* t = 10 */
        F1ap_UEContextReleaseRequest * pUEContextReleaseRequest;
        /* t = 12 */
        F1ap_DLRRCMessageTransfer * pDLRRCMessageTransfer;
        /* t = 13 */
        F1ap_ULRRCMessageTransfer * pULRRCMessageTransfer;
    }
typedef struct F1ap_ULRRCMessageTransfer_protocolIEs {
        F1ap_GNB_CU_UE_F1AP_ID   gNB_CU_UE_F1AP_ID;
        F1ap_GNB_DU_UE_F1AP_ID   gNB_DU_UE_F1AP_ID;
        F1ap_SRBID   sRBID;
        F1ap_RRCContainer   rRCContainer;
        F1ap_PLMN_Identity   selectedPLMNID_option;
        F1ap_GNB_DU_UE_F1AP_ID   new_gNB_DU_UE_F1AP_ID_option;
        F1ap_NRCellIdentity   nRCellIdentity;
        F1ap_PrivateAsn   privateAsn_option;
        F1ap_StdPrivateInfoContainer   stdPrivateInfoContainer_option;
    struct {
        ASN1UINT selectedPLMNID_optionPresent:1 ;
        ASN1UINT new_gNB_DU_UE_F1AP_ID_optionPresent:1 ;
        ASN1UINT privateAsn_optionPresent:1 ;
        ASN1UINT stdPrivateInfoContainer_optionPresent:1 ;
    } tOptFlags;

    ASN1_SEQUENCE_CONSTRUCT(F1ap_ULRRCMessageTransfer_protocolIEs)
}  F1ap_ULRRCMessageTransfer_protocolIEs;

typedef struct F1Private_PrivateContainerInStdMsg {
    ASN1UINT t;
    union {
        /* t = null */
        void  *value;
        /* t = T_F1Private_PrivateContainerInStdMsg_ueContextSetupReqPrivateInfo or 1 */
        F1Private_UeContextSetupReqPrivateInfo   *ueContextSetupReqPrivateInfo;
        F1Private_UEContextModifyFailurePrivateInfo   *uEContextModifyFailurePrivateInfo;
        /* t = T_F1Private_PrivateContainerInStdMsg_ulRrcMessageTransferPrivateInfo or 19 */
        F1Private_UlRRCMessageTransferPrivateInfo   *ulRrcMessageTransferPrivateInfo;
        Asn1OpenType *extElem;
    } u ;
} F1Private_PrivateContainerInStdMsg;

typedef struct F1Private_UlRRCMessageTransferPrivateInfo {
    F1Private_UlRRCMessageTransferPrivateInfo_protocolIEs   protocolIEs;
} F1Private_UlRRCMessageTransferPrivateInfo;

typedef struct F1Private_UlRRCMessageTransferPrivateInfo_protocolIEs {
        F1Private_SinrPosition   sinrPosition_option;
    struct {
        ASN1UINT sinrPosition_optionPresent:1 ;
    } tOptFlags;

    ASN1_SEQUENCE_CONSTRUCT(F1Private_UlRRCMessageTransferPrivateInfo_protocolIEs)
}  F1Private_UlRRCMessageTransferPrivateInfo_protocolIEs;

typedef enum 
{
    F1Private_SinrPosition_Root_nearPoint = 0,
    F1Private_SinrPosition_Root_farPoint = 1
} F1Private_SinrPosition_Root;

typedef ASN1UINT F1Private_SinrPosition;/* range: 0 - 1 */
        
### 涉及字段
pULRRCMessageTransfer->stdPrivateInfoContainer_option->ulRrcMessageTransferPrivateInfo->sinrPosition_option;


**ANSWER**
//STEPS_DESCRIBE_START
//STEP1:LUCM->LUC（UlDcchTranmissionMessage）传递远近点标识;
//STEP2:LUC收到远近点信息后,通过F1消息ULRRCMessageTransferIEs传递给HUCM,注意：传递过程中远近点信息字段含义（0: near_position ; 1:far_position），避免填写错误
//STEPS_DESCRIBE_END

- path:ft/uc/ftest/ftattach/src/FtAttach.cpp
- type:add_test
```cpp
TEST_F(FtAttachWithMsg4Opt, should_attach_succ_when_msg5_has_position)
{
    sendMsg3ReqfromStableOp();
    srbAdmitAction();
    buildAndSendMsg4();
    initULRrcMsgAction();

    ppshUeContextSetupWithMsg4OptAction();
    ueContextSrb1CfgWithMsg4OptAction();

    receiveMsg4FromCu();
    //STEP1_START
    recvMsg5WithMsg4OptAction(INVALID_CPF_UE_ID, true);
    //STEP1_END
    UeIdNotificationAction();
    //STEP2_START
    transferMsg5WithMsg4OptAction(true);
    //STEP2_END
    rrmSrbAckAction();

    ppshUeCtxtModifyCfgForNcgiWithMsg4OptAction();
    F1UeCtxtSetupAction();
    rrmInitDrbAdmitAction();
    ppshUeCtxtModifyCfgAction();
    mccUeCtxtModifyCfgAction();
    f1UeContextSetupRspAction();

    rrcRecfgCmpIndAction();
    rrmDrbAckAction();
    ppshUeCtxtModifyAck();
    mccUeCtxtModifyAck();
}
```
- path:ft/uc/ftest/ftattach/inc/domain/ucm/builder/RrcSetupCmpBuilder.h
- type:modify_code
```cpp

struct RrcSetupCmpBuilder : GenericPbBuilder<lucm_if::Message>
{
    RrcSetupCmpBuilder(bool isCran, WORD32 cpfUeId = CPF_UE_ID, bool isMsg4Opt = false, bool hasStmsi=false, Msg5TimeStamp time = TIME_FIVE, bool hasPosition = false)
    : isCran(isCran), cpfUeId(cpfUeId), isMsg4Opt(isMsg4Opt),hasStmsi(hasStmsi), time(time), hasPosition(hasPosition){}
    OVERRIDE(Status doBuild(lucm_if::Message&, FakeSystem& system) const);
private:
    bool isCran;
    WORD32 cpfUeId;
    bool isMsg4Opt;
    bool hasStmsi;
    Msg5TimeStamp time;
    //STEP1_START
    bool hasPosition;
    //STEP1_END
};

```

- path:ft/uc/ftest/ftattach/src/domain/ucm/builder/RrcSetupCmpBuilder.cpp
- type:modify_code
```cpp

Status RrcSetupCmpBuilder::doBuild(lucm_if::Message& msg, FakeSystem& system) const
{
    auto rrcSetupCmp = msg.mutable_ul_dcch_transfer_message();
    FT_ASSERT_VALID_PTR(rrcSetupCmp);

    rrcSetupCmp->set_bpf_ue_id(BPF_UE_ID);
    rrcSetupCmp->set_phy_cell_l3_id(CELL_ID);
    rrcSetupCmp->set_cpf_ue_id(cpfUeId);
    if(GET_SCENCE("NetShare"))
    {
        if(GET_SCENCE("MncLength is three"))
        {
            FT_ASSERT_SUCC_CALL(ns_RrcSetupCmpBuilder::fillNcgiWith3bitMnc(*rrcSetupCmp));
        }
        else
        {
            FT_ASSERT_SUCC_CALL(ns_RrcSetupCmpBuilder::fillNewNcgi(*rrcSetupCmp));
        }
    }
    else if(GET_SCENCE("MncLength is three"))
    {
        FT_ASSERT_SUCC_CALL(ns_RrcSetupCmpBuilder::fillNcgiWith3bitMnc(*rrcSetupCmp));
    }
    else
    {
        FT_ASSERT_SUCC_CALL(ns_RrcSetupCmpBuilder::fillNcgi(*rrcSetupCmp));
    }

    //服务Plmn为根据msg5携带的selectedPLMN_Identity_option在SIB1中找到的plmn
    auto servingPlmn = rrcSetupCmp->mutable_serving_plmn();
    FT_ASSERT_VALID_PTR(servingPlmn);
    servingPlmn->set_mcc(msg5ServingMcc, sizeof(msg5ServingMcc));
    if(GET_SCENCE("MncLength is three"))
    {
        servingPlmn->set_mnc(msg5ServingMnc_3bit, sizeof(msg5ServingMnc_3bit));
    }
    else
    {
        servingPlmn->set_mnc(msg5ServingMnc, sizeof(msg5ServingMnc));
    }

//    rrcSetupCmp->set_uu_asn1_code_stream(RRC_SETUP_CMP_WITH_PDCP, sizeof(RRC_SETUP_CMP_WITH_PDCP));
    rrcSetupCmp->set_uu_asn1_code_stream(RRC_SETUP_CPM, sizeof(RRC_SETUP_CPM));

    if(GET_SCENCE("MoMtUser"))
    {
        if(hasStmsi)
        {
            FT_ASSERT_SUCC_CALL(ns_RrcSetupCmpBuilder::fillTmsi(*rrcSetupCmp));
        }
        else
        {
            FT_ASSERT_SUCC_CALL(ns_RrcSetupCmpBuilder::fillTmsiPart2(*rrcSetupCmp));
        }
    }

    if(isCran)
    {
        ns_RrcSetupCmpBuilder::fillF1SctpInfo(*rrcSetupCmp);
    }

    if (isMsg4Opt)
    {
        std::stringstream msId;
        msId << "msId_" << getServlet();
        rrcSetupCmp->set_phsmsid(msId.str());
    }
    //STEP1_START

    if(hasPosition)
    {
        FT_AUTO_PTR(pos, rrcSetupCmp->mutable_position());
        pos->set_value(lucm_if::UlDcchTranmissionMessage_Position_Type_far_position);
    }
    //STEP1_END

    auto lucmSendMsg5Stamp = rrcSetupCmp->mutable_lucm_send_msg5_time();
    FT_ASSERT_VALID_PTR(lucmSendMsg5Stamp);
    if(time == TIME_FIVE)
    {
        lucmSendMsg5Stamp->set_value(getCurrentSystTimeUsU64()-(WORD64)1000);
    }
    else if(time == TIME_FIVETOTEN)
    {
        lucmSendMsg5Stamp->set_value(getCurrentSystTimeUsU64()-(WORD64)6000);
    }
    else if(time == TIME_TENTOTWENTY)
    {
        lucmSendMsg5Stamp->set_value(getCurrentSystTimeUsU64()-(WORD64)15000);
    }
    else if(time == TIME_TWENTYTOTHIRTY)
    {
        lucmSendMsg5Stamp->set_value(getCurrentSystTimeUsU64()-(WORD64)26000);
    }
    else if(time == TIME_THIRTY)
    {
        lucmSendMsg5Stamp->set_value(getCurrentSystTimeUsU64()-(WORD64)300000);
    }

    return USI_SUCCESS;
}

```
- path:ft/uc/ftest/ftattach/inc/domain/huc/asserter/F1RrcSetupCmpAsserter.h
- type:modify_code
```cpp
DECL_FT_MSG_ASSERTER(F1RrcSetupCmpAsserter)
{
    F1RrcSetupCmpAsserter(bool isMsg4Opt = false, bool hasPosition =false):isMsg4Opt(isMsg4Opt),hasPosition(hasPosition){}
   virtual void doAssert(const void* actualMsg, size_t size, FakeSystem& sys) const override;

private:
    bool isMsg4Opt;
    //STEP1_START
    bool hasPosition;
    //STEP1_END
};

DECL_FT_MSG_ASSERTER(F1OtherUlDcchAsserter)
{
    F1OtherUlDcchAsserter(BYTE streamNo = 2):streamNo(streamNo){}
    virtual void doAssert(const void* actualMsg, size_t size, FakeSystem& sys) const override;

private:
    BYTE streamNo;
};
```

- path:ft/uc/ftest/ftattach/src/domain/huc/asserter/F1RrcSetupCmpAsserter.cpp
- type:modify_code
```cpp

void F1RrcSetupCmpAsserter::doAssert(const void* actualMsg, size_t size, FakeSystem& sys) const
{
    FtAutoMsg<F1ap_F1AP_PDU> F1msg;
    static BYTE buffer[BUFF_LEN_8K];
    Decoder decode(actualMsg , size, buffer, sizeof(buffer), true);
    decode.decode(PerDeF1ap_F1AP_PDU, F1msg.getRef());

    FT_ASSERT_TRUE(F1msg.getRef().t == T_F1ap_F1AP_PDU_initiatingMessage);
    FT_ASSERT_TRUE(F1msg.getRef().u.initiatingMessage->procedureCode == F1AP_id_ULRRCMessageTransfer);
    F1ap_ULRRCMessageTransfer* msg = F1msg.getRef().u.initiatingMessage->union_value.pULRRCMessageTransfer;

    FT_ASSERT_TRUE(msg->protocolIEs.gNB_DU_UE_F1AP_ID == DU_F1AP_UE_ID);
    FT_ASSERT_TRUE(msg->protocolIEs.gNB_CU_UE_F1AP_ID == CU_F1AP_UE_ID);
    FT_ASSERT_TRUE(msg->protocolIEs.sRBID == 1);
    FT_ASSERT_TRUE(msg->protocolIEs.rRCContainer.nocts == sizeof(RRC_SETUP_CPM));
    FT_ASSERT_TRUE(memcmp(msg->protocolIEs.rRCContainer.data, RRC_SETUP_CPM, msg->protocolIEs.rRCContainer.nocts) == 0);

    FT_ASSERT_TRUE(msg->protocolIEs.nRCellIdentity.nbits == 36);
    FT_ASSERT_TRUE(getCellId(msg->protocolIEs.nRCellIdentity.data, sizeof(msg->protocolIEs.nRCellIdentity.data)/sizeof(ASN1OCTET)) == CELL_ID);
    FT_ASSERT_TRUE(getGnbId(msg->protocolIEs.nRCellIdentity.data, sizeof(msg->protocolIEs.nRCellIdentity.data)/sizeof(ASN1OCTET)) == GNB_ID);

    if (isMsg4Opt)
    {
        FT_ASSERT_TRUE(msg->protocolIEs.tOptFlags.privateAsn_optionPresent == USI_PRESENT);
        FT_ASSERT_TRUE(msg->protocolIEs.privateAsn_option.tOptFlags.duPhsAddr_optionPresent == USI_PRESENT);

        std::stringstream msId;
        msId << "msId_" << getServlet();
        FT_ASSERT_TRUE(msg->protocolIEs.privateAsn_option.duPhsAddr_option.nocts == msId.str().length());
        FT_ASSERT_TRUE(memcmp(msg->protocolIEs.privateAsn_option.duPhsAddr_option.data, msId.str().c_str(), msg->protocolIEs.privateAsn_option.duPhsAddr_option.nocts) == 0);
    }
    //STEP2_START
    if(hasPosition)
    {
        FT_ASSERT_TRUE(msg->protocolIEs.tOptFlags.stdPrivateInfoContainer_optionPresent == USI_PRESENT);
        FT_ASSERT_TRUE(msg->protocolIEs.stdPrivateInfoContainer_option.nocts > 0);
        FtAutoMsg<F1Private_PrivateContainerInStdMsg> f1ContainerPrivateInfo;

        FtAutoMsg<FixSizeBuff<BUFF_LEN_1K>> bufferPrivateInfo;
        AUTO_DECODE(decodeF1Private,msg->protocolIEs.stdPrivateInfoContainer_option.data, msg->protocolIEs.stdPrivateInfoContainer_option.nocts , bufferPrivateInfo.getRef().getBuffer(), bufferPrivateInfo.getRef().size(), true);
        auto& f1PrivateInfo = f1ContainerPrivateInfo.getRef();
        FT_ASSERT_SUCC_CALL(decodeF1Private.decode(PerDeF1Private_PrivateContainerInStdMsg, f1PrivateInfo));
        FT_ASSERT_TRUE(f1PrivateInfo.t == T_F1Private_PrivateContainerInStdMsg_ulRrcMessageTransferPrivateInfo);
        FT_ASSERT_TRUE(f1PrivateInfo.u.ulRrcMessageTransferPrivateInfo->protocolIEs.tOptFlags.sinrPosition_optionPresent == USI_PRESENT);
        FT_ASSERT_TRUE(f1PrivateInfo.u.ulRrcMessageTransferPrivateInfo->protocolIEs.sinrPosition_option == F1Private_SinrPosition_Root_farPoint);
    }
    //STEP2_END
}


```
