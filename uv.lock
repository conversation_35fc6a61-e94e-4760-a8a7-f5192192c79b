version = 1
requires-python = ">=3.12"

[[package]]
name = "ft-corpus-eval"
version = "0.1.0"
source = { virtual = "." }
dependencies = [
    { name = "mistune" },
]

[package.metadata]
requires-dist = [{ name = "mistune", specifier = ">=3.0.0" }]

[[package]]
name = "mistune"
version = "3.1.3"
source = { registry = "https://mirrors.aliyun.com/pypi/simple" }
sdist = { url = "https://mirrors.aliyun.com/pypi/packages/c4/79/bda47f7dd7c3c55770478d6d02c9960c430b0cf1773b72366ff89126ea31/mistune-3.1.3.tar.gz", hash = "sha256:a7035c21782b2becb6be62f8f25d3df81ccb4d6fa477a6525b15af06539f02a0" }
wheels = [
    { url = "https://mirrors.aliyun.com/pypi/packages/01/4d/23c4e4f09da849e127e9f123241946c23c1e30f45a88366879e064211815/mistune-3.1.3-py3-none-any.whl", hash = "sha256:1a32314113cff28aa6432e99e522677c8587fd83e3d51c29b82a52409c842bd9" },
]
