#!/usr/bin/env python3
"""
调试MarkdownCorpusParser的渲染器问题
"""

import sys
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass, field

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    import mistune
    from mistune import HTMLRenderer
    from mistune.core import BlockState, InlineState
except ImportError:
    raise ImportError("mistune is required. Please install it with: pip install mistune>=3.0.0")


@dataclass
class MarkdownSection:
    """表示markdown文档中的一个章节"""
    title: str
    level: int
    content: List[str] = field(default_factory=list)
    subsections: Dict[str, 'MarkdownSection'] = field(default_factory=dict)
    code_blocks: List[Dict[str, str]] = field(default_factory=list)
    lists: List[List[str]] = field(default_factory=list)


class DebugCorpusMarkdownRenderer(HTMLRenderer):
    """调试版本的markdown渲染器"""

    def __init__(self):
        super().__init__()
        self.sections: Dict[str, MarkdownSection] = {}
        self.current_section: Optional[MarkdownSection] = None
        self.section_stack: List[MarkdownSection] = []
        self.rdc_info_content: List[str] = []
        self.in_rdc_info = False

    def heading(self, text: str, level: int) -> str:
        """处理标题"""
        text = text.strip()
        print(f"🔍 处理标题: '{text}' (level {level})")
        print(f"   当前section_stack长度: {len(self.section_stack)}")
        print(f"   当前sections: {list(self.sections.keys())}")

        # 创建新的section
        section = MarkdownSection(title=text, level=level)

        # 根据层级管理section层次结构
        if level == 1:  # # 级别 - 顶级section
            print(f"   -> 添加为顶级section")
            self.sections[text] = section
            self.current_section = section
            self.section_stack = [section]
        elif level == 2:  # ## 级别
            if self.current_section and self.current_section.level == 1:
                # 如果当前有一级标题，二级标题作为其子section
                print(f"   -> 添加为一级标题的子section")
                self.current_section.subsections[text] = section
                self.section_stack = [self.current_section, section]
            else:
                # 否则二级标题作为顶级section
                print(f"   -> 添加为顶级section")
                self.sections[text] = section
                self.current_section = section
                self.section_stack = [section]
        elif level == 3 and self.current_section:  # ### 级别
            print(f"   -> 处理三级标题")
            if len(self.section_stack) >= 1:
                # 三级标题总是作为当前section的子section
                current = self.section_stack[-1] if self.section_stack else self.current_section
                print(f"   -> 添加到 '{current.title}' 的subsections")
                current.subsections[text] = section
                if len(self.section_stack) >= 2:
                    self.section_stack = self.section_stack[:2] + [section]
                else:
                    self.section_stack.append(section)
            else:
                print(f"   -> 添加到current_section的subsections")
                self.current_section.subsections[text] = section
                self.section_stack.append(section)

        print(f"   处理后section_stack: {[s.title for s in self.section_stack]}")
        self.in_rdc_info = False
        return super().heading(text, level)

    def paragraph(self, text: str) -> str:
        """处理段落"""
        text = text.strip()
        if not text:
            return super().paragraph(text)

        print(f"📝 处理段落: '{text[:50]}...'")
        print(f"   当前section_stack: {[s.title for s in self.section_stack]}")

        # 检查是否包含RdcInfo标记
        if "**RdcInfo**" in text or text == "RdcInfo":
            self.in_rdc_info = True
            return super().paragraph(text)

        if self.in_rdc_info:
            self.rdc_info_content.append(text)
        elif self.section_stack:
            # 添加到当前最深层的section
            current = self.section_stack[-1]
            print(f"   -> 添加到 '{current.title}' 的content")
            current.content.append(text)

        return super().paragraph(text)


def debug_renderer():
    """调试渲染器"""
    print("🔍 调试MarkdownCorpusParser渲染器")
    print("=" * 60)
    
    test_file = Path("corpus/测试用例 RAN-6612580_ 低速迁出测量场景，存在gumai惩罚的邻区，异系统测量上报无输出.md")
    if not test_file.exists():
        print("❌ 测试文件不存在")
        return
    
    content = test_file.read_text(encoding='utf-8')
    
    # 提取TestInfo部分进行测试
    import re
    testinfo_match = re.search(r'## TestInfo\n(.*?)(?=\n## |$)', content, re.DOTALL)
    if not testinfo_match:
        print("❌ 没有找到TestInfo内容")
        return
    
    testinfo_content = "## TestInfo\n" + testinfo_match.group(1)
    print("TestInfo内容:")
    print(testinfo_content[:300])
    print("\n" + "="*60)
    
    # 使用调试渲染器
    renderer = DebugCorpusMarkdownRenderer()
    markdown = mistune.create_markdown(renderer=renderer)
    
    print("\n开始解析...")
    markdown(testinfo_content)
    
    print("\n" + "="*60)
    print("解析结果:")
    for name, section in renderer.sections.items():
        print(f"Section: {name} (level {section.level})")
        if section.subsections:
            for sub_name, sub_section in section.subsections.items():
                print(f"  Subsection: {sub_name} (level {sub_section.level})")
                print(f"    Content: {sub_section.content}")


if __name__ == "__main__":
    debug_renderer()
