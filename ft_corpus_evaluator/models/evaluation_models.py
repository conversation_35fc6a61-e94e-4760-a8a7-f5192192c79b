from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum
import json
from pathlib import Path

class EvaluationLevel(Enum):
    CRITICAL = "critical"
    WARNING = "warning"
    INFO = "info"

class EvaluationDimension(Enum):
    COMPLETENESS = "completeness"
    CORRECTNESS = "correctness"
    DIFFICULTY = "difficulty"

@dataclass
class MetricResult:
    metric_name: str
    dimension: EvaluationDimension
    score: float
    weight: float
    level: EvaluationLevel
    message: str
    details: Dict[str, Any] = None
    suggestions: List[str] = None

    def to_dict(self) -> Dict[str, Any]:
        return {
            'metric_name': self.metric_name,
            'dimension': self.dimension.value,
            'score': self.score,
            'weight': self.weight,
            'level': self.level.value,
            'message': self.message,
            'details': self.details,
            'suggestions': self.suggestions
        }

@dataclass
class DimensionResult:
    dimension: EvaluationDimension
    overall_score: float
    weighted_score: float
    metric_results: List[MetricResult]
    enabled_metrics: List[str]

    def to_dict(self) -> Dict[str, Any]:
        return {
            'dimension': self.dimension.value,
            'overall_score': self.overall_score,
            'weighted_score': self.weighted_score,
            'metric_results': [result.to_dict() for result in self.metric_results],
            'enabled_metrics': self.enabled_metrics
        }

@dataclass
class EvaluationResult:
    evaluator_name: str
    level: EvaluationLevel
    score: float
    message: str
    details: Dict[str, Any] = None
    suggestions: List[str] = None
    dimension: Optional[EvaluationDimension] = None

    def to_dict(self) -> Dict[str, Any]:
        result = {
            'evaluator_name': self.evaluator_name,
            'level': self.level.value,
            'score': self.score,
            'message': self.message,
            'details': self.details,
            'suggestions': self.suggestions
        }
        if self.dimension:
            result['dimension'] = self.dimension.value
        return result

@dataclass
class CorpusEvaluationReport:
    corpus_id: str
    file_path: str
    overall_score: float
    evaluation_results: List[EvaluationResult]
    metadata: Dict[str, Any] = None

    def to_dict(self) -> Dict[str, Any]:
        return {
            'corpus_id': self.corpus_id,
            'file_path': self.file_path,
            'overall_score': self.overall_score,
            'evaluation_results': [result.to_dict() for result in self.evaluation_results],
            'metadata': self.metadata
        }