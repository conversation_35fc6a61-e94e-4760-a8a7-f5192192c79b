from typing import List, Dict, Any, Type, Optional
from ..evaluators.base_evaluator import BaseEvaluator
from ..evaluators.dimension_evaluator import DimensionEvaluator
from ..models.corpus_model import FTCorpus
from ..models.evaluation_models import (
    EvaluationResult, CorpusEvaluationReport, EvaluationLevel, EvaluationDimension
)
import importlib
import pkgutil

class EvaluatorManager:
    def __init__(self, dimension_config_path: Optional[str] = None):
        self.evaluators: Dict[str, BaseEvaluator] = {}
        self.config: Dict[str, Any] = {}
        self.dimension_evaluator = DimensionEvaluator(dimension_config_path)
        self.register_evaluator(self.dimension_evaluator)
    
    def register_evaluator(self, evaluator: BaseEvaluator):
        self.evaluators[evaluator.name] = evaluator
        if evaluator.name in self.config:
            evaluator.set_config(self.config[evaluator.name])
    
    def unregister_evaluator(self, evaluator_name: str):
        if evaluator_name in self.evaluators:
            del self.evaluators[evaluator_name]
    
    def load_evaluators_from_package(self, package_name: str):
        try:
            package = importlib.import_module(package_name)
            for _, module_name, _ in pkgutil.iter_modules(package.__path__):
                module = importlib.import_module(f"{package_name}.{module_name}")
                
                for attr_name in dir(module):
                    attr = getattr(module, attr_name)
                    if (isinstance(attr, type) and 
                        issubclass(attr, BaseEvaluator) and 
                        attr != BaseEvaluator):
                        evaluator_instance = attr()
                        self.register_evaluator(evaluator_instance)
        except ImportError as e:
            print(f"Failed to load evaluators from {package_name}: {e}")
    
    def set_config(self, config: Dict[str, Any]):
        self.config = config
        for evaluator_name, evaluator in self.evaluators.items():
            if evaluator_name in config:
                evaluator.set_config(config[evaluator_name])
    
    def evaluate_corpus(self, corpus: FTCorpus) -> CorpusEvaluationReport:
        all_results = []
        total_weighted_score = 0
        total_weight = 0
        
        for evaluator in self.evaluators.values():
            try:
                results = evaluator.evaluate(corpus)
                all_results.extend(results)
                
                for result in results:
                    total_weighted_score += result.score * evaluator.weight
                    total_weight += evaluator.weight
            except Exception as e:
                error_result = EvaluationResult(
                    evaluator_name=evaluator.name,
                    level=EvaluationLevel.CRITICAL,
                    score=0,
                    message=f"Evaluator failed: {str(e)}",
                    details={"error": str(e)}
                )
                all_results.append(error_result)
        
        overall_score = total_weighted_score / total_weight if total_weight > 0 else 0
        
        return CorpusEvaluationReport(
            corpus_id=corpus.rdc_info.rdc_id,
            file_path=corpus.file_path,
            overall_score=overall_score,
            evaluation_results=all_results,
            metadata={
                "evaluator_count": len(self.evaluators),
                "total_issues": len([r for r in all_results if r.level != EvaluationLevel.INFO])
            }
        )
    
    def get_evaluator_info(self) -> Dict[str, Dict[str, Any]]:
        return {
            name: {
                "name": evaluator.name,
                "weight": evaluator.weight,
                "config": evaluator.config
            }
            for name, evaluator in self.evaluators.items()
        }

    def evaluate_by_dimension(self, corpus: FTCorpus,
                            dimension: EvaluationDimension) -> Optional[Any]:
        return self.dimension_evaluator.get_dimension_result(corpus, dimension)

    def enable_dimension_metric(self, dimension: EvaluationDimension, metric_name: str):
        self.dimension_evaluator.enable_metric(dimension, metric_name)

    def disable_dimension_metric(self, dimension: EvaluationDimension, metric_name: str):
        self.dimension_evaluator.disable_metric(dimension, metric_name)

    def get_dimension_metrics_status(self) -> Dict[str, Dict[str, bool]]:
        return self.dimension_evaluator.get_all_metrics_status()

    def get_enabled_dimension_metrics(self, dimension: EvaluationDimension) -> List[str]:
        return self.dimension_evaluator.get_enabled_metrics(dimension)