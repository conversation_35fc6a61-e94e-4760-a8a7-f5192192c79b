import re
from typing import List, Dict, Any, Set
from .dimension_framework import BaseDimension, BaseMetric
from ..models.evaluation_models import EvaluationDimension, EvaluationLevel, MetricResult
from ..models.corpus_model import FTCorpus


class TechnicalComplexityMetric(BaseMetric):
    def __init__(self, weight: float = 1.0, enabled: bool = True):
        super().__init__("technical_complexity", weight, enabled)
    
    def evaluate(self, corpus: FTCorpus) -> MetricResult:
        complexity_score = 0
        complexity_factors = []
        
        technical_keywords = self.config.get('technical_keywords', {
            'high': ['并发', '多线程', '分布式', '集群', '负载均衡', '缓存', '数据库事务', '异步', '微服务'],
            'medium': ['接口', 'API', '配置', '网络', '协议', '算法', '数据结构', '性能'],
            'low': ['基本', '简单', '单一', '直接', '基础']
        })
        
        all_text = f"{corpus.test_info.test_title} {' '.join(corpus.test_info.tc_steps)}"
        
        high_count = sum(1 for keyword in technical_keywords['high'] if keyword in all_text)
        medium_count = sum(1 for keyword in technical_keywords['medium'] if keyword in all_text)
        low_count = sum(1 for keyword in technical_keywords['low'] if keyword in all_text)
        
        complexity_score += high_count * 15
        complexity_score += medium_count * 8
        complexity_score += low_count * 3
        
        if high_count > 0:
            complexity_factors.append(f"High complexity keywords: {high_count}")
        if medium_count > 0:
            complexity_factors.append(f"Medium complexity keywords: {medium_count}")
        
        code_complexity = self._analyze_code_complexity(corpus.code_snippets)
        complexity_score += code_complexity
        if code_complexity > 20:
            complexity_factors.append("Complex code patterns detected")
        
        steps_complexity = self._analyze_steps_complexity(corpus.test_info.tc_steps)
        complexity_score += steps_complexity
        if steps_complexity > 15:
            complexity_factors.append("Complex test procedures")
        
        complexity_score = min(100, complexity_score)
        
        if complexity_score >= 70:
            level = EvaluationLevel.INFO
            message = "High technical complexity"
            difficulty_level = "High"
        elif complexity_score >= 40:
            level = EvaluationLevel.INFO
            message = "Medium technical complexity"
            difficulty_level = "Medium"
        else:
            level = EvaluationLevel.INFO
            message = "Low technical complexity"
            difficulty_level = "Low"
        
        return self.create_result(
            score=complexity_score,
            level=level,
            message=message,
            dimension=EvaluationDimension.DIFFICULTY,
            details={
                "difficulty_level": difficulty_level,
                "complexity_factors": complexity_factors,
                "keyword_counts": {
                    "high": high_count,
                    "medium": medium_count,
                    "low": low_count
                },
                "code_complexity": code_complexity,
                "steps_complexity": steps_complexity
            },
            suggestions=[]
        )
    
    def _analyze_code_complexity(self, code_snippets: List) -> int:
        complexity = 0
        complex_patterns = [
            r'class\s+\w+.*{',  # Class definitions
            r'template\s*<',     # Templates
            r'std::\w+',         # STL usage
            r'#include\s*<\w+>', # System includes
            r'virtual\s+\w+',    # Virtual functions
            r'operator\s*\w+',   # Operator overloading
        ]
        
        for snippet in code_snippets:
            content = snippet.content
            for pattern in complex_patterns:
                matches = len(re.findall(pattern, content))
                complexity += matches * 3
            
            lines = content.split('\n')
            if len(lines) > 50:
                complexity += 10
            elif len(lines) > 20:
                complexity += 5
        
        return min(complexity, 30)
    
    def _analyze_steps_complexity(self, steps_text: str) -> int:
        complexity = 0
        complex_indicators = [
            '同时', '并行', '循环', '重复', '条件', '判断', '分支',
            '多个', '批量', '组合', '序列', '依赖', '关联'
        ]

        # 统计复杂性指标出现次数
        for indicator in complex_indicators:
            complexity += steps_text.count(indicator) * 2

        # 根据内容长度判断复杂度
        content_length = len(steps_text.strip())
        if content_length > 1000:
            complexity += 10
        elif content_length > 500:
            complexity += 5

        return min(complexity, 25)


class BusinessComplexityMetric(BaseMetric):
    def __init__(self, weight: float = 1.0, enabled: bool = True):
        super().__init__("business_complexity", weight, enabled)
    
    def evaluate(self, corpus: FTCorpus) -> MetricResult:
        complexity_score = 0
        business_factors = []
        
        business_domains = self.config.get('business_domains', {
            'telecom': ['RAN', '5G', 'LTE', '基站', '小区', '切换', '测量', '信令'],
            'network': ['网络', '协议', '路由', '交换', '传输', '接口'],
            'system': ['系统', '平台', '架构', '模块', '组件', '服务']
        })
        
        all_text = f"{corpus.test_info.test_title} {' '.join(corpus.test_info.tc_steps)}"
        all_tags = (corpus.tag_identification.business_content_scene_tags + 
                   corpus.tag_identification.code_modify_scene_tags)
        tag_text = ' '.join(all_tags)
        
        domain_matches = {}
        for domain, keywords in business_domains.items():
            matches = sum(1 for keyword in keywords if keyword in all_text or keyword in tag_text)
            if matches > 0:
                domain_matches[domain] = matches
                complexity_score += matches * 8
        
        if domain_matches:
            business_factors.append(f"Business domains: {list(domain_matches.keys())}")
        
        scenario_complexity = self._analyze_scenario_complexity(corpus)
        complexity_score += scenario_complexity
        if scenario_complexity > 20:
            business_factors.append("Complex business scenario")
        
        integration_complexity = self._analyze_integration_complexity(corpus)
        complexity_score += integration_complexity
        if integration_complexity > 15:
            business_factors.append("Multi-system integration")
        
        complexity_score = min(100, complexity_score)
        
        if complexity_score >= 60:
            level = EvaluationLevel.INFO
            message = "High business complexity"
            difficulty_level = "High"
        elif complexity_score >= 30:
            level = EvaluationLevel.INFO
            message = "Medium business complexity"
            difficulty_level = "Medium"
        else:
            level = EvaluationLevel.INFO
            message = "Low business complexity"
            difficulty_level = "Low"
        
        return self.create_result(
            score=complexity_score,
            level=level,
            message=message,
            dimension=EvaluationDimension.DIFFICULTY,
            details={
                "difficulty_level": difficulty_level,
                "business_factors": business_factors,
                "domain_matches": domain_matches,
                "scenario_complexity": scenario_complexity,
                "integration_complexity": integration_complexity
            },
            suggestions=[]
        )
    
    def _analyze_scenario_complexity(self, corpus: FTCorpus) -> int:
        complexity = 0
        complex_scenarios = [
            '优化', '异常', '故障', '恢复', '切换', '迁移',
            '负载', '压力', '性能', '容量', '扩展'
        ]
        
        all_text = corpus.test_info.test_title + ' ' + ' '.join(corpus.test_info.tc_steps)
        for scenario in complex_scenarios:
            if scenario in all_text:
                complexity += 5
        
        return min(complexity, 30)
    
    def _analyze_integration_complexity(self, corpus: FTCorpus) -> int:
        complexity = 0
        integration_keywords = [
            '接口', '集成', '对接', '交互', '通信', '协调',
            '同步', '异步', '消息', '事件', '回调'
        ]
        
        all_text = corpus.test_info.test_title + ' ' + ' '.join(corpus.test_info.tc_steps)
        for keyword in integration_keywords:
            if keyword in all_text:
                complexity += 3
        
        if len(corpus.code_snippets) > 3:
            complexity += 10
        
        return min(complexity, 25)


class TestCoverageComplexityMetric(BaseMetric):
    def __init__(self, weight: float = 0.8, enabled: bool = True):
        super().__init__("test_coverage_complexity", weight, enabled)
    
    def evaluate(self, corpus: FTCorpus) -> MetricResult:
        coverage_score = 0
        coverage_factors = []
        
        test_types = self._identify_test_types(corpus)
        coverage_score += len(test_types) * 10
        if test_types:
            coverage_factors.append(f"Test types: {', '.join(test_types)}")
        
        test_scenarios = self._count_test_scenarios(corpus)
        coverage_score += min(test_scenarios * 5, 25)
        if test_scenarios > 3:
            coverage_factors.append(f"Multiple scenarios: {test_scenarios}")
        
        edge_cases = self._identify_edge_cases(corpus)
        coverage_score += len(edge_cases) * 8
        if edge_cases:
            coverage_factors.append(f"Edge cases: {len(edge_cases)}")
        
        data_variations = self._analyze_data_variations(corpus)
        coverage_score += data_variations
        if data_variations > 10:
            coverage_factors.append("Multiple data variations")
        
        coverage_score = min(100, coverage_score)
        
        if coverage_score >= 70:
            level = EvaluationLevel.INFO
            message = "Comprehensive test coverage"
            difficulty_level = "High"
        elif coverage_score >= 40:
            level = EvaluationLevel.INFO
            message = "Moderate test coverage"
            difficulty_level = "Medium"
        else:
            level = EvaluationLevel.WARNING
            message = "Limited test coverage"
            difficulty_level = "Low"
        
        return self.create_result(
            score=coverage_score,
            level=level,
            message=message,
            dimension=EvaluationDimension.DIFFICULTY,
            details={
                "difficulty_level": difficulty_level,
                "coverage_factors": coverage_factors,
                "test_types": test_types,
                "test_scenarios": test_scenarios,
                "edge_cases": edge_cases,
                "data_variations": data_variations
            },
            suggestions=[]
        )
    
    def _identify_test_types(self, corpus: FTCorpus) -> List[str]:
        test_types = []
        type_keywords = {
            'functional': ['功能', '业务', '逻辑'],
            'performance': ['性能', '压力', '负载', '响应时间'],
            'integration': ['集成', '接口', '对接'],
            'regression': ['回归', '兼容', '向后'],
            'boundary': ['边界', '极限', '最大', '最小'],
            'negative': ['异常', '错误', '失败', '无效']
        }
        
        all_text = corpus.test_info.test_title + ' ' + ' '.join(corpus.test_info.tc_steps)
        for test_type, keywords in type_keywords.items():
            if any(keyword in all_text for keyword in keywords):
                test_types.append(test_type)
        
        return test_types
    
    def _count_test_scenarios(self, corpus: FTCorpus) -> int:
        scenarios = set()
        scenario_indicators = ['场景', '情况', '条件', '状态', '模式']
        
        for step in corpus.test_info.tc_steps:
            for indicator in scenario_indicators:
                if indicator in step:
                    scenarios.add(step[:20])  # Use first 20 chars as scenario identifier
        
        return len(scenarios)
    
    def _identify_edge_cases(self, corpus: FTCorpus) -> List[str]:
        edge_cases = []
        edge_keywords = [
            '边界', '极限', '最大', '最小', '空', '零', '满',
            '异常', '错误', '失败', '超时', '中断', '断开'
        ]
        
        all_text = corpus.test_info.test_title + ' ' + ' '.join(corpus.test_info.tc_steps)
        for keyword in edge_keywords:
            if keyword in all_text:
                edge_cases.append(keyword)
        
        return list(set(edge_cases))
    
    def _analyze_data_variations(self, corpus: FTCorpus) -> int:
        variations = 0
        variation_indicators = [
            '不同', '多种', '各种', '多个', '批量',
            '随机', '变化', '动态', '可变'
        ]
        
        all_text = corpus.test_info.test_title + ' ' + ' '.join(corpus.test_info.tc_steps)
        for indicator in variation_indicators:
            if indicator in all_text:
                variations += 3
        
        return min(variations, 20)


class DifficultyDimension(BaseDimension):
    def __init__(self):
        super().__init__(EvaluationDimension.DIFFICULTY)
        self.register_metric(TechnicalComplexityMetric())
        self.register_metric(BusinessComplexityMetric())
        self.register_metric(TestCoverageComplexityMetric())
