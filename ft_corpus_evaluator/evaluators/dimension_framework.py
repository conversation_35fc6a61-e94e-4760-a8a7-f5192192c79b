from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Type
from ..models.evaluation_models import (
    EvaluationDimension, MetricResult, DimensionResult, EvaluationLevel
)
from ..models.corpus_model import FTCorpus


class BaseMetric(ABC):
    def __init__(self, name: str, weight: float = 1.0, enabled: bool = True):
        self.name = name
        self.weight = weight
        self.enabled = enabled
        self.config = {}
    
    @abstractmethod
    def evaluate(self, corpus: FTCorpus) -> MetricResult:
        pass
    
    def set_config(self, config: Dict[str, Any]):
        self.config = config
    
    def create_result(self, score: float, level: EvaluationLevel, 
                     message: str, dimension: EvaluationDimension,
                     details: Dict[str, Any] = None,
                     suggestions: List[str] = None) -> MetricResult:
        return MetricResult(
            metric_name=self.name,
            dimension=dimension,
            score=score,
            weight=self.weight,
            level=level,
            message=message,
            details=details or {},
            suggestions=suggestions or []
        )


class BaseDimension(ABC):
    def __init__(self, dimension: EvaluationDimension):
        self.dimension = dimension
        self.metrics: Dict[str, BaseMetric] = {}
        self.config = {}
    
    def register_metric(self, metric: BaseMetric):
        self.metrics[metric.name] = metric
        if metric.name in self.config.get('metrics', {}):
            metric.set_config(self.config['metrics'][metric.name])
    
    def unregister_metric(self, metric_name: str):
        if metric_name in self.metrics:
            del self.metrics[metric_name]
    
    def set_config(self, config: Dict[str, Any]):
        self.config = config
        for metric_name, metric_config in config.get('metrics', {}).items():
            if metric_name in self.metrics:
                self.metrics[metric_name].set_config(metric_config)
    
    def evaluate(self, corpus: FTCorpus) -> DimensionResult:
        metric_results = []
        total_weighted_score = 0
        total_weight = 0
        enabled_metrics = []
        
        for metric in self.metrics.values():
            if not metric.enabled:
                continue
                
            enabled_metrics.append(metric.name)
            try:
                result = metric.evaluate(corpus)
                metric_results.append(result)
                total_weighted_score += result.score * metric.weight
                total_weight += metric.weight
            except Exception as e:
                error_result = MetricResult(
                    metric_name=metric.name,
                    dimension=self.dimension,
                    score=0,
                    weight=metric.weight,
                    level=EvaluationLevel.CRITICAL,
                    message=f"Metric evaluation failed: {str(e)}",
                    details={"error": str(e)}
                )
                metric_results.append(error_result)
        
        overall_score = total_weighted_score / total_weight if total_weight > 0 else 0
        weighted_score = total_weighted_score
        
        return DimensionResult(
            dimension=self.dimension,
            overall_score=overall_score,
            weighted_score=weighted_score,
            metric_results=metric_results,
            enabled_metrics=enabled_metrics
        )


class DimensionManager:
    def __init__(self):
        self.dimensions: Dict[EvaluationDimension, BaseDimension] = {}
        self.config = {}
    
    def register_dimension(self, dimension: BaseDimension):
        self.dimensions[dimension.dimension] = dimension
        if dimension.dimension.value in self.config:
            dimension.set_config(self.config[dimension.dimension.value])
    
    def unregister_dimension(self, dimension_type: EvaluationDimension):
        if dimension_type in self.dimensions:
            del self.dimensions[dimension_type]
    
    def register_metric(self, dimension_type: EvaluationDimension, metric: BaseMetric):
        if dimension_type in self.dimensions:
            self.dimensions[dimension_type].register_metric(metric)
    
    def set_config(self, config: Dict[str, Any]):
        self.config = config
        for dimension_name, dimension_config in config.items():
            dimension_enum = None
            try:
                dimension_enum = EvaluationDimension(dimension_name)
            except ValueError:
                continue
                
            if dimension_enum in self.dimensions:
                self.dimensions[dimension_enum].set_config(dimension_config)
    
    def evaluate_all_dimensions(self, corpus: FTCorpus) -> Dict[EvaluationDimension, DimensionResult]:
        results = {}
        for dimension_type, dimension in self.dimensions.items():
            results[dimension_type] = dimension.evaluate(corpus)
        return results
    
    def evaluate_dimension(self, corpus: FTCorpus, 
                          dimension_type: EvaluationDimension) -> Optional[DimensionResult]:
        if dimension_type in self.dimensions:
            return self.dimensions[dimension_type].evaluate(corpus)
        return None
    
    def get_enabled_metrics(self, dimension_type: EvaluationDimension) -> List[str]:
        if dimension_type in self.dimensions:
            return [name for name, metric in self.dimensions[dimension_type].metrics.items() 
                   if metric.enabled]
        return []
    
    def enable_metric(self, dimension_type: EvaluationDimension, metric_name: str):
        if (dimension_type in self.dimensions and 
            metric_name in self.dimensions[dimension_type].metrics):
            self.dimensions[dimension_type].metrics[metric_name].enabled = True
    
    def disable_metric(self, dimension_type: EvaluationDimension, metric_name: str):
        if (dimension_type in self.dimensions and 
            metric_name in self.dimensions[dimension_type].metrics):
            self.dimensions[dimension_type].metrics[metric_name].enabled = False


class MetricRegistry:
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance.metrics = {}
        return cls._instance
    
    def register_metric_class(self, dimension: EvaluationDimension, 
                            metric_name: str, metric_class: Type[BaseMetric]):
        key = f"{dimension.value}.{metric_name}"
        self.metrics[key] = metric_class
    
    def create_metric(self, dimension: EvaluationDimension, 
                     metric_name: str, **kwargs) -> Optional[BaseMetric]:
        key = f"{dimension.value}.{metric_name}"
        if key in self.metrics:
            return self.metrics[key](**kwargs)
        return None
    
    def get_available_metrics(self, dimension: EvaluationDimension) -> List[str]:
        prefix = f"{dimension.value}."
        return [key[len(prefix):] for key in self.metrics.keys() if key.startswith(prefix)]
    
    def get_all_available_metrics(self) -> Dict[str, List[str]]:
        result = {}
        for dimension in EvaluationDimension:
            result[dimension.value] = self.get_available_metrics(dimension)
        return result
