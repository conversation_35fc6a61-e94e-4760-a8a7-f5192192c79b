from typing import List, Dict, Any, Optional
from .base_evaluator import BaseEvaluator
from .dimension_framework import DimensionManager
from .completeness_dimension import CompletenessDimension
from .correctness_dimension import CorrectnessDimension
from .difficulty_dimension import DifficultyDimension
from ..models.evaluation_models import (
    EvaluationResult, EvaluationLevel, EvaluationDimension, DimensionResult
)
from ..models.corpus_model import FTCorpus
import json
from pathlib import Path


class DimensionEvaluator(BaseEvaluator):
    def __init__(self, config_path: Optional[str] = None):
        super().__init__("dimension_evaluator", 1.0)
        self.dimension_manager = DimensionManager()
        self._initialize_dimensions()
        
        if config_path:
            self._load_config(config_path)
        else:
            self._load_default_config()
    
    def _initialize_dimensions(self):
        self.dimension_manager.register_dimension(CompletenessDimension())
        self.dimension_manager.register_dimension(CorrectnessDimension())
        self.dimension_manager.register_dimension(DifficultyDimension())
    
    def _load_config(self, config_path: str):
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            self.set_config(config)
        except Exception as e:
            print(f"Failed to load config from {config_path}: {e}")
            self._load_default_config()
    
    def _load_default_config(self):
        default_config_path = Path(__file__).parent.parent / "config" / "evaluator_config.json"
        if default_config_path.exists():
            self._load_config(str(default_config_path))
    
    def set_config(self, config: Dict[str, Any]):
        super().set_config(config)
        
        dimensions_config = config.get('dimensions', {})
        self.dimension_manager.set_config(dimensions_config)
        
        for dimension_name, dimension_config in dimensions_config.items():
            try:
                dimension_enum = EvaluationDimension(dimension_name)
                if not dimension_config.get('enabled', True):
                    self.dimension_manager.unregister_dimension(dimension_enum)
            except ValueError:
                continue
    
    def evaluate(self, corpus: FTCorpus) -> List[EvaluationResult]:
        results = []
        
        dimension_results = self.dimension_manager.evaluate_all_dimensions(corpus)
        
        overall_scores = {}
        total_weighted_score = 0
        total_weight = 0
        
        for dimension_type, dimension_result in dimension_results.items():
            dimension_config = self.config.get('dimensions', {}).get(dimension_type.value, {})
            dimension_weight = dimension_config.get('weight', 1.0)
            
            overall_scores[dimension_type.value] = {
                'score': dimension_result.overall_score,
                'weighted_score': dimension_result.overall_score * dimension_weight,
                'weight': dimension_weight,
                'level': self._determine_dimension_level(dimension_result.overall_score),
                'metric_count': len(dimension_result.metric_results),
                'enabled_metrics': dimension_result.enabled_metrics
            }
            
            total_weighted_score += dimension_result.overall_score * dimension_weight
            total_weight += dimension_weight
            
            dimension_evaluation_result = self._create_dimension_evaluation_result(
                dimension_type, dimension_result, dimension_weight
            )
            results.append(dimension_evaluation_result)
        
        overall_score = total_weighted_score / total_weight if total_weight > 0 else 0
        
        comprehensive_result = self._create_comprehensive_result(
            overall_score, overall_scores, dimension_results
        )
        results.append(comprehensive_result)
        
        return results
    
    def _determine_dimension_level(self, score: float) -> EvaluationLevel:
        if score < 60:
            return EvaluationLevel.CRITICAL
        elif score < 80:
            return EvaluationLevel.WARNING
        else:
            return EvaluationLevel.INFO
    
    def _create_dimension_evaluation_result(self, dimension_type: EvaluationDimension, 
                                          dimension_result: DimensionResult, 
                                          weight: float) -> EvaluationResult:
        level = self._determine_dimension_level(dimension_result.overall_score)
        
        dimension_names = {
            EvaluationDimension.COMPLETENESS: "完整性",
            EvaluationDimension.CORRECTNESS: "正确性", 
            EvaluationDimension.DIFFICULTY: "难度分级"
        }
        
        dimension_name = dimension_names.get(dimension_type, dimension_type.value)
        
        if level == EvaluationLevel.CRITICAL:
            message = f"{dimension_name}维度评分较低，需要重点改进"
        elif level == EvaluationLevel.WARNING:
            message = f"{dimension_name}维度评分中等，建议优化"
        else:
            message = f"{dimension_name}维度评分良好"
        
        suggestions = []
        for metric_result in dimension_result.metric_results:
            if metric_result.level in [EvaluationLevel.CRITICAL, EvaluationLevel.WARNING]:
                suggestions.extend(metric_result.suggestions[:2])
        
        return EvaluationResult(
            evaluator_name=f"{self.name}_{dimension_type.value}",
            level=level,
            score=dimension_result.overall_score,
            message=message,
            dimension=dimension_type,
            details={
                'dimension': dimension_type.value,
                'dimension_name': dimension_name,
                'overall_score': dimension_result.overall_score,
                'weighted_score': dimension_result.weighted_score,
                'weight': weight,
                'metric_results': [result.to_dict() for result in dimension_result.metric_results],
                'enabled_metrics': dimension_result.enabled_metrics,
                'metric_count': len(dimension_result.metric_results)
            },
            suggestions=suggestions[:5]
        )
    
    def _create_comprehensive_result(self, overall_score: float, 
                                   overall_scores: Dict[str, Any],
                                   dimension_results: Dict[EvaluationDimension, DimensionResult]) -> EvaluationResult:
        level = self._determine_dimension_level(overall_score)
        
        if level == EvaluationLevel.CRITICAL:
            message = f"综合评分: {overall_score:.1f} - 质量较低，需要全面改进"
        elif level == EvaluationLevel.WARNING:
            message = f"综合评分: {overall_score:.1f} - 质量中等，建议优化"
        else:
            message = f"综合评分: {overall_score:.1f} - 质量良好"
        
        weak_dimensions = []
        strong_dimensions = []
        
        for dim_name, dim_info in overall_scores.items():
            if dim_info['score'] < 60:
                weak_dimensions.append(dim_name)
            elif dim_info['score'] >= 80:
                strong_dimensions.append(dim_name)
        
        suggestions = []
        if weak_dimensions:
            suggestions.append(f"重点改进维度: {', '.join(weak_dimensions)}")
        
        for dimension_type, dimension_result in dimension_results.items():
            critical_metrics = [
                result for result in dimension_result.metric_results 
                if result.level == EvaluationLevel.CRITICAL
            ]
            if critical_metrics:
                suggestions.append(
                    f"{dimension_type.value}维度关键问题: {critical_metrics[0].message}"
                )
        
        return EvaluationResult(
            evaluator_name=f"{self.name}_comprehensive",
            level=level,
            score=overall_score,
            message=message,
            details={
                'overall_score': overall_score,
                'dimension_scores': overall_scores,
                'weak_dimensions': weak_dimensions,
                'strong_dimensions': strong_dimensions,
                'total_metrics': sum(len(dr.metric_results) for dr in dimension_results.values()),
                'evaluation_summary': {
                    'completeness': overall_scores.get('completeness', {}).get('score', 0),
                    'correctness': overall_scores.get('correctness', {}).get('score', 0),
                    'difficulty': overall_scores.get('difficulty', {}).get('score', 0)
                }
            },
            suggestions=suggestions[:5]
        )
    
    def get_dimension_result(self, corpus: FTCorpus, 
                           dimension: EvaluationDimension) -> Optional[DimensionResult]:
        return self.dimension_manager.evaluate_dimension(corpus, dimension)
    
    def enable_metric(self, dimension: EvaluationDimension, metric_name: str):
        self.dimension_manager.enable_metric(dimension, metric_name)
    
    def disable_metric(self, dimension: EvaluationDimension, metric_name: str):
        self.dimension_manager.disable_metric(dimension, metric_name)
    
    def get_enabled_metrics(self, dimension: EvaluationDimension) -> List[str]:
        return self.dimension_manager.get_enabled_metrics(dimension)
    
    def get_all_metrics_status(self) -> Dict[str, Dict[str, bool]]:
        status = {}
        for dimension in EvaluationDimension:
            if dimension in self.dimension_manager.dimensions:
                dimension_obj = self.dimension_manager.dimensions[dimension]
                status[dimension.value] = {
                    metric_name: metric.enabled 
                    for metric_name, metric in dimension_obj.metrics.items()
                }
        return status
