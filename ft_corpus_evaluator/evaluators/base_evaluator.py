from abc import ABC, abstractmethod
from typing import List, Dict, Any
from ..models.corpus_model import FTCorpus
from ..models.evaluation_models import EvaluationResult


class BaseEvaluator(ABC):
    def __init__(self, name: str, weight: float = 1.0):
        self.name = name
        self.weight = weight
        self.config = {}

    @abstractmethod
    def evaluate(self, corpus: FTCorpus) -> List[EvaluationResult]:
        pass

    def set_config(self, config: Dict[str, Any]):
        self.config = config

