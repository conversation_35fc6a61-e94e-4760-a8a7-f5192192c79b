import argparse
from pathlib import Path
from ..core.evaluator_manager import <PERSON><PERSON>atorManager
from ..core.batch_processor import BatchProcessor
from ..models.evaluation_models import EvaluationDimension

def main():
    parser = argparse.ArgumentParser(description="FT Corpus Quality Evaluation Tool - Dimension-based Evaluation")
    parser.add_argument("corpus_dir", type=str, nargs='?', help="Directory containing corpus files")
    parser.add_argument("-o", "--output", type=str, help="Output directory for results")
    parser.add_argument("-c", "--config", type=str, help="Configuration file path")
    parser.add_argument("-p", "--pattern", type=str, default="*.md", help="File pattern to match")
    parser.add_argument("-w", "--workers", type=int, default=4, help="Number of worker threads")
    parser.add_argument("--dimensions", type=str, nargs="+",
                       choices=["completeness", "correctness", "difficulty", "all"],
                       default=["all"], help="Evaluation dimensions to run")
    parser.add_argument("--dimension-only", type=str,
                       choices=["completeness", "correctness", "difficulty"],
                       help="Run evaluation for a specific dimension only")
    parser.add_argument("--list-metrics", action="store_true",
                       help="List all available metrics for each dimension")
    parser.add_argument("--disable-metric", type=str, nargs=2, metavar=("DIMENSION", "METRIC"),
                       action="append", help="Disable specific metric (dimension metric_name)")
    parser.add_argument("--enable-metric", type=str, nargs=2, metavar=("DIMENSION", "METRIC"),
                       action="append", help="Enable specific metric (dimension metric_name)")
    
    args = parser.parse_args()

    # Handle list metrics command
    if args.list_metrics:
        list_available_metrics()
        return 0

    # Check if corpus_dir is provided for other operations
    if not args.corpus_dir:
        print("Error: corpus_dir is required for evaluation operations")
        parser.print_help()
        return 1

    corpus_dir = Path(args.corpus_dir)
    if not corpus_dir.exists():
        print(f"Error: Corpus directory {corpus_dir} does not exist")
        return 1

    output_dir = Path(args.output) if args.output else corpus_dir / "evaluation_results"

    # Create evaluator manager with dimension-based evaluation
    config_path = args.config if args.config else None
    evaluator_manager = EvaluatorManager(dimension_config_path=config_path)

    # Handle metric enable/disable commands
    if args.disable_metric:
        for dimension_name, metric_name in args.disable_metric:
            try:
                dimension = EvaluationDimension(dimension_name)
                evaluator_manager.disable_dimension_metric(dimension, metric_name)
                print(f"Disabled metric '{metric_name}' in dimension '{dimension_name}'")
            except ValueError:
                print(f"Warning: Unknown dimension '{dimension_name}'")

    if args.enable_metric:
        for dimension_name, metric_name in args.enable_metric:
            try:
                dimension = EvaluationDimension(dimension_name)
                evaluator_manager.enable_dimension_metric(dimension, metric_name)
                print(f"Enabled metric '{metric_name}' in dimension '{dimension_name}'")
            except ValueError:
                print(f"Warning: Unknown dimension '{dimension_name}'")
    
    processor = BatchProcessor(evaluator_manager, max_workers=args.workers)

    print(f"Processing corpus files in {corpus_dir}")
    print(f"Using pattern: {args.pattern}")
    print(f"Output directory: {output_dir}")

    # Handle dimension-specific evaluation
    if args.dimension_only:
        try:
            dimension = EvaluationDimension(args.dimension_only)
            print(f"Running evaluation for dimension: {args.dimension_only}")
            results = processor.process_directory_by_dimension(
                corpus_dir, dimension, args.pattern, output_dir
            )
        except ValueError:
            print(f"Error: Unknown dimension '{args.dimension_only}'")
            return 1
    else:
        # Run comprehensive evaluation
        print("Running comprehensive evaluation for all dimensions")
        results = processor.process_directory(corpus_dir, args.pattern, output_dir)

    if "error" in results:
        print(f"Error: {results['error']}")
        return 1

    # Display results
    if args.dimension_only:
        display_dimension_results(results)
    else:
        display_comprehensive_results(results)

    print(f"\nResults saved to: {output_dir}")
    if args.dimension_only:
        print(f"- Dimension results: {output_dir / f'dimension_{args.dimension_only}_results.json'}")
    else:
        print(f"- JSON report: {output_dir / 'evaluation_results.json'}")
        print(f"- HTML report: {output_dir / 'evaluation_report.html'}")

    return 0


def list_available_metrics():
    """List all available metrics for each dimension"""
    print("Available Evaluation Dimensions and Metrics:")
    print("=" * 50)

    # Create a temporary evaluator manager to get metrics info
    manager = EvaluatorManager()
    metrics_status = manager.get_dimension_metrics_status()

    dimension_names = {
        "completeness": "完整性维度",
        "correctness": "正确性维度",
        "difficulty": "难度分级维度"
    }

    for dimension_name, metrics in metrics_status.items():
        display_name = dimension_names.get(dimension_name, dimension_name)
        print(f"\n📊 {display_name} ({dimension_name}):")
        for metric_name, enabled in metrics.items():
            status = "✅ Enabled" if enabled else "❌ Disabled"
            print(f"  • {metric_name}: {status}")


def display_dimension_results(results):
    """Display results for dimension-specific evaluation"""
    summary = results["summary"]
    dimension = results["dimension"]

    print(f"\n📊 {dimension.title()} Dimension Evaluation Results:")
    print("=" * 50)
    print(f"Total files processed: {results['total_processed']}")
    print(f"Total files failed: {results['total_failed']}")
    print(f"Average score: {summary.get('average_score', 0):.2f}")

    # Display score distribution
    distribution = summary.get("score_distribution", {})
    print("\nScore Distribution:")
    print(f"  Excellent (90-100): {distribution.get('excellent', 0)}")
    print(f"  Good (80-89): {distribution.get('good', 0)}")
    print(f"  Fair (60-79): {distribution.get('fair', 0)}")
    print(f"  Poor (<60): {distribution.get('poor', 0)}")

    # Display top metric issues
    top_issues = summary.get("top_metric_issues", [])
    if top_issues:
        print("\nTop Metric Issues:")
        for issue in top_issues[:5]:
            print(f"  • {issue['metric']}: {issue['frequency']} occurrences")


def display_comprehensive_results(results):
    """Display results for comprehensive evaluation"""
    summary = results["summary"]

    print("\n📊 Comprehensive Evaluation Results:")
    print("=" * 50)
    print(f"Total files processed: {results['total_processed']}")
    print(f"Total files failed: {results['total_failed']}")
    print(f"Average score: {summary.get('average_score', 0):.2f}")

    # Display score distribution
    distribution = summary.get("score_distribution", {})
    print("\nScore Distribution:")
    print(f"  Excellent (90-100): {distribution.get('excellent', 0)}")
    print(f"  Good (80-89): {distribution.get('good', 0)}")
    print(f"  Fair (60-79): {distribution.get('fair', 0)}")
    print(f"  Poor (<60): {distribution.get('poor', 0)}")

    # Display dimension breakdown if available
    dimension_scores = summary.get("dimension_scores", {})
    if dimension_scores:
        print("\nDimension Breakdown:")
        dimension_names = {
            "completeness": "完整性",
            "correctness": "正确性",
            "difficulty": "难度分级"
        }
        for dim_name, score in dimension_scores.items():
            display_name = dimension_names.get(dim_name, dim_name)
            print(f"  • {display_name}: {score:.2f}")

    # Display top issues
    top_issues = summary.get("top_issues", [])
    if top_issues:
        print("\nTop Issues:")
        for issue in top_issues[:5]:
            print(f"  • {issue}")

if __name__ == "__main__":
    exit(main())