{"dimensions": {"completeness": {"enabled": true, "weight": 1.0, "metrics": {"required_fields": {"enabled": true, "weight": 1.0, "required_fields": {"rdc_info": ["rdc_id", "repo_name", "gerrit_link", "date"], "test_info": ["test_title", "tc_steps", "tc_expected_results"], "tag_identification": ["business_content_scene_tags", "code_modify_scene_tags"]}}, "content_structure": {"enabled": true, "weight": 1.0}, "test_steps_completeness": {"enabled": true, "weight": 1.0, "min_step_length": 10, "min_steps_count": 2}, "code_snippets_completeness": {"enabled": true, "weight": 0.8, "min_snippets": 1, "min_snippet_length": 50}}}, "correctness": {"enabled": true, "weight": 1.0, "metrics": {"format_correctness": {"enabled": true, "weight": 1.0, "rdc_id_pattern": "^RAN-\\d+$", "gerrit_link_pattern": "^https://gerrit\\.zte\\.com\\.cn/#/c/\\d+(/\\d+)?$", "date_pattern": "^\\d{4}-\\d{2}-\\d{2}$"}, "logical_consistency": {"enabled": true, "weight": 1.0}, "data_validity": {"enabled": true, "weight": 1.0}}}, "difficulty": {"enabled": true, "weight": 0.8, "metrics": {"technical_complexity": {"enabled": true, "weight": 1.0, "technical_keywords": {"high": ["并发", "多线程", "分布式", "集群", "负载均衡", "缓存", "数据库事务", "异步", "微服务"], "medium": ["接口", "API", "配置", "网络", "协议", "算法", "数据结构", "性能"], "low": ["基本", "简单", "单一", "直接", "基础"]}}, "business_complexity": {"enabled": true, "weight": 1.0, "business_domains": {"telecom": ["RAN", "5G", "LTE", "基站", "小区", "切换", "测量", "信令"], "network": ["网络", "协议", "路由", "交换", "传输", "接口"], "system": ["系统", "平台", "架构", "模块", "组件", "服务"]}}, "test_coverage_complexity": {"enabled": true, "weight": 0.8}}}}, "batch_processing": {"max_workers": 4, "file_pattern": "*.md"}, "output": {"formats": ["json", "html", "csv"], "include_details": true, "include_dimension_breakdown": true}}