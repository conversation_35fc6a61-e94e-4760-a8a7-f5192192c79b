import re
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass, field

try:
    import mistune
    from mistune import HTMLRender<PERSON>
    from mistune.core import BlockState, InlineState
except ImportError:
    raise ImportError("mistune is required. Please install it with: pip install mistune>=3.0.0")

from ..models.corpus_model import (
    FTCorpus, RdcInfo, TestInfo, TagIdentification, CodeSnippet,
    DependencyCode, SimilarTestCase, SimilarCodeInfo, GenerationRequirements,
    ImplementationSteps, OutputFormat, OutputRequirements, TargetTestCaseDescription
)


@dataclass
class MarkdownSection:
    """表示markdown文档中的一个章节"""
    title: str
    level: int
    content: List[str] = field(default_factory=list)
    subsections: Dict[str, 'MarkdownSection'] = field(default_factory=dict)
    code_blocks: List[Dict[str, str]] = field(default_factory=list)
    lists: List[List[str]] = field(default_factory=list)


class CorpusMarkdownRenderer(HTMLRenderer):
    """自定义的markdown渲染器，用于提取语料库结构化信息"""

    def __init__(self):
        super().__init__()
        self.sections: Dict[str, MarkdownSection] = {}
        self.current_section: Optional[MarkdownSection] = None
        self.section_stack: List[MarkdownSection] = []
        self.rdc_info_content: List[str] = []
        self.in_rdc_info = False

    def heading(self, text: str, level: int) -> str:
        """处理标题"""
        text = text.strip()

        # 创建新的section
        section = MarkdownSection(title=text, level=level)

        # 根据层级管理section层次结构
        if level == 1:  # # 级别 - 顶级section
            self.sections[text] = section
            self.current_section = section
            self.section_stack = [section]
        elif level == 2:  # ## 级别
            if self.current_section and self.current_section.level == 1:
                # 如果当前有一级标题，二级标题作为其子section
                self.current_section.subsections[text] = section
                self.section_stack = [self.current_section, section]
            else:
                # 否则二级标题作为顶级section
                self.sections[text] = section
                self.current_section = section
                self.section_stack = [section]
        elif level == 3 and self.current_section:  # ### 级别
            # 三级标题应该作为二级标题的直接子section，而不是嵌套在其他三级标题下
            if len(self.section_stack) >= 1:
                # 找到最近的二级标题作为父section
                parent_section = None
                for s in reversed(self.section_stack):
                    if s.level == 2:
                        parent_section = s
                        break

                if parent_section:
                    # 添加到二级标题的subsections中
                    parent_section.subsections[text] = section
                    # 更新section_stack：保留到二级标题，然后添加当前三级标题
                    stack_index = next(i for i, s in enumerate(self.section_stack) if s.level == 2)
                    self.section_stack = self.section_stack[:stack_index + 1] + [section]
                else:
                    # 如果没有找到二级标题，添加到current_section
                    self.current_section.subsections[text] = section
                    self.section_stack.append(section)
            else:
                self.current_section.subsections[text] = section
                self.section_stack.append(section)

        self.in_rdc_info = False
        return super().heading(text, level)

    def strong(self, text: str) -> str:
        """处理粗体文本，特别处理RdcInfo"""
        if text.strip() == "RdcInfo":
            self.in_rdc_info = True
        return super().strong(text)
    
    def paragraph(self, text: str) -> str:
        """处理段落"""
        text = text.strip()
        if not text:
            return super().paragraph(text)

        # 检查是否包含RdcInfo标记
        if "**RdcInfo**" in text or text == "RdcInfo":
            self.in_rdc_info = True
            return super().paragraph(text)

        if self.in_rdc_info:
            self.rdc_info_content.append(text)
        elif self.section_stack:
            # 添加到当前最深层的section
            current = self.section_stack[-1]
            current.content.append(text)

        return super().paragraph(text)
    
    def list_item(self, text: str) -> str:
        """处理列表项"""
        text = text.strip()
        if self.in_rdc_info:
            self.rdc_info_content.append(text)
        elif self.section_stack:
            current = self.section_stack[-1]
            if not current.lists:
                current.lists.append([])
            current.lists[-1].append(text)
        
        return super().list_item(text)
    
    def list(self, text: str, ordered: bool, **attrs) -> str:
        """处理列表"""
        if self.section_stack and not self.in_rdc_info:
            current = self.section_stack[-1]
            if not current.lists:
                current.lists.append([])
        return super().list(text, ordered, **attrs)
    
    def block_code(self, code: str, info: Optional[str] = None) -> str:
        """处理代码块"""
        if self.section_stack:
            current = self.section_stack[-1]
            current.code_blocks.append({
                'language': info or 'unknown',
                'content': code.strip()
            })
        
        return super().block_code(code, info)


class MarkdownCorpusParser:
    """基于mistune的markdown语料解析器"""
    
    def __init__(self):
        self.renderer = CorpusMarkdownRenderer()
        self.markdown = mistune.create_markdown(renderer=self.renderer)
    
    def parse_file(self, file_path: Path) -> FTCorpus:
        """解析markdown文件"""
        content = file_path.read_text(encoding='utf-8')

        # 保存原始内容供后续使用
        self._raw_content = content

        # 重置渲染器状态
        self.renderer = CorpusMarkdownRenderer()
        self.markdown = mistune.create_markdown(renderer=self.renderer)

        # 解析markdown
        self.markdown(content)
        
        # 提取各个部分的信息
        rdc_info = self._parse_rdc_info()
        test_info = self._parse_test_info()
        tag_identification = self._parse_tag_identification()
        code_snippets = self._parse_code_snippets()
        
        # 解析新字段
        dependency_code = self._parse_dependency_code()
        similar_code_info = self._parse_similar_code_info()
        generation_requirements = self._parse_generation_requirements()
        implementation_steps = self._parse_implementation_steps()
        output_format = self._parse_output_format()
        output_requirements = self._parse_output_requirements()
        target_test_case_description = self._parse_target_test_case_description()
        
        return FTCorpus(
            file_path=str(file_path),
            rdc_info=rdc_info,
            test_info=test_info,
            tag_identification=tag_identification,
            code_snippets=code_snippets,
            raw_content=content,
            dependency_code=dependency_code,
            similar_code_info=similar_code_info,
            generation_requirements=generation_requirements,
            implementation_steps=implementation_steps,
            output_format=output_format,
            output_requirements=output_requirements,
            target_test_case_description=target_test_case_description
        )
    
    def _parse_rdc_info(self) -> RdcInfo:
        """解析RdcInfo信息"""
        rdc_content = self.renderer.rdc_info_content
        
        # 提取字段
        rdc_id = self._extract_field_from_list(rdc_content, r'rdc_id:(.+)')
        repo_name = self._extract_field_from_list(rdc_content, r'repo_name:(.+)')
        gerrit_link = self._extract_field_from_list(rdc_content, r'gerrit_link:(.+)')
        date = self._extract_field_from_list(rdc_content, r'date:(.+)')
        final_test = self._extract_field_from_list(rdc_content, r'final_test:(.+)')
        compile_script = self._extract_field_from_list(rdc_content, r'compile_script:(.+)')
        compile_command_json_path = self._extract_field_from_list(rdc_content, r'compile_command_json_path:(.+)')
        
        return RdcInfo(
            rdc_id=rdc_id,
            repo_name=repo_name,
            gerrit_link=gerrit_link,
            date=date,
            final_test=final_test,
            compile_script=compile_script,
            compile_command_json_path=compile_command_json_path
        )
    
    def _parse_test_info(self) -> TestInfo:
        """解析TestInfo信息"""
        test_section = self.renderer.sections.get("TestInfo")
        if not test_section:
            return TestInfo("")

        # 提取测试标题 - 只提取测试标题自己的内容
        test_title = ""
        for key in ["测试标题", "测试标题:"]:
            if key in test_section.subsections:
                title_section = test_section.subsections[key]
                test_title = " ".join(title_section.content) if title_section.content else ""
                break

        # 提取预置条件 - 支持多种格式
        preconditions = ""
        for key in ["预制条件", "预置条件", "预制条件:", "预置条件:"]:
            if key in test_section.subsections:
                precond_section = test_section.subsections[key]
                preconditions = " ".join(precond_section.content) if precond_section.content else ""
                break

        # 提取TC步骤
        tc_steps = ""
        for key in ["TC步骤", "TC步骤:"]:
            if key in test_section.subsections:
                steps_section = test_section.subsections[key]
                tc_steps = " ".join(steps_section.content) if steps_section.content else ""
                break

        # 提取TC预期结果
        tc_expected_results = ""
        for key in ["TC预期结果", "TC预期结果:"]:
            if key in test_section.subsections:
                results_section = test_section.subsections[key]
                tc_expected_results = " ".join(results_section.content) if results_section.content else ""
                break

        # 提取预期结果
        expected_results = ""
        for key in ["预期结果", "预期结果:"]:
            if key in test_section.subsections:
                expected_section = test_section.subsections[key]
                expected_results = " ".join(expected_section.content) if expected_section.content else ""
                break

        # 提取通过准则
        pass_criteria = ""
        for key in ["验收准则", "通过准则", "验收准则:", "通过准则:"]:
            if key in test_section.subsections:
                criteria_section = test_section.subsections[key]
                pass_criteria = " ".join(criteria_section.content) if criteria_section.content else ""
                break

        return TestInfo(
            test_title=test_title,
            preconditions=preconditions,
            tc_steps=tc_steps,
            tc_expected_results=tc_expected_results,
            expected_results=expected_results,
            pass_criteria=pass_criteria
        )
    
    def _parse_tag_identification(self) -> TagIdentification:
        """解析Tag Identification信息"""
        tag_section = self.renderer.sections.get("Tag Identification")
        if not tag_section:
            return TagIdentification()
        
        business_tags = []
        code_tags = []
        
        if "business_content_scence_tag" in tag_section.subsections:
            business_section = tag_section.subsections["business_content_scence_tag"]
            business_tags = self._extract_list_content(business_section)
        
        if "code_modify_scence_tag" in tag_section.subsections:
            code_section = tag_section.subsections["code_modify_scence_tag"]
            code_tags = self._extract_list_content(code_section)
        
        return TagIdentification(
            business_content_scene_tags=business_tags,
            code_modify_scene_tags=code_tags
        )
    
    def _parse_code_snippets(self) -> List[CodeSnippet]:
        """解析代码片段"""
        snippets = []
        
        # 遍历所有section收集代码块
        for section_name, section in self.renderer.sections.items():
            snippets.extend(self._extract_code_snippets_from_section(section, section_name))
        
        return snippets
    
    def _extract_field_from_list(self, content_list: List[str], pattern: str) -> str:
        """从内容列表中提取字段"""
        for line in content_list:
            match = re.search(pattern, line, re.MULTILINE)
            if match:
                return match.group(1).strip()
        return ""
    
    def _extract_list_content(self, section: MarkdownSection) -> List[str]:
        """从section中提取列表内容"""
        result = []

        # 从lists中提取
        for lst in section.lists:
            result.extend([item.strip() for item in lst if item.strip()])

        # 从content中提取（处理非列表格式的内容）
        for content in section.content:
            # 过滤掉包含HTML标签或特殊标记的内容（如**QUESTION**）
            if '<strong>' in content or '**QUESTION**' in content:
                continue

            lines = content.split('\n')
            for line in lines:
                line = line.strip()
                if line and not line.startswith('#'):
                    # 移除列表标记（支持数字列表和符号列表）
                    line = re.sub(r'^[-*•]\s*', '', line)
                    line = re.sub(r'^\d+\.\s*', '', line)
                    if line:
                        result.append(line)

        return result

    def _extract_all_content_from_section(self, section: MarkdownSection) -> List[str]:
        """递归提取section及其subsections的所有内容"""
        content_parts = []

        # 提取subsections的内容
        for sub_section in section.subsections.values():
            content_parts.extend(sub_section.content)
            for lst in sub_section.lists:
                content_parts.extend(lst)
            # 递归处理子section
            content_parts.extend(self._extract_all_content_from_section(sub_section))

        return content_parts

    def _extract_section_from_raw_content(self, section_header: str) -> Optional[str]:
        """从原始内容中提取指定section的内容，保持原始格式"""
        # 获取原始内容（需要在parse_file中保存）
        if not hasattr(self, '_raw_content'):
            return None

        import re
        # 匹配section标题到下一个同级或更高级标题
        pattern = rf'{re.escape(section_header)}\n(.*?)(?=\n# |$)'
        match = re.search(pattern, self._raw_content, re.DOTALL)

        if match:
            return match.group(1).strip()
        return None
    
    def _extract_code_snippets_from_section(self, section: MarkdownSection, section_name: str) -> List[CodeSnippet]:
        """从section中提取代码片段"""
        snippets = []
        
        for code_block in section.code_blocks:
            # 尝试从内容中提取文件路径
            file_path = self._extract_file_path_from_content(section.content)
            
            snippets.append(CodeSnippet(
                file_path=file_path,
                language=code_block['language'],
                content=code_block['content'],
                description=f"来自{section_name}部分的代码"
            ))
        
        # 递归处理子section
        for subsection in section.subsections.values():
            snippets.extend(self._extract_code_snippets_from_section(subsection, section_name))
        
        return snippets
    
    def _extract_file_path_from_content(self, content_list: List[str]) -> str:
        """从内容中提取文件路径"""
        for content in content_list:
            match = re.search(r'代码路径[：:]?\s*(.+)', content)
            if match:
                return match.group(1).strip()
        return ""

    def _parse_dependency_code(self) -> List[DependencyCode]:
        """解析目标用例生成可能依赖代码"""
        dependency_codes = []

        # 查找相关section
        for section_name, section in self.renderer.sections.items():
            if "目标用例生成可能依赖代码" in section_name or "依赖代码" in section_name:
                file_path = self._extract_file_path_from_content(section.content)

                for code_block in section.code_blocks:
                    dependency_codes.append(DependencyCode(
                        file_path=file_path,
                        language=code_block['language'],
                        content=code_block['content'],
                        description="目标用例生成可能依赖代码"
                    ))

        return dependency_codes

    def _parse_similar_code_info(self) -> Optional[SimilarCodeInfo]:
        """解析相似代码信息"""
        similar_section = None

        # 查找相似代码信息section
        for section_name, section in self.renderer.sections.items():
            if "相似代码信息" in section_name:
                similar_section = section
                break

        if not similar_section:
            return None

        # 解析相似测试用例
        similar_test_case = None
        if "相似文本测试用例描述" in similar_section.subsections:
            test_case_section = similar_section.subsections["相似文本测试用例描述"]
            similar_test_case = self._parse_similar_test_case(test_case_section)

        # 解析相似FT代码
        similar_ft_code = []
        if "相似用例FT代码" in similar_section.subsections:
            ft_code_section = similar_section.subsections["相似用例FT代码"]
            similar_ft_code = self._extract_code_snippets_from_section(ft_code_section, "相似用例FT代码")

        # 解析相似用例代码依赖
        similar_dependency_code = []
        if "相似用例代码依赖" in similar_section.subsections:
            dep_code_section = similar_section.subsections["相似用例代码依赖"]
            similar_dependency_code = self._extract_code_snippets_from_section(dep_code_section, "相似用例代码依赖")

        return SimilarCodeInfo(
            similar_test_case=similar_test_case,
            similar_ft_code=similar_ft_code,
            similar_dependency_code=similar_dependency_code
        )

    def _parse_similar_test_case(self, section: MarkdownSection) -> Optional[SimilarTestCase]:
        """解析相似测试用例"""
        if not section:
            return None

        # 提取测试标题
        test_title = ""
        if "测试标题" in section.subsections:
            title_section = section.subsections["测试标题"]
            test_title = " ".join(title_section.content) if title_section.content else ""
        elif section.content:
            # 如果没有子section，从第一行内容提取标题
            test_title = section.content[0] if section.content else ""

        # 提取其他字段
        preconditions = []
        tc_steps = []
        tc_expected_results = []
        expected_results = ""
        pass_criteria = ""

        for key in ["预制条件", "预置条件"]:
            if key in section.subsections:
                precond_section = section.subsections[key]
                preconditions = " ".join(precond_section.content) if precond_section.content else ""
                break

        if "TC步骤" in section.subsections:
            steps_section = section.subsections["TC步骤"]
            tc_steps = " ".join(steps_section.content) if steps_section.content else ""

        if "TC预期结果" in section.subsections:
            results_section = section.subsections["TC预期结果"]
            tc_expected_results = " ".join(results_section.content) if results_section.content else ""

        if "预期结果" in section.subsections:
            expected_section = section.subsections["预期结果"]
            expected_results = " ".join(expected_section.content) if expected_section.content else ""

        for key in ["验收准则", "通过准则"]:
            if key in section.subsections:
                criteria_section = section.subsections[key]
                pass_criteria = " ".join(criteria_section.content) if criteria_section.content else ""
                break

        return SimilarTestCase(
            test_title=test_title,
            preconditions=preconditions,
            tc_steps=tc_steps,
            tc_expected_results=tc_expected_results,
            expected_results=expected_results,
            pass_criteria=pass_criteria
        )

    def _parse_generation_requirements(self) -> Optional[GenerationRequirements]:
        """解析代码生成要求"""
        # 直接从原始内容中提取，保持原始格式
        content = self._extract_section_from_raw_content("# 代码生成要求")
        if content:
            return GenerationRequirements(content=content)
        return None

    def _parse_implementation_steps(self) -> Optional[ImplementationSteps]:
        """解析建议的实现步骤"""
        # 直接从原始内容中提取，保持原始格式
        content = self._extract_section_from_raw_content("# 建议的实现步骤")
        if content:
            return ImplementationSteps(content=content)
        return None

    def _parse_output_format(self) -> Optional[OutputFormat]:
        """解析输出格式"""
        # 直接从原始内容中提取，保持原始格式
        content = self._extract_section_from_raw_content("# 输出格式")
        if content:
            return OutputFormat(content=content)
        return None

    def _parse_output_requirements(self) -> Optional[OutputRequirements]:
        """解析输出要求"""
        # 直接从原始内容中提取，保持原始格式
        content = self._extract_section_from_raw_content("# 输出要求")
        if content:
            return OutputRequirements(content=content)
        return None

    def _parse_target_test_case_description(self) -> Optional[TargetTestCaseDescription]:
        """解析目标测试用例描述"""
        for section_name, section in self.renderer.sections.items():
            if "目标测试用例描述" in section_name:
                content = "\n".join(section.content)
                return TargetTestCaseDescription(content=content)
        return None
