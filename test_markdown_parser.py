#!/usr/bin/env python3
"""
测试新的markdown解析器
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from ft_corpus_evaluator.parsers.corpus_parser import CorpusParser
from ft_corpus_evaluator.parsers.markdown_corpus_parser import MarkdownCorpusParser


def test_parser_comparison():
    """比较原解析器和新解析器的结果"""
    
    # 选择一个测试文件
    test_file = Path("corpus/RAN-5869391.md")
    if not test_file.exists():
        print(f"测试文件 {test_file} 不存在")
        return
    
    print(f"测试文件: {test_file}")
    print("=" * 60)
    
    # 使用原解析器
    print("\n1. 原解析器结果:")
    print("-" * 30)
    try:
        old_parser = CorpusParser()
        old_result = old_parser.parse_file(test_file)
        print_corpus_info(old_result, "原解析器")
    except Exception as e:
        print(f"原解析器出错: {e}")
    
    # 使用新解析器
    print("\n2. 新解析器结果:")
    print("-" * 30)
    try:
        new_parser = MarkdownCorpusParser()
        new_result = new_parser.parse_file(test_file)
        print_corpus_info(new_result, "新解析器")
    except Exception as e:
        print(f"新解析器出错: {e}")
        import traceback
        traceback.print_exc()


def print_corpus_info(corpus, parser_name):
    """打印语料库信息"""
    print(f"\n{parser_name} - RdcInfo:")
    print(f"  rdc_id: {corpus.rdc_info.rdc_id}")
    print(f"  repo_name: {corpus.rdc_info.repo_name}")
    print(f"  gerrit_link: {corpus.rdc_info.gerrit_link}")
    print(f"  date: {corpus.rdc_info.date}")
    
    print(f"\n{parser_name} - TestInfo:")
    print(f"  test_title: {corpus.test_info.test_title[:100]}...")
    print(f"  preconditions count: {len(corpus.test_info.preconditions)}")
    print(f"  tc_steps count: {len(corpus.test_info.tc_steps)}")
    print(f"  tc_expected_results count: {len(corpus.test_info.tc_expected_results)}")
    
    print(f"\n{parser_name} - TagIdentification:")
    print(f"  business_tags count: {len(corpus.tag_identification.business_content_scene_tags)}")
    print(f"  code_tags count: {len(corpus.tag_identification.code_modify_scene_tags)}")
    
    print(f"\n{parser_name} - CodeSnippets:")
    print(f"  code_snippets count: {len(corpus.code_snippets)}")
    for i, snippet in enumerate(corpus.code_snippets[:3]):  # 只显示前3个
        print(f"    [{i}] {snippet.language}: {len(snippet.content)} chars")
    
    print(f"\n{parser_name} - DependencyCode:")
    print(f"  dependency_code count: {len(corpus.dependency_code)}")
    
    print(f"\n{parser_name} - SimilarCodeInfo:")
    if corpus.similar_code_info:
        print(f"  similar_test_case: {'存在' if corpus.similar_code_info.similar_test_case else '不存在'}")
        print(f"  similar_ft_code count: {len(corpus.similar_code_info.similar_ft_code)}")
        print(f"  similar_dependency_code count: {len(corpus.similar_code_info.similar_dependency_code)}")
    else:
        print("  similar_code_info: 不存在")


def test_markdown_renderer():
    """测试markdown渲染器的基本功能"""
    print("\n3. 测试markdown渲染器:")
    print("-" * 30)
    
    from ft_corpus_evaluator.parsers.markdown_corpus_parser import CorpusMarkdownRenderer
    import mistune
    
    # 创建测试markdown内容
    test_markdown = """
**RdcInfo**
- rdc_id:RAN-123456
- repo_name:test_repo
- gerrit_link:https://gerrit.example.com
- date:2024-01-01

## TestInfo

### 测试标题
这是一个测试标题

### 预置条件
- 条件1
- 条件2

### TC步骤
1. 步骤1
2. 步骤2

### TC预期结果
- 结果1
- 结果2

## Tag Identification

### business_content_scence_tag
- 业务标签1
- 业务标签2

### code_modify_scence_tag
- 代码标签1
- 代码标签2

# 目标用例生成可能依赖代码
代码路径: test/path.cpp
```cpp
int main() {
    return 0;
}
```
"""
    
    renderer = CorpusMarkdownRenderer()
    markdown = mistune.create_markdown(renderer=renderer)
    
    # 解析测试内容
    markdown(test_markdown)
    
    print("RdcInfo内容:")
    for content in renderer.rdc_info_content:
        print(f"  {content}")
    
    print("\nSections:")
    for name, section in renderer.sections.items():
        print(f"  {name}: {len(section.content)} content items, {len(section.subsections)} subsections")
        for sub_name in section.subsections.keys():
            print(f"    - {sub_name}")


if __name__ == "__main__":
    print("开始测试markdown解析器...")
    
    # 首先测试基本的markdown渲染器
    test_markdown_renderer()
    
    # 然后测试完整的解析器比较
    test_parser_comparison()
    
    print("\n测试完成!")
