{
  "summary": {
    "total_files": 4,
    "average_score": 45.01,
    "score_distribution": {
      "excellent": 0,
      "good": 0,
      "fair": 0,
      "poor": 4
    },
    "issue_counts": {
      "critical": 12,
      "warning": 3,
      "info": 1
    },
    "top_issues": [
      {
        "issue": "three_dimension_evaluator_completeness: 完整性维度评分较低，需要重点改进",
        "frequency": 4
      },
      {
        "issue": "three_dimension_evaluator_difficulty: 难度分级维度评分较低，需要重点改进",
        "frequency": 4
      },
      {
        "issue": "three_dimension_evaluator_correctness: 正确性维度评分中等，建议优化",
        "frequency": 3
      },
      {
        "issue": "three_dimension_evaluator_comprehensive: 综合评分: 48.2 - 质量较低，需要全面改进",
        "frequency": 1
      },
      {
        "issue": "three_dimension_evaluator_comprehensive: 综合评分: 49.3 - 质量较低，需要全面改进",
        "frequency": 1
      }
    ]
  },
  "reports": [
    {
      "corpus_id": "RAN-6808752",
      "file_path": "corpus/测试用例 RAN-6808752_ UE为免切用户时,lrrm收到DrbAdmitRequest消息发起承载添加流程部分接纳失败校验.md",
      "overall_score": 46.53628117913833,
      "evaluation_results": [
        {
          "evaluator_name": "three_dimension_evaluator_completeness",
          "level": "critical",
          "score": 46.959064327485386,
          "message": "完整性维度评分较低，需要重点改进",
          "details": {
            "dimension": "completeness",
            "dimension_name": "完整性",
            "overall_score": 46.959064327485386,
            "weighted_score": 178.44444444444446,
            "weight": 1.0,
            "metric_results": [
              {
                "metric_name": "required_fields",
                "dimension": "completeness",
                "score": 44.44444444444444,
                "weight": 1.0,
                "level": "critical",
                "message": "Critical: 5 required fields missing",
                "details": {
                  "missing_fields": [
                    "test_info.test_title",
                    "test_info.tc_steps",
                    "test_info.tc_expected_results",
                    "tag_identification.business_content_scene_tags",
                    "tag_identification.code_modify_scene_tags"
                  ],
                  "present_fields": 4,
                  "total_fields": 9,
                  "completion_rate": "4/9"
                },
                "suggestions": [
                  "Add missing field: test_info.test_title",
                  "Add missing field: test_info.tc_steps",
                  "Add missing field: test_info.tc_expected_results",
                  "Add missing field: tag_identification.business_content_scene_tags",
                  "Add missing field: tag_identification.code_modify_scene_tags"
                ]
              },
              {
                "metric_name": "content_structure",
                "dimension": "completeness",
                "score": 0,
                "weight": 1.0,
                "level": "critical",
                "message": "Critical structure issues found: 5",
                "details": {
                  "structure_issues": [
                    "Test title is too short or missing",
                    "Insufficient test steps (minimum 2 required)",
                    "Expected results are missing",
                    "Business content scene tags are missing",
                    "Code modify scene tags are missing"
                  ],
                  "issues_count": 5
                },
                "suggestions": [
                  "Fix: Test title is too short or missing",
                  "Fix: Insufficient test steps (minimum 2 required)",
                  "Fix: Expected results are missing",
                  "Fix: Business content scene tags are missing",
                  "Fix: Code modify scene tags are missing"
                ]
              },
              {
                "metric_name": "test_steps_completeness",
                "dimension": "completeness",
                "score": 70,
                "weight": 1.0,
                "level": "warning",
                "message": "Test steps need improvement",
                "details": {
                  "steps_count": 0,
                  "expected_results_count": 0,
                  "short_steps": [],
                  "empty_steps": [],
                  "issues": [
                    "Too few test steps: 0 (minimum: 2)"
                  ]
                },
                "suggestions": [
                  "Add more test steps (current: 0, minimum: 2)"
                ]
              },
              {
                "metric_name": "code_snippets_completeness",
                "dimension": "completeness",
                "score": 80,
                "weight": 0.8,
                "level": "info",
                "message": "Code snippets are complete",
                "details": {
                  "snippets_count": 15,
                  "incomplete_snippets": [
                    {
                      "index": 13,
                      "issues": [
                        "missing or unknown language"
                      ]
                    },
                    {
                      "index": 14,
                      "issues": [
                        "missing or unknown language"
                      ]
                    }
                  ],
                  "issues": [
                    "Incomplete code snippets: 2"
                  ]
                },
                "suggestions": [
                  "Fix snippet 13: missing or unknown language",
                  "Fix snippet 14: missing or unknown language"
                ]
              }
            ],
            "enabled_metrics": [
              "required_fields",
              "content_structure",
              "test_steps_completeness",
              "code_snippets_completeness"
            ],
            "metric_count": 4
          },
          "suggestions": [
            "Add missing field: test_info.test_title",
            "Add missing field: test_info.tc_steps",
            "Fix: Test title is too short or missing",
            "Fix: Insufficient test steps (minimum 2 required)",
            "Add more test steps (current: 0, minimum: 2)"
          ],
          "dimension": "completeness"
        },
        {
          "evaluator_name": "three_dimension_evaluator_correctness",
          "level": "warning",
          "score": 76.66666666666667,
          "message": "正确性维度评分中等，建议优化",
          "details": {
            "dimension": "correctness",
            "dimension_name": "正确性",
            "overall_score": 76.66666666666667,
            "weighted_score": 230.0,
            "weight": 1.0,
            "metric_results": [
              {
                "metric_name": "format_correctness",
                "dimension": "correctness",
                "score": 80,
                "weight": 1.0,
                "level": "info",
                "message": "Format validation passed",
                "details": {
                  "format_issues": [
                    "Invalid date format (expected: YYYY-MM-DD)"
                  ],
                  "invalid_tags": [],
                  "issues_count": 1
                },
                "suggestions": [
                  "Fix format issue: Invalid date format (expected: YYYY-MM-DD)"
                ]
              },
              {
                "metric_name": "logical_consistency",
                "dimension": "correctness",
                "score": 80,
                "weight": 1.0,
                "level": "info",
                "message": "Content is logically consistent",
                "details": {
                  "consistency_issues": [
                    "Test title doesn't match test content"
                  ],
                  "issues_count": 1,
                  "steps_results_ratio": "0:0"
                },
                "suggestions": [
                  "Fix: Test title doesn't match test content"
                ]
              },
              {
                "metric_name": "data_validity",
                "dimension": "correctness",
                "score": 70,
                "weight": 1.0,
                "level": "warning",
                "message": "Data validity issues detected",
                "details": {
                  "validity_issues": [
                    "Date format is invalid",
                    "Code snippet 14 has unknown language",
                    "Code snippet 15 has unknown language"
                  ],
                  "duplicate_steps": [],
                  "issues_count": 3
                },
                "suggestions": [
                  "Fix: Date format is invalid",
                  "Fix: Code snippet 14 has unknown language",
                  "Fix: Code snippet 15 has unknown language"
                ]
              }
            ],
            "enabled_metrics": [
              "format_correctness",
              "logical_consistency",
              "data_validity"
            ],
            "metric_count": 3
          },
          "suggestions": [
            "Fix: Date format is invalid",
            "Fix: Code snippet 14 has unknown language"
          ],
          "dimension": "correctness"
        },
        {
          "evaluator_name": "three_dimension_evaluator_difficulty",
          "level": "critical",
          "score": 14.285714285714286,
          "message": "难度分级维度评分较低，需要重点改进",
          "details": {
            "dimension": "difficulty",
            "dimension_name": "难度分级",
            "overall_score": 14.285714285714286,
            "weighted_score": 40.0,
            "weight": 0.8,
            "metric_results": [
              {
                "metric_name": "technical_complexity",
                "dimension": "difficulty",
                "score": 30,
                "weight": 1.0,
                "level": "info",
                "message": "Low technical complexity",
                "details": {
                  "difficulty_level": "Low",
                  "complexity_factors": [
                    "Complex code patterns detected"
                  ],
                  "keyword_counts": {
                    "high": 0,
                    "medium": 0,
                    "low": 0
                  },
                  "code_complexity": 30,
                  "steps_complexity": 0
                },
                "suggestions": []
              },
              {
                "metric_name": "business_complexity",
                "dimension": "difficulty",
                "score": 10,
                "weight": 1.0,
                "level": "info",
                "message": "Low business complexity",
                "details": {
                  "difficulty_level": "Low",
                  "business_factors": [],
                  "domain_matches": {},
                  "scenario_complexity": 0,
                  "integration_complexity": 10
                },
                "suggestions": []
              },
              {
                "metric_name": "test_coverage_complexity",
                "dimension": "difficulty",
                "score": 0,
                "weight": 0.8,
                "level": "warning",
                "message": "Limited test coverage",
                "details": {
                  "difficulty_level": "Low",
                  "coverage_factors": [],
                  "test_types": [],
                  "test_scenarios": 0,
                  "edge_cases": [],
                  "data_variations": 0
                },
                "suggestions": []
              }
            ],
            "enabled_metrics": [
              "technical_complexity",
              "business_complexity",
              "test_coverage_complexity"
            ],
            "metric_count": 3
          },
          "suggestions": [],
          "dimension": "difficulty"
        },
        {
          "evaluator_name": "three_dimension_evaluator_comprehensive",
          "level": "critical",
          "score": 48.23367943668696,
          "message": "综合评分: 48.2 - 质量较低，需要全面改进",
          "details": {
            "overall_score": 48.23367943668696,
            "dimension_scores": {
              "completeness": {
                "score": 46.959064327485386,
                "weighted_score": 46.959064327485386,
                "weight": 1.0,
                "level": 