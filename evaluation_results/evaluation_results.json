{"summary": {"total_files": 4, "average_score": 41.75, "score_distribution": {"excellent": 0, "good": 0, "fair": 0, "poor": 4}, "issue_counts": {"critical": 12, "warning": 4, "info": 4}, "top_issues": [{"issue": "completeness_evaluator: Missing 5 required fields", "frequency": 4}, {"issue": "content_quality_evaluator: Content quality is poor", "frequency": 4}, {"issue": "test_case_validity_evaluator: Test case has significant validity problems", "frequency": 4}, {"issue": "format_validation_evaluator: Found 2 format issues", "frequency": 2}, {"issue": "format_validation_evaluator: Found 1 format issues", "frequency": 2}]}, "reports": [{"corpus_id": "RAN-5869391", "file_path": "corpus/RAN-5869391.md", "overall_score": 39.88662131519274, "evaluation_results": [{"evaluator_name": "completeness_evaluator", "level": "critical", "score": 44.44444444444444, "message": "Missing 5 required fields", "details": {"missing_fields": ["test_info.test_title", "test_info.tc_steps", "test_info.tc_expected_results", "tag_identification.business_content_scene_tags", "tag_identification.code_modify_scene_tags"], "completeness_percentage": 44.44444444444444}, "suggestions": ["Please fill in the missing field: test_info.test_title", "Please fill in the missing field: test_info.tc_steps", "Please fill in the missing field: test_info.tc_expected_results", "Please fill in the missing field: tag_identification.business_content_scene_tags", "Please fill in the missing field: tag_identification.code_modify_scene_tags"]}, {"evaluator_name": "content_quality_evaluator", "level": "critical", "score": 0.0, "message": "Content quality is poor", "details": {"title_score": 0, "steps_score": 0, "expected_results_score": 0, "overall_score": 0.0}, "suggestions": ["Improve test title clarity and add RAN ID reference", "Add more detailed test steps with specific actions", "Provide more specific expected results"]}, {"evaluator_name": "format_validation_evaluator", "level": "warning", "score": 50, "message": "Found 2 format issues", "details": {"format_issues": ["Invalid Gerrit link format", "Code snippets have formatting issues"]}, "suggestions": ["Fix format issue: Invalid Gerrit link format", "Fix format issue: Code snippets have formatting issues"]}, {"evaluator_name": "test_case_validity_evaluator", "level": "critical", "score": 25, "message": "Test case has significant validity problems", "details": {"validity_score": 25, "issues": ["Test objective is not clear", "Test steps are not executable", "Expected results are not verifiable"]}, "suggestions": ["Address issue: Test objective is not clear", "Address issue: Test steps are not executable", "Address issue: Expected results are not verifiable"]}, {"evaluator_name": "code_quality_evaluator", "level": "info", "score": 90.0, "message": "Code quality is good", "details": {"average_score": 90.0, "snippet_scores": [{"snippet_index": 0, "file_path": "ft/uc/ftest/nsa/include/ft-sn-pscell-change/domain/kpi/TTrigInraSnPscellChangeStat.h", "language": "cpp", "score": 90}, {"snippet_index": 1, "file_path": "ft/uc/ftest/nsa/include/ft-sn-pscell-change/domain/kpi/TTrigInraSnPscellChangeStat.h", "language": "cpp", "score": 90}, {"snippet_index": 2, "file_path": "ft/uc/ftest/nsa/include/ft-sn-pscell-change/domain/kpi/TTrigInraSnPscellChangeStat.h", "language": "cpp", "score": 90}, {"snippet_index": 3, "file_path": "ft/uc/ftest/nsa/include/ft-sn-pscell-change/domain/kpi/TTrigInraSnPscellChangeStat.h", "language": "cpp", "score": 90}, {"snippet_index": 4, "file_path": "ft/uc/ftest/nsa/include/ft-sn-pscell-change/domain/kpi/TTrigInraSnPscellChangeStat.h", "language": "cpp", "score": 90}, {"snippet_index": 5, "file_path": "ft/uc/ftest/nsa/include/ft-sn-pscell-change/domain/kpi/TTrigInraSnPscellChangeStat.h", "language": "cpp", "score": 100}, {"snippet_index": 6, "file_path": "ft/uc/ftest/nsa/include/ft-sn-pscell-change/domain/kpi/TTrigInraSnPscellChangeStat.h", "language": "cpp", "score": 90}, {"snippet_index": 7, "file_path": "ft/uc/ftest/nsa/include/ft-sn-pscell-change/domain/kpi/TTrigInraSnPscellChangeStat.h", "language": "cpp", "score": 90}, {"snippet_index": 8, "file_path": "ft/uc/ftest/nsa/include/ft-sn-pscell-change/domain/kpi/TTrigInraSnPscellChangeStat.h", "language": "cpp", "score": 90}, {"snippet_index": 9, "file_path": "ft/uc/ftest/nsa/include/ft-sn-pscell-change/domain/kpi/TTrigInraSnPscellChangeStat.h", "language": "cpp", "score": 90}, {"snippet_index": 10, "file_path": "ft/uc/ftest/nsa/include/ft-sn-pscell-change/domain/kpi/TTrigInraSnPscellChangeStat.h", "language": "cpp", "score": 100}, {"snippet_index": 11, "file_path": "ft/uc/ftest/nsa/include/ft-sn-pscell-change/domain/kpi/TTrigInraSnPscellChangeStat.h", "language": "cpp", "score": 90}, {"snippet_index": 12, "file_path": "ft/uc/ftest/nsa/include/ft-sn-pscell-change/domain/kpi/TTrigInraSnPscellChangeStat.h", "language": "cpp", "score": 90}, {"snippet_index": 13, "file_path": "ft/uc/ftest/nsa/include/ft-sn-pscell-change/domain/kpi/TTrigInraSnPscellChangeStat.h", "language": "cpp", "score": 90}, {"snippet_index": 14, "file_path": "ft/uc/ftest/nsa/include/ft-sn-pscell-change/domain/kpi/TTrigInraSnPscellChangeStat.h", "language": "cpp", "score": 90}, {"snippet_index": 15, "file_path": "ft/uc/ftest/nsa/include/ft-sn-pscell-change/domain/kpi/TTrigInraSnPscellChangeStat.h", "language": "cpp", "score": 90}, {"snippet_index": 16, "file_path": "ft/uc/ftest/nsa/include/ft-sn-pscell-change/domain/kpi/TTrigInraSnPscellChangeStat.h", "language": "cpp", "score": 90}, {"snippet_index": 17, "file_path": "ft/uc/ftest/nsa/include/ft-sn-pscell-change/domain/kpi/TTrigInraSnPscellChangeStat.h", "language": "cpp", "score": 90}, {"snippet_index": 18, "file_path": "ft/uc/ftest/nsa/include/ft-sn-pscell-change/domain/kpi/TTrigInraSnPscellChangeStat.h", "language": "cpp", "score": 80}, {"snippet_index": 19, "file_path": "ft/uc/ftest/nsa/include/ft-sn-pscell-change/domain/kpi/TTrigInraSnPscellChangeStat.h", "language": "cpp", "score": 90}, {"snippet_index": 20, "file_path": "ft/uc/ftest/nsa/include/ft-sn-pscell-change/domain/kpi/TTrigInraSnPscellChangeStat.h", "language": "cpp", "score": 90}, {"snippet_index": 21, "file_path": "ft/uc/ftest/nsa/include/ft-sn-pscell-change/domain/kpi/TTrigInraSnPscellChangeStat.h", "language": "cpp", "score": 90}, {"snippet_index": 22, "file_path": "ft/uc/ftest/nsa/include/ft-sn-pscell-change/domain/kpi/TTrigInraSnPscellChangeStat.h", "language": "cpp", "score": 90}, {"snippet_index": 23, "file_path": "ft/uc/ftest/nsa/include/ft-sn-pscell-change/domain/kpi/TTrigInraSnPscellChangeStat.h", "language": "cpp", "score": 80}, {"snippet_index": 24, "file_path": "ft/uc/ftest/nsa/include/ft-sn-pscell-change/domain/kpi/TTrigInraSnPscellChangeStat.h", "language": "cpp", "score": 90}, {"snippet_index": 25, "file_path": "ft/uc/ftest/nsa/include/ft-sn-pscell-change/domain/kpi/TTrigInraSnPscellChangeStat.h", "language": "cpp", "score": 90}, {"snippet_index": 26, "file_path": "ft/uc/ftest/nsa/include/ft-sn-pscell-change/domain/kpi/TTrigInraSnPscellChangeStat.h", "language": "cpp", "score": 90}, {"snippet_index": 27, "file_path": "ft/uc/ftest/nsa/include/ft-sn-pscell-change/domain/kpi/TTrigInraSnPscellChangeStat.h", "language": "cpp", "score": 90}, {"snippet_index": 28, "file_path": "ft/uc/ftest/nsa/include/ft-sn-pscell-change/domain/kpi/TTrigInraSnPscellChangeStat.h", "language": "cpp", "score": 100}, {"snippet_index": 29, "file_path": "ft/uc/ftest/nsa/include/ft-sn-pscell-change/domain/kpi/TTrigInraSnPscellChangeStat.h", "language": "cpp", "score": 100}, {"snippet_index": 30, "file_path": "ft/uc/ftest/nsa/include/ft-sn-pscell-change/domain/kpi/TTrigInraSnPscellChangeStat.h", "language": "cpp", "score": 100}, {"snippet_index": 31, "file_path": "ft/uc/ftest/nsa/include/ft-sn-pscell-change/domain/kpi/TTrigInraSnPscellChangeStat.h", "language": "cpp", "score": 90}, {"snippet_index": 32, "file_path": "ft/uc/ftest/nsa/include/ft-sn-pscell-change/domain/kpi/TTrigInraSnPscellChangeStat.h", "language": "unknown", "score": 75}, {"snippet_index": 33, "file_path": "ft/uc/ftest/nsa/include/ft-sn-pscell-change/domain/kpi/TTrigInraSnPscellChangeStat.h", "language": "unknown", "score": 75}], "total_snippets": 34}, "suggestions": []}], "metadata": {"evaluator_count": 5, "total_issues": 4}}, {"corpus_id": "RAN-6808752", "file_path": "corpus/测试用例 RAN-6808752_ UE为免切用户时,lrrm收到DrbAdmitRequest消息发起承载添加流程部分接纳失败校验.md", "overall_score": 44.58049886621315, "evaluation_results": [{"evaluator_name": "completeness_evaluator", "level": "critical", "score": 44.44444444444444, "message": "Missing 5 required fields", "details": {"missing_fields": ["test_info.test_title", "test_info.tc_steps", "test_info.tc_expected_results", "tag_identification.business_content_scene_tags", "tag_identification.code_modify_scene_tags"], "completeness_percentage": 44.44444444444444}, "suggestions": ["Please fill in the missing field: test_info.test_title", "Please fill in the missing field: test_info.tc_steps", "Please fill in the missing field: test_info.tc_expected_results", "Please fill in the missing field: tag_identification.business_content_scene_tags", "Please fill in the missing field: tag_identification.code_modify_scene_tags"]}, {"evaluator_name": "content_quality_evaluator", "level": "critical", "score": 0.0, "message": "Content quality is poor", "details": {"title_score": 0, "steps_score": 0, "expected_results_score": 0, "overall_score": 0.0}, "suggestions": ["Improve test title clarity and add RAN ID reference", "Add more detailed test steps with specific actions", "Provide more specific expected results"]}, {"evaluator_name": "format_validation_evaluator", "level": "warning", "score": 75, "message": "Found 1 format issues", "details": {"format_issues": ["Code snippets have formatting issues"]}, "suggestions": ["Fix format issue: Code snippets have formatting issues"]}, {"evaluator_name": "test_case_validity_evaluator", "level": "critical", "score": 25, "message": "Test case has significant validity problems", "details": {"validity_score": 25, "issues": ["Test objective is not clear", "Test steps are not executable", "Expected results are not verifiable"]}, "suggestions": ["Address issue: Test objective is not clear", "Address issue: Test steps are not executable", "Address issue: Expected results are not verifiable"]}, {"evaluator_name": "code_quality_evaluator", "level": "info", "score": 93.33333333333333, "message": "Code quality is good", "details": {"average_score": 93.33333333333333, "snippet_scores": [{"snippet_index": 0, "file_path": "ft/ft-test/ft-common/include/ft-common/domain/ue-admit/drb-admit/builder/DrbReqBuilder.h", "language": "cpp", "score": 100}, {"snippet_index": 1, "file_path": "ft/ft-test/ft-common/include/ft-common/domain/ue-admit/drb-admit/builder/DrbReqBuilder.h", "language": "cpp", "score": 90}, {"snippet_index": 2, "file_path": "ft/ft-test/ft-common/include/ft-common/domain/ue-admit/drb-admit/builder/DrbReqBuilder.h", "language": "cpp", "score": 100}, {"snippet_index": 3, "file_path": "ft/ft-test/ft-common/include/ft-common/domain/ue-admit/drb-admit/builder/DrbReqBuilder.h", "language": "cpp", "score": 90}, {"snippet_index": 4, "file_path": "ft/ft-test/ft-common/include/ft-common/domain/ue-admit/drb-admit/builder/DrbReqBuilder.h", "language": "cpp", "score": 100}, {"snippet_index": 5, "file_path": "ft/ft-test/ft-common/include/ft-common/domain/ue-admit/drb-admit/builder/DrbReqBuilder.h", "language": "cpp", "score": 90}, {"snippet_index": 6, "file_path": "ft/ft-test/ft-common/include/ft-common/domain/ue-admit/drb-admit/builder/DrbReqBuilder.h", "language": "cpp", "score": 100}, {"snippet_index": 7, "file_path": "ft/ft-test/ft-common/include/ft-common/domain/ue-admit/drb-admit/builder/DrbReqBuilder.h", "language": "cpp", "score": 90}, {"snippet_index": 8, "file_path": "ft/ft-test/ft-common/include/ft-common/domain/ue-admit/drb-admit/builder/DrbReqBuilder.h", "language": "cpp", "score": 100}, {"snippet_index": 9, "file_path": "ft/ft-test/ft-common/include/ft-common/domain/ue-admit/drb-admit/builder/DrbReqBuilder.h", "language": "cpp", "score": 100}, {"snippet_index": 10, "file_path": "ft/ft-test/ft-common/include/ft-common/domain/ue-admit/drb-admit/builder/DrbReqBuilder.h", "language": "cpp", "score": 100}, {"snippet_index": 11, "file_path": "ft/ft-test/ft-common/include/ft-common/domain/ue-admit/drb-admit/builder/DrbReqBuilder.h", "language": "cpp", "score": 100}, {"snippet_index": 12, "file_path": "ft/ft-test/ft-common/include/ft-common/domain/ue-admit/drb-admit/builder/DrbReqBuilder.h", "language": "cpp", "score": 90}, {"snippet_index": 13, "file_path": "ft/ft-test/ft-common/include/ft-common/domain/ue-admit/drb-admit/builder/DrbReqBuilder.h", "language": "unknown", "score": 75}, {"snippet_index": 14, "file_path": "ft/ft-test/ft-common/include/ft-common/domain/ue-admit/drb-admit/builder/DrbReqBuilder.h", "language": "unknown", "score": 75}], "total_snippets": 15}, "suggestions": []}], "metadata": {"evaluator_count": 5, "total_issues": 4}}, {"corpus_id": "RAN-5946243", "file_path": "corpus/RAN-5946243 RRC建立优化场景MSG5的传递路径：LUCM-_LUC（UlDcchTranmissionMessage）传递远近点标识.md", "overall_score": 43.83049886621315, "evaluation_results": [{"evaluator_name": "completeness_evaluator", "level": "critical", "score": 44.44444444444444, "message": "Missing 5 required fields", "details": {"missing_fields": ["test_info.test_title", "test_info.tc_steps", "test_info.tc_expected_results", "tag_identification.business_content_scene_tags", "tag_identification.code_modify_scene_tags"], "completeness_percentage": 44.44444444444444}, "suggestions": ["Please fill in the missing field: test_info.test_title", "Please fill in the missing field: test_info.tc_steps", "Please fill in the missing field: test_info.tc_expected_results", "Please fill in the missing field: tag_identification.business_content_scene_tags", "Please fill in the missing field: tag_identification.code_modify_scene_tags"]}, {"evaluator_name": "content_quality_evaluator", "level": "critical", "score": 0.0, "message": "Content quality is poor", "details": {"title_score": 0, "steps_score": 0, "expected_results_score": 0, "overall_score": 0.0}, "suggestions": ["Improve test title clarity and add RAN ID reference", "Add more detailed test steps with specific actions", "Provide more specific expected results"]}, {"evaluator_name": "format_validation_evaluator", "level": "warning", "score": 75, "message": "Found 1 format issues", "details": {"format_issues": ["Code snippets have formatting issues"]}, "suggestions": ["Fix format issue: Code snippets have formatting issues"]}, {"evaluator_name": "test_case_validity_evaluator", "level": "critical", "score": 25, "message": "Test case has significant validity problems", "details": {"validity_score": 25, "issues": ["Test objective is not clear", "Test steps are not executable", "Expected results are not verifiable"]}, "suggestions": ["Address issue: Test objective is not clear", "Address issue: Test steps are not executable", "Address issue: Expected results are not verifiable"]}, {"evaluator_name": "code_quality_evaluator", "level": "info", "score": 89.25, "message": "Code quality is good", "details": {"average_score": 89.25, "snippet_scores": [{"snippet_index": 0, "file_path": "ft/uc/ftest/ftattach/src/action/FtAttachAction.cpp", "language": "cpp", "score": 90}, {"snippet_index": 1, "file_path": "ft/uc/ftest/ftattach/src/action/FtAttachAction.cpp", "language": "cpp", "score": 100}, {"snippet_index": 2, "file_path": "ft/uc/ftest/ftattach/src/action/FtAttachAction.cpp", "language": "cpp", "score": 90}, {"snippet_index": 3, "file_path": "ft/uc/ftest/ftattach/src/action/FtAttachAction.cpp", "language": "cpp", "score": 90}, {"snippet_index": 4, "file_path": "ft/uc/ftest/ftattach/src/action/FtAttachAction.cpp", "language": "cpp", "score": 100}, {"snippet_index": 5, "file_path": "ft/uc/ftest/ftattach/src/action/FtAttachAction.cpp", "language": "cpp", "score": 90}, {"snippet_index": 6, "file_path": "ft/uc/ftest/ftattach/src/action/FtAttachAction.cpp", "language": "cpp", "score": 90}, {"snippet_index": 7, "file_path": "ft/uc/ftest/ftattach/src/action/FtAttachAction.cpp", "language": "cpp", "score": 90}, {"snippet_index": 8, "file_path": "ft/uc/ftest/ftattach/src/action/FtAttachAction.cpp", "language": "cpp", "score": 90}, {"snippet_index": 9, "file_path": "ft/uc/ftest/ftattach/src/action/FtAttachAction.cpp", "language": "cpp", "score": 100}, {"snippet_index": 10, "file_path": "ft/uc/ftest/ftattach/src/action/FtAttachAction.cpp", "language": "cpp", "score": 100}, {"snippet_index": 11, "file_path": "ft/uc/ftest/ftattach/src/action/FtAttachAction.cpp", "language": "cpp", "score": 100}, {"snippet_index": 12, "file_path": "ft/uc/ftest/ftattach/src/action/FtAttachAction.cpp", "language": "cpp", "score": 90}, {"snippet_index": 13, "file_path": "ft/uc/ftest/ftattach/src/action/FtAttachAction.cpp", "language": "cpp", "score": 90}, {"snippet_index": 14, "file_path": "ft/uc/ftest/ftattach/src/action/FtAttachAction.cpp", "language": "cpp", "score": 90}, {"snippet_index": 15, "file_path": "ft/uc/ftest/ftattach/src/action/FtAttachAction.cpp", "language": "unknown", "score": 85}, {"snippet_index": 16, "file_path": "ft/uc/ftest/ftattach/src/action/FtAttachAction.cpp", "language": "unknown", "score": 75}, {"snippet_index": 17, "file_path": "ft/uc/ftest/ftattach/src/action/FtAttachAction.cpp", "language": "unknown", "score": 75}, {"snippet_index": 18, "file_path": "ft/uc/ftest/ftattach/src/action/FtAttachAction.cpp", "language": "unknown", "score": 75}, {"snippet_index": 19, "file_path": "ft/uc/ftest/ftattach/src/action/FtAttachAction.cpp", "language": "unknown", "score": 75}], "total_snippets": 20}, "suggestions": []}], "metadata": {"evaluator_count": 5, "total_issues": 4}}, {"corpus_id": "RAN-6612580", "file_path": "corpus/测试用例 RAN-6612580_ 低速迁出测量场景，存在gumai惩罚的邻区，异系统测量上报无输出.md", "overall_score": 38.70586329770003, "evaluation_results": [{"evaluator_name": "completeness_evaluator", "level": "critical", "score": 44.44444444444444, "message": "Missing 5 required fields", "details": {"missing_fields": ["test_info.test_title", "test_info.tc_steps", "test_info.tc_expected_results", "tag_identification.business_content_scene_tags", "tag_identification.code_modify_scene_tags"], "completeness_percentage": 44.44444444444444}, "suggestions": ["Please fill in the missing field: test_info.test_title", "Please fill in the missing field: test_info.tc_steps", "Please fill in the missing field: test_info.tc_expected_results", "Please fill in the missing field: tag_identification.business_content_scene_tags", "Please fill in the missing field: tag_identification.code_modify_scene_tags"]}, {"evaluator_name": "content_quality_evaluator", "level": "critical", "score": 0.0, "message": "Content quality is poor", "details": {"title_score": 0, "steps_score": 0, "expected_results_score": 0, "overall_score": 0.0}, "suggestions": ["Improve test title clarity and add RAN ID reference", "Add more detailed test steps with specific actions", "Provide more specific expected results"]}, {"evaluator_name": "format_validation_evaluator", "level": "warning", "score": 50, "message": "Found 2 format issues", "details": {"format_issues": ["Invalid Gerrit link format", "Code snippets have formatting issues"]}, "suggestions": ["Fix format issue: Invalid Gerrit link format", "Fix format issue: Code snippets have formatting issues"]}, {"evaluator_name": "test_case_validity_evaluator", "level": "critical", "score": 25, "message": "Test case has significant validity problems", "details": {"validity_score": 25, "issues": ["Test objective is not clear", "Test steps are not executable", "Expected results are not verifiable"]}, "suggestions": ["Address issue: Test objective is not clear", "Address issue: Test steps are not executable", "Address issue: Expected results are not verifiable"]}, {"evaluator_name": "code_quality_evaluator", "level": "info", "score": 83.57142857142857, "message": "Code quality is good", "details": {"average_score": 83.57142857142857, "snippet_scores": [{"snippet_index": 0, "file_path": "ft/drm/lf/src/test/mrRptDecision/speepMigMeas/FtSpeedMigRptHandle.cpp", "language": "cpp", "score": 100}, {"snippet_index": 1, "file_path": "ft/drm/lf/src/test/mrRptDecision/speepMigMeas/FtSpeedMigRptHandle.cpp", "language": "cpp", "score": 90}, {"snippet_index": 2, "file_path": "ft/drm/lf/src/test/mrRptDecision/speepMigMeas/FtSpeedMigRptHandle.cpp", "language": "cpp", "score": 90}, {"snippet_index": 3, "file_path": "ft/drm/lf/src/test/mrRptDecision/speepMigMeas/FtSpeedMigRptHandle.cpp", "language": "cpp", "score": 90}, {"snippet_index": 4, "file_path": "ft/drm/lf/src/test/mrRptDecision/speepMigMeas/FtSpeedMigRptHandle.cpp", "language": "cpp", "score": 90}, {"snippet_index": 5, "file_path": "ft/drm/lf/src/test/mrRptDecision/speepMigMeas/FtSpeedMigRptHandle.cpp", "language": "cpp", "score": 90}, {"snippet_index": 6, "file_path": "ft/drm/lf/src/test/mrRptDecision/speepMigMeas/FtSpeedMigRptHandle.cpp", "language": "cpp", "score": 90}, {"snippet_index": 7, "file_path": "ft/drm/lf/src/test/mrRptDecision/speepMigMeas/FtSpeedMigRptHandle.cpp", "language": "cpp", "score": 90}, {"snippet_index": 8, "file_path": "ft/drm/lf/src/test/mrRptDecision/speepMigMeas/FtSpeedMigRptHandle.cpp", "language": "proto", "score": 90}, {"snippet_index": 9, "file_path": "ft/drm/lf/src/test/mrRptDecision/speepMigMeas/FtSpeedMigRptHandle.cpp", "language": "cpp", "score": 90}, {"snippet_index": 10, "file_path": "ft/drm/lf/src/test/mrRptDecision/speepMigMeas/FtSpeedMigRptHandle.cpp", "language": "proto", "score": 90}, {"snippet_index": 11, "file_path": "ft/drm/lf/src/test/mrRptDecision/speepMigMeas/FtSpeedMigRptHandle.cpp", "language": "cpp", "score": 90}, {"snippet_index": 12, "file_path": "ft/drm/lf/src/test/mrRptDecision/speepMigMeas/FtSpeedMigRptHandle.cpp", "language": "cpp", "score": 90}, {"snippet_index": 13, "file_path": "ft/drm/lf/src/test/mrRptDecision/speepMigMeas/FtSpeedMigRptHandle.cpp", "language": "proto", "score": 90}, {"snippet_index": 14, "file_path": "ft/drm/lf/src/test/mrRptDecision/speepMigMeas/FtSpeedMigRptHandle.cpp", "language": "cpp", "score": 100}, {"snippet_index": 15, "file_path": "ft/drm/lf/src/test/mrRptDecision/speepMigMeas/FtSpeedMigRptHandle.cpp", "language": "cpp", "score": 90}, {"snippet_index": 16, "file_path": "ft/drm/lf/src/test/mrRptDecision/speepMigMeas/FtSpeedMigRptHandle.cpp", "language": "unknown", "score": 75}, {"snippet_index": 17, "file_path": "ft/drm/lf/src/test/mrRptDecision/speepMigMeas/FtSpeedMigRptHandle.cpp", "language": "unknown", "score": 75}, {"snippet_index": 18, "file_path": "ft/drm/lf/src/test/mrRptDecision/speepMigMeas/FtSpeedMigRptHandle.cpp", "language": "unknown", "score": 75}, {"snippet_index": 19, "file_path": "ft/drm/lf/src/test/mrRptDecision/speepMigMeas/FtSpeedMigRptHandle.cpp", "language": "unknown", "score": 75}, {"snippet_index": 20, "file_path": "ft/drm/lf/src/test/mrRptDecision/speepMigMeas/FtSpeedMigRptHandle.cpp", "language": "unknown", "score": 75}, {"snippet_index": 21, "file_path": "ft/drm/lf/src/test/mrRptDecision/speepMigMeas/FtSpeedMigRptHandle.cpp", "language": "unknown", "score": 75}, {"snippet_index": 22, "file_path": "ft/drm/lf/src/test/mrRptDecision/speepMigMeas/FtSpeedMigRptHandle.cpp", "language": "unknown", "score": 75}, {"snippet_index": 23, "file_path": "ft/drm/lf/src/test/mrRptDecision/speepMigMeas/FtSpeedMigRptHandle.cpp", "language": "unknown", "score": 75}, {"snippet_index": 24, "file_path": "ft/drm/lf/src/test/mrRptDecision/speepMigMeas/FtSpeedMigRptHandle.cpp", "language": "unknown", "score": 75}, {"snippet_index": 25, "file_path": "ft/drm/lf/src/test/mrRptDecision/speepMigMeas/FtSpeedMigRptHandle.cpp", "language": "unknown", "score": 65}, {"snippet_index": 26, "file_path": "ft/drm/lf/src/test/mrRptDecision/speepMigMeas/FtSpeedMigRptHandle.cpp", "language": "unknown", "score": 65}, {"snippet_index": 27, "file_path": "ft/drm/lf/src/test/mrRptDecision/speepMigMeas/FtSpeedMigRptHandle.cpp", "language": "unknown", "score": 75}], "total_snippets": 28}, "suggestions": ["Improve 2 code snippets with low quality scores"]}], "metadata": {"evaluator_count": 5, "total_issues": 4}}], "failed_files": [], "total_processed": 4, "total_failed": 0}