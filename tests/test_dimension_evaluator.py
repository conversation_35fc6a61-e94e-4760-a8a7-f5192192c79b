import unittest
from pathlib import Path
import sys
import tempfile
import json

sys.path.append(str(Path(__file__).parent.parent))

from ft_corpus_evaluator.models.corpus_model import FTCorpus, RdcInfo, TestInfo, TagIdentification, CodeSnippet
from ft_corpus_evaluator.evaluators.dimension_evaluator import DimensionEvaluator
from ft_corpus_evaluator.evaluators.dimension_framework import DimensionManager
from ft_corpus_evaluator.evaluators.completeness_dimension import CompletenessDimension
from ft_corpus_evaluator.evaluators.correctness_dimension import CorrectnessDimension
from ft_corpus_evaluator.evaluators.difficulty_dimension import DifficultyDimension
from ft_corpus_evaluator.models.evaluation_models import EvaluationDimension, EvaluationLevel
from ft_corpus_evaluator.core.evaluator_manager import EvaluatorManager
from ft_corpus_evaluator.core.batch_processor import BatchProcessor


class TestDimensionEvaluator(unittest.TestCase):
    
    def setUp(self):
        self.sample_corpus = FTCorpus(
            file_path="test_file.md",
            rdc_info=RdcInfo(
                rdc_id="RAN-1234567",
                repo_name="test_repo",
                gerrit_link="https://gerrit.zte.com.cn/#/c/123456/1",
                date="2024-01-15"
            ),
            test_info=TestInfo(
                test_title="测试用例 RAN-1234567: 5G基站切换优化场景验证",
                tc_steps="1. 配置基站参数，启用切换优化功能\n2. 模拟UE在小区间移动，触发切换流程\n3. 监控切换成功率和延迟指标",
                tc_expected_results="1. 基站参数配置成功，优化功能启用\n2. UE成功触发切换流程，无异常\n3. 切换成功率达到95%以上，延迟小于100ms"
            ),
            tag_identification=TagIdentification(
                business_content_scene_tags=["5G", "基站", "切换优化"],
                code_modify_scene_tags=["RAN", "切换算法", "性能优化"]
            ),
            code_snippets=[
                CodeSnippet(
                    file_path="src/handover_optimizer.cpp",
                    language="cpp",
                    content="""
class HandoverOptimizer {
public:
    void enableOptimization() {
        // 启用切换优化
        optimizationEnabled = true;
    }
    
    bool processHandover(UE* ue) {
        if (!optimizationEnabled) return false;
        // 处理切换逻辑
        return true;
    }
private:
    bool optimizationEnabled = false;
};
                    """.strip()
                )
            ]
        )
    
    def test_dimension_evaluator_initialization(self):
        evaluator = DimensionEvaluator()
        self.assertEqual(evaluator.name, "dimension_evaluator")
        self.assertIsNotNone(evaluator.dimension_manager)
        self.assertEqual(len(evaluator.dimension_manager.dimensions), 3)
    
    def test_dimension_manager_registration(self):
        manager = DimensionManager()
        
        completeness_dim = CompletenessDimension()
        correctness_dim = CorrectnessDimension()
        difficulty_dim = DifficultyDimension()
        
        manager.register_dimension(completeness_dim)
        manager.register_dimension(correctness_dim)
        manager.register_dimension(difficulty_dim)
        
        self.assertEqual(len(manager.dimensions), 3)
        self.assertIn(EvaluationDimension.COMPLETENESS, manager.dimensions)
        self.assertIn(EvaluationDimension.CORRECTNESS, manager.dimensions)
        self.assertIn(EvaluationDimension.DIFFICULTY, manager.dimensions)
    
    def test_completeness_dimension_evaluation(self):
        dimension = CompletenessDimension()
        result = dimension.evaluate(self.sample_corpus)
        
        self.assertEqual(result.dimension, EvaluationDimension.COMPLETENESS)
        self.assertGreater(result.overall_score, 0)
        self.assertLessEqual(result.overall_score, 100)
        self.assertGreater(len(result.metric_results), 0)
        self.assertGreater(len(result.enabled_metrics), 0)
    
    def test_correctness_dimension_evaluation(self):
        dimension = CorrectnessDimension()
        result = dimension.evaluate(self.sample_corpus)
        
        self.assertEqual(result.dimension, EvaluationDimension.CORRECTNESS)
        self.assertGreater(result.overall_score, 0)
        self.assertLessEqual(result.overall_score, 100)
        self.assertGreater(len(result.metric_results), 0)
    
    def test_difficulty_dimension_evaluation(self):
        dimension = DifficultyDimension()
        result = dimension.evaluate(self.sample_corpus)
        
        self.assertEqual(result.dimension, EvaluationDimension.DIFFICULTY)
        self.assertGreater(result.overall_score, 0)
        self.assertLessEqual(result.overall_score, 100)
        self.assertGreater(len(result.metric_results), 0)
    
    def test_dimension_comprehensive_evaluation(self):
        evaluator = DimensionEvaluator()
        results = evaluator.evaluate(self.sample_corpus)
        
        self.assertGreater(len(results), 0)
        
        dimension_results = [r for r in results if r.dimension is not None]
        comprehensive_result = [r for r in results if r.dimension is None]
        
        self.assertEqual(len(dimension_results), 3)
        self.assertEqual(len(comprehensive_result), 1)
        
        for result in dimension_results:
            self.assertIn(result.dimension, [
                EvaluationDimension.COMPLETENESS,
                EvaluationDimension.CORRECTNESS,
                EvaluationDimension.DIFFICULTY
            ])
            self.assertGreater(result.score, 0)
            self.assertLessEqual(result.score, 100)
    
    def test_metric_enable_disable(self):
        evaluator = DimensionEvaluator()
        
        initial_metrics = evaluator.get_enabled_metrics(EvaluationDimension.COMPLETENESS)
        self.assertGreater(len(initial_metrics), 0)
        
        evaluator.disable_metric(EvaluationDimension.COMPLETENESS, "required_fields")
        updated_metrics = evaluator.get_enabled_metrics(EvaluationDimension.COMPLETENESS)
        self.assertNotIn("required_fields", updated_metrics)
        
        evaluator.enable_metric(EvaluationDimension.COMPLETENESS, "required_fields")
        final_metrics = evaluator.get_enabled_metrics(EvaluationDimension.COMPLETENESS)
        self.assertIn("required_fields", final_metrics)
    
    def test_custom_configuration(self):
        config = {
            "dimensions": {
                "completeness": {
                    "enabled": True,
                    "weight": 1.5,
                    "metrics": {
                        "required_fields": {
                            "enabled": True,
                            "weight": 2.0
                        }
                    }
                }
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(config, f)
            config_path = f.name
        
        try:
            evaluator = DimensionEvaluator(config_path)
            results = evaluator.evaluate(self.sample_corpus)
            self.assertGreater(len(results), 0)
        finally:
            Path(config_path).unlink()
    
    def test_evaluator_manager_integration(self):
        manager = EvaluatorManager()
        self.assertIsNotNone(manager.dimension_evaluator)

        report = manager.evaluate_corpus(self.sample_corpus)
        self.assertGreater(report.overall_score, 0)
        self.assertGreater(len(report.evaluation_results), 0)
    
    def test_dimension_specific_evaluation(self):
        manager = EvaluatorManager()

        for dimension in EvaluationDimension:
            result = manager.evaluate_by_dimension(self.sample_corpus, dimension)
            self.assertIsNotNone(result)
            self.assertEqual(result.dimension, dimension)
            self.assertGreater(result.overall_score, 0)
    
    def test_batch_processor_integration(self):
        manager = EvaluatorManager()
        processor = BatchProcessor(manager)

        metrics_status = processor.get_dimension_metrics_status()
        self.assertGreater(len(metrics_status), 0)

        processor.disable_dimension_metric(
            EvaluationDimension.COMPLETENESS,
            "code_snippets_completeness"
        )

        updated_status = processor.get_dimension_metrics_status()
        completeness_metrics = updated_status.get("completeness", {})
        self.assertFalse(completeness_metrics.get("code_snippets_completeness", True))
    

    
    def test_incomplete_corpus_handling(self):
        incomplete_corpus = FTCorpus(
            file_path="incomplete_test.md",
            rdc_info=RdcInfo(
                rdc_id="",  # 缺失RDC ID
                repo_name="",
                gerrit_link="invalid_link",
                date="invalid_date"
            ),
            test_info=TestInfo(
                test_title="",  # 缺失标题
                tc_steps="",  # 缺失步骤
                tc_expected_results=""
            ),
            tag_identification=TagIdentification(
                business_content_scene_tags=[],
                code_modify_scene_tags=[]
            ),
            code_snippets=[]
        )
        
        evaluator = DimensionEvaluator()
        results = evaluator.evaluate(incomplete_corpus)
        
        self.assertGreater(len(results), 0)
        
        critical_results = [r for r in results if r.level == EvaluationLevel.CRITICAL]
        self.assertGreater(len(critical_results), 0)


class TestDimensionMetrics(unittest.TestCase):
    
    def setUp(self):
        self.sample_corpus = FTCorpus(
            file_path="test_file.md",
            rdc_info=RdcInfo(
                rdc_id="RAN-1234567",
                repo_name="test_repo",
                gerrit_link="https://gerrit.zte.com.cn/#/c/123456/1",
                date="2024-01-15"
            ),
            test_info=TestInfo(
                test_title="测试用例 RAN-1234567: 基本功能验证",
                tc_steps="1. 步骤1\n2. 步骤2",
                tc_expected_results="1. 结果1\n2. 结果2"
            ),
            tag_identification=TagIdentification(
                business_content_scene_tags=["基本功能"],
                code_modify_scene_tags=["功能测试"]
            )
        )
    
    def test_required_fields_metric(self):
        from ft_corpus_evaluator.evaluators.completeness_dimension import RequiredFieldsMetric
        
        metric = RequiredFieldsMetric()
        result = metric.evaluate(self.sample_corpus)
        
        self.assertEqual(result.dimension, EvaluationDimension.COMPLETENESS)
        self.assertEqual(result.metric_name, "required_fields")
        self.assertGreater(result.score, 0)
    
    def test_format_correctness_metric(self):
        from ft_corpus_evaluator.evaluators.correctness_dimension import FormatCorrectnessMetric
        
        metric = FormatCorrectnessMetric()
        result = metric.evaluate(self.sample_corpus)
        
        self.assertEqual(result.dimension, EvaluationDimension.CORRECTNESS)
        self.assertEqual(result.metric_name, "format_correctness")
        self.assertGreater(result.score, 0)
    
    def test_technical_complexity_metric(self):
        from ft_corpus_evaluator.evaluators.difficulty_dimension import TechnicalComplexityMetric
        
        metric = TechnicalComplexityMetric()
        result = metric.evaluate(self.sample_corpus)
        
        self.assertEqual(result.dimension, EvaluationDimension.DIFFICULTY)
        self.assertEqual(result.metric_name, "technical_complexity")
        self.assertGreaterEqual(result.score, 0)


if __name__ == '__main__':
    unittest.main()
