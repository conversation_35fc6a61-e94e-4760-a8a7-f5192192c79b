#!/usr/bin/env python3
"""
测试Answer字段解析
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from ft_corpus_evaluator.parsers.markdown_corpus_parser import MarkdownCorpusParser
from ft_corpus_evaluator.parsers.corpus_parser import CorpusParser
from ft_corpus_evaluator.parsers.improved_markdown_parser import ImprovedMarkdownCorpusParser


def test_answer_field():
    """测试Answer字段解析"""
    print("🧪 测试Answer字段解析")
    print("=" * 60)
    
    # 测试有ANSWER部分的文件
    test_file = Path("corpus/RAN-5946243 RRC建立优化场景MSG5的传递路径：LUCM-_LUC（UlDcchTranmissionMessage）传递远近点标识.md")
    if not test_file.exists():
        print("❌ 测试文件不存在")
        return False
    
    parsers = [
        ("MarkdownCorpusParser", MarkdownCorpusParser()),
        ("CorpusParser", CorpusParser()),
        ("ImprovedMarkdownCorpusParser", ImprovedMarkdownCorpusParser())
    ]
    
    all_success = True
    
    for parser_name, parser in parsers:
        print(f"\n📋 测试 {parser_name}:")
        
        try:
            # 解析文件
            result = parser.parse_file(test_file)
            
            # 测试Answer字段
            if result.answer:
                content = result.answer.content
                print(f"  ✅ 有Answer内容，长度: {len(content)} 字符")
                
                # 检查是否包含关键内容
                key_items = [
                    "//STEPS_DESCRIBE_START",
                    "//STEP1:LUCM->LUC",
                    "//STEP2:LUC收到远近点信息后",
                    "- path:ft/uc/ftest/ftattach/src/FtAttach.cpp",
                    "- type:add_test",
                    "TEST_F(FtAttachWithMsg4Opt",
                    "hasPosition"
                ]
                
                missing_items = []
                for item in key_items:
                    if item not in content:
                        missing_items.append(item)
                
                if not missing_items:
                    print("  ✅ 包含所有关键内容")
                else:
                    print(f"  ❌ 缺少内容: {missing_items}")
                
                # 检查格式是否保持
                has_steps_describe = "//STEPS_DESCRIBE_START" in content
                has_code_blocks = "```cpp" in content
                has_path_info = "- path:" in content
                has_type_info = "- type:" in content
                
                print(f"  {'✅' if has_steps_describe else '❌'} 保持步骤描述格式")
                print(f"  {'✅' if has_code_blocks else '❌'} 保持代码块格式")
                print(f"  {'✅' if has_path_info else '❌'} 保持路径信息")
                print(f"  {'✅' if has_type_info else '❌'} 保持类型信息")
                
                # 显示内容预览
                print(f"\n📄 内容预览（前300字符）:")
                print(content[:300])
                print("...")
                
                success = (not missing_items and has_steps_describe and 
                          has_code_blocks and has_path_info and has_type_info and
                          len(content) > 500)  # 应该有足够的内容
                
                if success:
                    print(f"  🎉 {parser_name} 解析成功")
                else:
                    print(f"  ❌ {parser_name} 解析失败")
                    all_success = False
                
            else:
                print(f"  ❌ {parser_name}: Answer字段为None")
                all_success = False
                
        except Exception as e:
            print(f"  ❌ {parser_name} 解析出错: {e}")
            all_success = False
    
    # 测试没有ANSWER部分的文件
    print(f"\n📋 测试没有ANSWER部分的文件:")
    test_file_no_answer = Path("corpus/测试用例 RAN-6612580_ 低速迁出测量场景，存在gumai惩罚的邻区，异系统测量上报无输出.md")
    if test_file_no_answer.exists():
        parser = MarkdownCorpusParser()
        result = parser.parse_file(test_file_no_answer)
        
        if result.answer is None:
            print("  ✅ 正确处理没有ANSWER部分的文件（返回None）")
        else:
            print(f"  ⚠️  没有ANSWER部分的文件返回了内容: {result.answer.content[:100] if result.answer.content else 'empty'}")
    
    print(f"\n📊 测试结果:")
    print(f"  总体结果: {'🎉 完全成功' if all_success else '⚠️  部分失败'}")
    
    return all_success


if __name__ == "__main__":
    success = test_answer_field()
    sys.exit(0 if success else 1)
