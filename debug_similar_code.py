#!/usr/bin/env python3
"""
调试SimilarCodeInfo解析问题
"""

import sys
import re
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from ft_corpus_evaluator.parsers.improved_markdown_parser import ImprovedMarkdownCorpusParser


def debug_similar_code_parsing():
    """调试相似代码解析"""
    print("🔍 调试SimilarCodeInfo解析问题")
    print("=" * 60)
    
    test_file = Path("corpus/RAN-5869391.md")
    if not test_file.exists():
        print("❌ 测试文件不存在")
        return
    
    # 读取文件内容
    content = test_file.read_text(encoding='utf-8')
    
    # 创建解析器
    parser = ImprovedMarkdownCorpusParser()
    
    # 分割sections
    sections = parser._split_content_by_headers(content)
    
    print(f"找到的sections ({len(sections)}):")
    for title in sections.keys():
        print(f"  - {title}")
    
    # 检查相似代码信息section
    similar_content = sections.get('相似代码信息', '')
    print(f"\n相似代码信息section存在: {bool(similar_content)}")
    print(f"相似代码信息内容长度: {len(similar_content)} 字符")
    
    if similar_content:
        print(f"\n相似代码信息内容:")
        print("-" * 40)
        print(similar_content[:500] + "..." if len(similar_content) > 500 else similar_content)
        print("-" * 40)
        
        # 测试相似FT代码的正则表达式
        print(f"\n测试相似FT代码正则表达式:")
        ft_code_pattern = r'## 相似用例FT代码\n(.*?)(?=## |# |$)'
        ft_code_match = re.search(ft_code_pattern, similar_content, re.DOTALL)
        
        if ft_code_match:
            ft_section = ft_code_match.group(1)
            print(f"✅ 匹配成功，提取的内容长度: {len(ft_section)} 字符")
            print(f"提取的内容:")
            print(ft_section[:300] + "..." if len(ft_section) > 300 else ft_section)
            
            # 测试文件路径提取
            file_path_pattern = r'代码路径[：:]?\s*(.+)'
            file_path_match = re.search(file_path_pattern, ft_section)
            if file_path_match:
                file_path = file_path_match.group(1).strip()
                print(f"✅ 文件路径: {file_path}")
            else:
                print("❌ 未找到文件路径")
            
            # 测试代码块提取
            code_blocks_pattern = r'```(\w+)?\n(.*?)\n```'
            code_blocks = re.findall(code_blocks_pattern, ft_section, re.DOTALL)
            print(f"✅ 找到 {len(code_blocks)} 个代码块")
            
            for i, (language, code) in enumerate(code_blocks):
                print(f"  代码块 {i+1}: 语言={language or 'unknown'}, 长度={len(code)} 字符")
                print(f"    前50字符: {repr(code[:50])}")
        else:
            print("❌ 正则表达式匹配失败")
            
            # 尝试其他可能的模式
            print("\n尝试其他模式:")
            
            # 检查是否存在相似用例FT代码标题
            if "相似用例FT代码" in similar_content:
                print("✅ 找到'相似用例FT代码'文本")
                
                # 查找其位置
                pos = similar_content.find("相似用例FT代码")
                print(f"位置: {pos}")
                print(f"周围内容: {repr(similar_content[max(0, pos-20):pos+50])}")
            else:
                print("❌ 未找到'相似用例FT代码'文本")
    
    # 测试完整解析
    print(f"\n测试完整解析:")
    result = parser.parse_file(test_file)
    
    if result.similar_code_info:
        print(f"✅ SimilarCodeInfo存在")
        print(f"  similar_test_case: {result.similar_code_info.similar_test_case}")
        print(f"  similar_ft_code数量: {len(result.similar_code_info.similar_ft_code)}")
        print(f"  similar_dependency_code数量: {len(result.similar_code_info.similar_dependency_code)}")
        
        for i, code in enumerate(result.similar_code_info.similar_ft_code):
            print(f"    FT代码 {i+1}: {code.language}, {len(code.content)} 字符, 路径: {code.file_path}")
    else:
        print("❌ SimilarCodeInfo为None")


if __name__ == "__main__":
    debug_similar_code_parsing()
