#!/usr/bin/env python3
"""
最终验证preconditions字段类型改变
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from ft_corpus_evaluator.parsers.corpus_parser import CorpusParser
from ft_corpus_evaluator.parsers.improved_markdown_parser import ImprovedMarkdownCorpusParser
from ft_corpus_evaluator.models.corpus_model import TestInfo, SimilarTestCase


def test_model_types():
    """测试模型类型定义"""
    print("🔍 测试模型类型定义")
    print("=" * 50)
    
    # 测试TestInfo
    test_info = TestInfo(
        test_title="测试标题",
        preconditions="这是预置条件",
        tc_steps="步骤1\n步骤2",
        tc_expected_results="结果1\n结果2"
    )
    
    print(f"TestInfo.preconditions类型: {type(test_info.preconditions)}")
    print(f"TestInfo.preconditions值: {repr(test_info.preconditions)}")
    print(f"TestInfo.tc_steps类型: {type(test_info.tc_steps)}")
    print(f"TestInfo.tc_steps值: {repr(test_info.tc_steps)}")
    print(f"TestInfo.tc_expected_results类型: {type(test_info.tc_expected_results)}")
    print(f"TestInfo.tc_expected_results值: {repr(test_info.tc_expected_results)}")
    
    # 测试SimilarTestCase
    similar_test = SimilarTestCase(
        test_title="相似测试",
        preconditions="相似预置条件",
        tc_steps="相似步骤1\n相似步骤2",
        tc_expected_results="相似结果1\n相似结果2"
    )
    
    print(f"SimilarTestCase.preconditions类型: {type(similar_test.preconditions)}")
    print(f"SimilarTestCase.preconditions值: {repr(similar_test.preconditions)}")
    print(f"SimilarTestCase.tc_steps类型: {type(similar_test.tc_steps)}")
    print(f"SimilarTestCase.tc_expected_results类型: {type(similar_test.tc_expected_results)}")

    # 验证类型
    assert isinstance(test_info.preconditions, str), "TestInfo.preconditions应该是str类型"
    assert isinstance(test_info.tc_steps, str), "TestInfo.tc_steps应该是str类型"
    assert isinstance(test_info.tc_expected_results, str), "TestInfo.tc_expected_results应该是str类型"
    assert isinstance(similar_test.preconditions, str), "SimilarTestCase.preconditions应该是str类型"
    assert isinstance(similar_test.tc_steps, str), "SimilarTestCase.tc_steps应该是str类型"
    assert isinstance(similar_test.tc_expected_results, str), "SimilarTestCase.tc_expected_results应该是str类型"
    
    print("✅ 所有模型类型验证通过！")


def test_parser_consistency():
    """测试解析器一致性"""
    print("\n🔍 测试解析器一致性")
    print("=" * 50)
    
    test_file = Path("corpus/RAN-5869391.md")
    if not test_file.exists():
        print("❌ 测试文件不存在")
        return
    
    # 测试原解析器
    old_parser = CorpusParser()
    old_result = old_parser.parse_file(test_file)
    
    # 测试新解析器
    new_parser = ImprovedMarkdownCorpusParser()
    new_result = new_parser.parse_file(test_file)
    
    # 验证preconditions类型
    old_precond_type = type(old_result.test_info.preconditions)
    new_precond_type = type(new_result.test_info.preconditions)
    
    print(f"原解析器preconditions类型: {old_precond_type}")
    print(f"新解析器preconditions类型: {new_precond_type}")
    
    assert old_precond_type == str, f"原解析器preconditions应该是str，实际是{old_precond_type}"
    assert new_precond_type == str, f"新解析器preconditions应该是str，实际是{new_precond_type}"
    
    # 验证内容长度一致
    old_len = len(old_result.test_info.preconditions)
    new_len = len(new_result.test_info.preconditions)
    
    print(f"原解析器preconditions长度: {old_len}")
    print(f"新解析器preconditions长度: {new_len}")
    
    if old_len == new_len:
        print("✅ 内容长度完全一致！")
    else:
        print(f"⚠️  内容长度略有差异: {abs(old_len - new_len)} 字符")
    
    # 验证SimilarCodeInfo中的SimilarTestCase
    if old_result.similar_code_info and old_result.similar_code_info.similar_test_case:
        similar_precond_type = type(old_result.similar_code_info.similar_test_case.preconditions)
        print(f"SimilarTestCase.preconditions类型: {similar_precond_type}")
        assert similar_precond_type == str, f"SimilarTestCase.preconditions应该是str，实际是{similar_precond_type}"
    
    print("✅ 解析器一致性验证通过！")


def test_backward_compatibility():
    """测试向后兼容性"""
    print("\n🔍 测试向后兼容性")
    print("=" * 50)
    
    # 测试默认值
    test_info_default = TestInfo(test_title="测试")
    print(f"默认preconditions: {repr(test_info_default.preconditions)}")
    print(f"默认preconditions类型: {type(test_info_default.preconditions)}")
    
    assert test_info_default.preconditions == "", "默认preconditions应该是空字符串"
    assert isinstance(test_info_default.preconditions, str), "默认preconditions应该是str类型"
    
    # 测试显式设置
    test_info_explicit = TestInfo(
        test_title="测试",
        preconditions="显式设置的预置条件"
    )
    print(f"显式preconditions: {repr(test_info_explicit.preconditions)}")
    
    assert test_info_explicit.preconditions == "显式设置的预置条件", "显式设置的preconditions应该保持原值"
    
    print("✅ 向后兼容性验证通过！")


def test_evaluator_compatibility():
    """测试评估器兼容性"""
    print("\n🔍 测试评估器兼容性")
    print("=" * 50)
    
    try:
        from ft_corpus_evaluator.evaluators.completeness_dimension import RequiredFieldsMetric
        from ft_corpus_evaluator.models.corpus_model import FTCorpus, RdcInfo, TestInfo, TagIdentification
        
        # 创建测试语料
        test_corpus = FTCorpus(
            file_path="test.md",
            rdc_info=RdcInfo(
                rdc_id="RAN-123456",
                repo_name="test_repo",
                gerrit_link="https://test.com",
                date="2024-01-01"
            ),
            test_info=TestInfo(
                test_title="测试标题",
                preconditions="测试预置条件",  # 现在是字符串
                tc_steps="1. 步骤1\n2. 步骤2",  # 现在是字符串
                tc_expected_results="1. 结果1\n2. 结果2"  # 现在是字符串
            ),
            tag_identification=TagIdentification(
                business_content_scene_tags=["业务标签"],
                code_modify_scene_tags=["代码标签"]
            )
        )
        
        # 测试评估器
        metric = RequiredFieldsMetric()
        result = metric.evaluate(test_corpus)
        
        print(f"评估结果分数: {result.score}")
        print(f"评估结果级别: {result.level}")
        
        assert result.score > 0, "评估器应该能正常工作"
        print("✅ 评估器兼容性验证通过！")
        
    except Exception as e:
        print(f"❌ 评估器兼容性测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("🚀 开始最终验证...")
    
    try:
        test_model_types()
        test_parser_consistency()
        test_backward_compatibility()
        test_evaluator_compatibility()
        
        print("\n🎉 所有验证通过！")
        print("✅ preconditions字段已成功从List[str]改为str类型")
        print("✅ 所有解析器和评估器都兼容新的类型")
        print("✅ 向后兼容性良好")
        
    except Exception as e:
        print(f"\n❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
