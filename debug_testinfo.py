#!/usr/bin/env python3
"""
调试TestInfo解析问题
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from ft_corpus_evaluator.parsers.markdown_corpus_parser import MarkdownCorpusParser, CorpusMarkdownRenderer
import mistune


def debug_testinfo_parsing():
    """调试TestInfo解析"""
    print("🔍 调试TestInfo解析问题")
    print("=" * 60)
    
    test_file = Path("corpus/测试用例 RAN-6612580_ 低速迁出测量场景，存在gumai惩罚的邻区，异系统测量上报无输出.md")
    if not test_file.exists():
        print("❌ 测试文件不存在")
        return
    
    # 创建解析器
    parser = MarkdownCorpusParser()
    
    # 解析文件
    result = parser.parse_file(test_file)
    
    print("=== TestInfo 解析结果 ===")
    print(f"test_title: {repr(result.test_info.test_title)}")
    print(f"preconditions: {repr(result.test_info.preconditions)}")
    print(f"tc_steps: {repr(result.test_info.tc_steps)}")
    print(f"tc_expected_results: {repr(result.test_info.tc_expected_results)}")
    print(f"expected_results: {repr(result.test_info.expected_results)}")
    print(f"pass_criteria: {repr(result.test_info.pass_criteria)}")
    
    print("\n=== 渲染器sections ===")
    for name, section in parser.renderer.sections.items():
        print(f"Section: {name} (level {section.level})")
        if section.subsections:
            for sub_name, sub_section in section.subsections.items():
                print(f"  Subsection: {sub_name} (level {sub_section.level})")
                print(f"    Content: {sub_section.content[:2] if sub_section.content else []}")

    # 专门检查TestInfo section
    print("\n=== TestInfo section 详细信息 ===")
    test_section = parser.renderer.sections.get("TestInfo")
    if test_section:
        print(f"TestInfo subsections: {list(test_section.subsections.keys())}")
        for sub_name, sub_section in test_section.subsections.items():
            print(f"  {sub_name}: {sub_section.content}")
    else:
        print("❌ 没有找到TestInfo section")

    # 测试原始markdown解析
    print("\n=== 原始markdown解析测试 ===")
    content = test_file.read_text(encoding='utf-8')

    # 提取TestInfo部分
    import re
    testinfo_match = re.search(r'## TestInfo\n(.*?)(?=\n## |$)', content, re.DOTALL)
    if testinfo_match:
        testinfo_content = testinfo_match.group(1)
        print("TestInfo原始内容:")
        print(repr(testinfo_content[:500]))

        # 查找所有三级标题
        h3_matches = re.findall(r'### ([^\n]+)\n([^#]*?)(?=### |$)', testinfo_content, re.DOTALL)
        print(f"\n找到的三级标题: {len(h3_matches)}")
        for title, content_part in h3_matches:
            print(f"  {title}: {repr(content_part.strip()[:100])}")

    else:
        print("❌ 没有找到TestInfo内容")


if __name__ == "__main__":
    debug_testinfo_parsing()
