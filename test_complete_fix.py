#!/usr/bin/env python3
"""
测试MarkdownCorpusParser的完整修复
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from ft_corpus_evaluator.parsers.markdown_corpus_parser import MarkdownCorpusParser


def test_complete_fix():
    """测试完整修复"""
    print("🧪 测试MarkdownCorpusParser完整修复")
    print("=" * 60)
    
    test_file = Path("corpus/测试用例 RAN-6612580_ 低速迁出测量场景，存在gumai惩罚的邻区，异系统测量上报无输出.md")
    if not test_file.exists():
        print("❌ 测试文件不存在")
        return False
    
    # 创建解析器
    parser = MarkdownCorpusParser()
    
    # 解析文件
    result = parser.parse_file(test_file)
    
    print("✅ 解析成功！")
    
    # 测试TestInfo
    print("\n📋 TestInfo 测试:")
    testinfo_fields = [
        ('test_title', result.test_info.test_title),
        ('preconditions', result.test_info.preconditions),
        ('tc_steps', result.test_info.tc_steps),
        ('tc_expected_results', result.test_info.tc_expected_results),
        ('expected_results', result.test_info.expected_results),
        ('pass_criteria', result.test_info.pass_criteria)
    ]
    
    testinfo_success = 0
    for field_name, field_value in testinfo_fields:
        has_value = bool(field_value and field_value.strip())
        status = "✅" if has_value else "❌"
        print(f"  {status} {field_name}: {'有值' if has_value else '无值'}")
        if has_value:
            testinfo_success += 1
    
    # 测试TagIdentification
    print("\n🏷️  TagIdentification 测试:")
    
    # 测试business_content_scene_tags
    business_tags = result.tag_identification.business_content_scene_tags
    business_expected = ['参数配置', 'protobuf消息构造', 'protobuf消息校验']
    business_correct = business_tags == business_expected
    print(f"  {'✅' if business_correct else '❌'} business_content_scene_tags: {business_tags}")
    if not business_correct:
        print(f"    期望: {business_expected}")
    
    # 测试code_modify_scene_tags
    code_tags = result.tag_identification.code_modify_scene_tags
    code_expected = ['add_test_function']
    code_correct = code_tags == code_expected
    print(f"  {'✅' if code_correct else '❌'} code_modify_scene_tags: {code_tags}")
    if not code_correct:
        print(f"    期望: {code_expected}")
    
    # 检查是否包含不应该包含的内容
    has_question = any('QUESTION' in str(tag) for tag in code_tags)
    has_html = any('<strong>' in str(tag) for tag in code_tags)
    clean_tags = not has_question and not has_html
    print(f"  {'✅' if clean_tags else '❌'} 不包含QUESTION或HTML标签: {'是' if clean_tags else '否'}")
    
    # 统计结果
    print(f"\n📊 测试结果:")
    print(f"  TestInfo字段成功率: {testinfo_success}/6 ({testinfo_success/6*100:.1f}%)")
    print(f"  TagIdentification正确性: {'✅ 完全正确' if business_correct and code_correct and clean_tags else '❌ 有问题'}")
    
    overall_success = (testinfo_success == 6) and business_correct and code_correct and clean_tags
    print(f"  总体测试结果: {'🎉 完全成功' if overall_success else '⚠️  部分失败'}")
    
    return overall_success


if __name__ == "__main__":
    success = test_complete_fix()
    sys.exit(0 if success else 1)
