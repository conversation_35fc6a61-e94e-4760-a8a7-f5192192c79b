#!/usr/bin/env python3
"""
测试SimilarCodeInfo修复效果
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from ft_corpus_evaluator.parsers.corpus_parser import CorpusParser
from ft_corpus_evaluator.parsers.improved_markdown_parser import ImprovedMarkdownCorpusParser


def test_similar_code_fix():
    """测试SimilarCodeInfo修复效果"""
    print("🔍 测试SimilarCodeInfo修复效果")
    print("=" * 70)
    
    test_file = Path("corpus/RAN-5869391.md")
    if not test_file.exists():
        print("❌ 测试文件不存在")
        return
    
    # 测试原解析器
    print("\n📋 原解析器结果:")
    print("-" * 40)
    old_parser = CorpusParser()
    old_result = old_parser.parse_file(test_file)
    
    if old_result.similar_code_info:
        print(f"✅ SimilarCodeInfo存在")
        print(f"  similar_test_case: {old_result.similar_code_info.similar_test_case}")
        print(f"  similar_ft_code数量: {len(old_result.similar_code_info.similar_ft_code)}")
        print(f"  similar_dependency_code数量: {len(old_result.similar_code_info.similar_dependency_code)}")
        
        for i, code in enumerate(old_result.similar_code_info.similar_ft_code):
            print(f"    FT代码 {i+1}: {code.language}, {len(code.content)} 字符")
            print(f"      路径: {code.file_path}")
            print(f"      前50字符: {repr(code.content[:50])}")
    else:
        print("❌ SimilarCodeInfo为None")
    
    # 测试新解析器
    print("\n🚀 ImprovedMarkdownCorpusParser结果:")
    print("-" * 40)
    new_parser = ImprovedMarkdownCorpusParser()
    new_result = new_parser.parse_file(test_file)
    
    if new_result.similar_code_info:
        print(f"✅ SimilarCodeInfo存在")
        print(f"  similar_test_case: {new_result.similar_code_info.similar_test_case}")
        print(f"  similar_ft_code数量: {len(new_result.similar_code_info.similar_ft_code)}")
        print(f"  similar_dependency_code数量: {len(new_result.similar_code_info.similar_dependency_code)}")
        
        for i, code in enumerate(new_result.similar_code_info.similar_ft_code):
            print(f"    FT代码 {i+1}: {code.language}, {len(code.content)} 字符")
            print(f"      路径: {code.file_path}")
            print(f"      前50字符: {repr(code.content[:50])}")
    else:
        print("❌ SimilarCodeInfo为None")
    
    # 对比结果
    print("\n📊 对比结果:")
    print("-" * 40)
    
    old_ft_count = len(old_result.similar_code_info.similar_ft_code) if old_result.similar_code_info else 0
    new_ft_count = len(new_result.similar_code_info.similar_ft_code) if new_result.similar_code_info else 0
    
    old_dep_count = len(old_result.similar_code_info.similar_dependency_code) if old_result.similar_code_info else 0
    new_dep_count = len(new_result.similar_code_info.similar_dependency_code) if new_result.similar_code_info else 0
    
    ft_status = "✅" if old_ft_count == new_ft_count else "❌"
    dep_status = "✅" if old_dep_count == new_dep_count else "❌"
    
    print(f"  {ft_status} similar_ft_code: 原{old_ft_count} vs 新{new_ft_count}")
    print(f"  {dep_status} similar_dependency_code: 原{old_dep_count} vs 新{new_dep_count}")
    
    # 检查内容是否一致
    if old_ft_count > 0 and new_ft_count > 0:
        old_content = old_result.similar_code_info.similar_ft_code[0].content
        new_content = new_result.similar_code_info.similar_ft_code[0].content
        
        content_status = "✅" if len(old_content) == len(new_content) else "❌"
        print(f"  {content_status} 内容长度: 原{len(old_content)} vs 新{len(new_content)} 字符")
        
        if old_content.strip() == new_content.strip():
            print("  ✅ 内容完全一致")
        else:
            print("  ⚠️  内容略有差异")
    
    # 总体评估
    if old_ft_count == new_ft_count and old_dep_count == new_dep_count and old_ft_count > 0:
        print("\n🎉 修复成功！SimilarCodeInfo现在能正确解析")
        return True
    elif new_ft_count > old_ft_count:
        print("\n🎉 修复成功！新解析器解析出更多内容")
        return True
    else:
        print("\n⚠️  仍需进一步调试")
        return False


def test_other_corpus_files():
    """测试其他语料文件"""
    print("\n🔍 测试其他语料文件")
    print("=" * 50)
    
    corpus_dir = Path("corpus")
    if not corpus_dir.exists():
        print("❌ corpus目录不存在")
        return
    
    md_files = list(corpus_dir.glob("*.md"))[:3]  # 只测试前3个文件
    
    parser = ImprovedMarkdownCorpusParser()
    
    for file_path in md_files:
        print(f"\n测试文件: {file_path.name}")
        try:
            result = parser.parse_file(file_path)
            
            if result.similar_code_info:
                ft_count = len(result.similar_code_info.similar_ft_code)
                dep_count = len(result.similar_code_info.similar_dependency_code)
                print(f"  ✅ SimilarCodeInfo: FT代码{ft_count}个, 依赖代码{dep_count}个")
            else:
                print(f"  ⚪ SimilarCodeInfo: 无")
                
        except Exception as e:
            print(f"  ❌ 解析失败: {e}")


if __name__ == "__main__":
    print("🚀 开始测试SimilarCodeInfo修复...")
    
    success = test_similar_code_fix()
    test_other_corpus_files()
    
    if success:
        print("\n🎉 SimilarCodeInfo修复验证完成！")
    else:
        print("\n❌ 修复验证失败")
        sys.exit(1)
