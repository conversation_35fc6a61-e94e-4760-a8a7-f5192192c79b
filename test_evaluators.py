#!/usr/bin/env python3
"""
测试评估器与新字段类型的兼容性
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from ft_corpus_evaluator.parsers.improved_markdown_parser import ImprovedMarkdownCorpusParser
from ft_corpus_evaluator.evaluators.completeness_dimension import RequiredFieldsMetric, ContentStructureMetric, TestStepsCompletenessMetric
from ft_corpus_evaluator.evaluators.correctness_dimension import LogicalConsistencyMetric, FormatCorrectnessMetric
from ft_corpus_evaluator.evaluators.difficulty_dimension import TechnicalComplexityMetric


def test_evaluators_with_real_data():
    """使用真实数据测试评估器"""
    print("🔍 测试评估器与新字段类型的兼容性")
    print("=" * 70)
    
    test_file = Path("corpus/RAN-5869391.md")
    if not test_file.exists():
        print("❌ 测试文件不存在")
        return
    
    # 解析真实数据
    parser = ImprovedMarkdownCorpusParser()
    corpus = parser.parse_file(test_file)
    
    print(f"解析的语料信息:")
    print(f"  tc_steps类型: {type(corpus.test_info.tc_steps)}")
    print(f"  tc_steps长度: {len(corpus.test_info.tc_steps)} 字符")
    print(f"  tc_expected_results类型: {type(corpus.test_info.tc_expected_results)}")
    print(f"  tc_expected_results长度: {len(corpus.test_info.tc_expected_results)} 字符")
    
    # 测试各个评估器
    evaluators = [
        ("RequiredFieldsMetric", RequiredFieldsMetric()),
        ("ContentStructureMetric", ContentStructureMetric()),
        ("TestStepsCompletenessMetric", TestStepsCompletenessMetric()),
        ("LogicalConsistencyMetric", LogicalConsistencyMetric()),
        ("FormatCorrectnessMetric", FormatCorrectnessMetric()),
        ("TechnicalComplexityMetric", TechnicalComplexityMetric())
    ]
    
    print(f"\n测试 {len(evaluators)} 个评估器:")
    print("-" * 50)
    
    all_passed = True
    
    for name, evaluator in evaluators:
        try:
            result = evaluator.evaluate(corpus)
            print(f"✅ {name}: 分数={result.score:.1f}, 级别={result.level.value}")
            
            # 验证结果的基本属性
            assert hasattr(result, 'score'), f"{name}: 结果缺少score属性"
            assert hasattr(result, 'level'), f"{name}: 结果缺少level属性"
            assert hasattr(result, 'message'), f"{name}: 结果缺少message属性"
            assert 0 <= result.score <= 100, f"{name}: 分数超出范围 [0, 100]"
            
        except Exception as e:
            print(f"❌ {name}: 评估失败 - {e}")
            import traceback
            traceback.print_exc()
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有评估器测试通过！")
    else:
        print("\n❌ 部分评估器测试失败")
    
    return all_passed


def test_evaluators_with_edge_cases():
    """测试边界情况"""
    print("\n🔍 测试边界情况")
    print("=" * 50)
    
    from ft_corpus_evaluator.models.corpus_model import FTCorpus, RdcInfo, TestInfo, TagIdentification
    
    # 创建边界测试用例
    edge_cases = [
        {
            "name": "空内容",
            "corpus": FTCorpus(
                file_path="empty.md",
                rdc_info=RdcInfo("", "", "", ""),
                test_info=TestInfo(test_title=""),
                tag_identification=TagIdentification()
            )
        },
        {
            "name": "最小内容",
            "corpus": FTCorpus(
                file_path="minimal.md",
                rdc_info=RdcInfo(rdc_id="RAN-123", repo_name="test", gerrit_link="http://test.com", date="2024-01-01"),
                test_info=TestInfo(
                    test_title="最小测试",
                    tc_steps="步骤1",
                    tc_expected_results="结果1"
                ),
                tag_identification=TagIdentification(
                    business_content_scene_tags=["测试"],
                    code_modify_scene_tags=["功能"]
                )
            )
        },
        {
            "name": "长内容",
            "corpus": FTCorpus(
                file_path="long.md",
                rdc_info=RdcInfo(rdc_id="RAN-123456", repo_name="long_test_repo", gerrit_link="https://gerrit.example.com/c/123456", date="2024-01-01"),
                test_info=TestInfo(
                    test_title="这是一个非常长的测试标题，用来测试评估器对长内容的处理能力" * 10,
                    tc_steps="这是一个非常详细的测试步骤描述，包含了大量的技术细节和操作说明" * 20,
                    tc_expected_results="这是一个非常详细的预期结果描述，包含了大量的验证点和检查项" * 20
                ),
                tag_identification=TagIdentification(
                    business_content_scene_tags=["复杂业务", "多场景", "综合测试"],
                    code_modify_scene_tags=["复杂修改", "多模块", "系统级"]
                )
            )
        }
    ]
    
    evaluators = [
        RequiredFieldsMetric(),
        ContentStructureMetric(),
        TestStepsCompletenessMetric()
    ]
    
    all_passed = True
    
    for case in edge_cases:
        print(f"\n测试用例: {case['name']}")
        print("-" * 30)
        
        for evaluator in evaluators:
            try:
                result = evaluator.evaluate(case['corpus'])
                print(f"  ✅ {evaluator.name}: 分数={result.score:.1f}")
                
                # 验证结果合理性
                assert 0 <= result.score <= 100, f"分数超出范围: {result.score}"
                
            except Exception as e:
                print(f"  ❌ {evaluator.name}: 失败 - {e}")
                all_passed = False
    
    if all_passed:
        print("\n✅ 边界情况测试通过！")
    else:
        print("\n❌ 边界情况测试失败")
    
    return all_passed


if __name__ == "__main__":
    print("🚀 开始测试评估器兼容性...")
    
    try:
        # 测试真实数据
        real_data_passed = test_evaluators_with_real_data()
        
        # 测试边界情况
        edge_cases_passed = test_evaluators_with_edge_cases()
        
        if real_data_passed and edge_cases_passed:
            print("\n🎉 所有评估器兼容性测试通过！")
            print("✅ 评估器已成功适配新的字段类型")
            print("✅ 字符串类型的tc_steps和tc_expected_results工作正常")
        else:
            print("\n❌ 部分测试失败")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
