#!/usr/bin/env python3
"""
测试TestInfo字段类型修改
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from ft_corpus_evaluator.parsers.corpus_parser import CorpusParser
from ft_corpus_evaluator.parsers.improved_markdown_parser import ImprovedMarkdownCorpusParser
from ft_corpus_evaluator.models.corpus_model import TestInfo, SimilarTestCase


def test_model_types():
    """测试模型类型定义"""
    print("🔍 测试模型类型定义")
    print("=" * 60)
    
    # 测试TestInfo
    test_info = TestInfo(
        test_title="测试标题",
        preconditions="这是预置条件",
        tc_steps="步骤1\n步骤2",
        tc_expected_results="结果1\n结果2",
        expected_results="预期结果",
        pass_criteria="通过准则"
    )
    
    print("TestInfo字段类型:")
    print(f"  preconditions: {type(test_info.preconditions)} = {repr(test_info.preconditions)}")
    print(f"  tc_steps: {type(test_info.tc_steps)} = {repr(test_info.tc_steps)}")
    print(f"  tc_expected_results: {type(test_info.tc_expected_results)} = {repr(test_info.tc_expected_results)}")
    print(f"  expected_results: {type(test_info.expected_results)} = {repr(test_info.expected_results)}")
    print(f"  pass_criteria: {type(test_info.pass_criteria)} = {repr(test_info.pass_criteria)}")
    
    # 验证类型
    assert isinstance(test_info.preconditions, str), "preconditions应该是str类型"
    assert isinstance(test_info.tc_steps, str), "tc_steps应该是str类型"
    assert isinstance(test_info.tc_expected_results, str), "tc_expected_results应该是str类型"
    assert isinstance(test_info.expected_results, str), "expected_results应该是str类型"
    assert isinstance(test_info.pass_criteria, str), "pass_criteria应该是str类型"
    
    print("✅ TestInfo类型验证通过！")
    
    # 测试SimilarTestCase
    similar_test = SimilarTestCase(
        test_title="相似测试",
        preconditions="相似预置条件",
        tc_steps="相似步骤1\n相似步骤2",
        tc_expected_results="相似结果1\n相似结果2"
    )
    
    print("\nSimilarTestCase字段类型:")
    print(f"  preconditions: {type(similar_test.preconditions)}")
    print(f"  tc_steps: {type(similar_test.tc_steps)}")
    print(f"  tc_expected_results: {type(similar_test.tc_expected_results)}")
    
    assert isinstance(similar_test.preconditions, str), "SimilarTestCase.preconditions应该是str类型"
    assert isinstance(similar_test.tc_steps, str), "SimilarTestCase.tc_steps应该是str类型"
    assert isinstance(similar_test.tc_expected_results, str), "SimilarTestCase.tc_expected_results应该是str类型"
    
    print("✅ SimilarTestCase类型验证通过！")


def test_parser_results():
    """测试解析器结果"""
    print("\n🔍 测试解析器结果")
    print("=" * 60)
    
    test_file = Path("corpus/RAN-5869391.md")
    if not test_file.exists():
        print("❌ 测试文件不存在")
        return
    
    # 测试原解析器
    print("\n📋 原解析器:")
    old_parser = CorpusParser()
    old_result = old_parser.parse_file(test_file)
    
    print(f"  tc_steps类型: {type(old_result.test_info.tc_steps)}")
    print(f"  tc_steps长度: {len(old_result.test_info.tc_steps)} 字符")
    print(f"  tc_steps前100字符: {repr(old_result.test_info.tc_steps[:100])}")
    
    print(f"  tc_expected_results类型: {type(old_result.test_info.tc_expected_results)}")
    print(f"  tc_expected_results长度: {len(old_result.test_info.tc_expected_results)} 字符")
    print(f"  tc_expected_results前100字符: {repr(old_result.test_info.tc_expected_results[:100])}")
    
    print(f"  expected_results类型: {type(old_result.test_info.expected_results)}")
    print(f"  expected_results长度: {len(old_result.test_info.expected_results)} 字符")
    print(f"  expected_results: {repr(old_result.test_info.expected_results)}")
    
    print(f"  pass_criteria类型: {type(old_result.test_info.pass_criteria)}")
    print(f"  pass_criteria长度: {len(old_result.test_info.pass_criteria)} 字符")
    print(f"  pass_criteria: {repr(old_result.test_info.pass_criteria)}")
    
    # 测试改进的解析器
    print("\n🚀 ImprovedMarkdownCorpusParser:")
    improved_parser = ImprovedMarkdownCorpusParser()
    improved_result = improved_parser.parse_file(test_file)
    
    print(f"  tc_steps类型: {type(improved_result.test_info.tc_steps)}")
    print(f"  tc_steps长度: {len(improved_result.test_info.tc_steps)} 字符")
    print(f"  tc_steps前100字符: {repr(improved_result.test_info.tc_steps[:100])}")
    
    print(f"  tc_expected_results类型: {type(improved_result.test_info.tc_expected_results)}")
    print(f"  tc_expected_results长度: {len(improved_result.test_info.tc_expected_results)} 字符")
    print(f"  tc_expected_results前100字符: {repr(improved_result.test_info.tc_expected_results[:100])}")
    
    print(f"  expected_results类型: {type(improved_result.test_info.expected_results)}")
    print(f"  expected_results长度: {len(improved_result.test_info.expected_results)} 字符")
    print(f"  expected_results: {repr(improved_result.test_info.expected_results)}")
    
    print(f"  pass_criteria类型: {type(improved_result.test_info.pass_criteria)}")
    print(f"  pass_criteria长度: {len(improved_result.test_info.pass_criteria)} 字符")
    print(f"  pass_criteria: {repr(improved_result.test_info.pass_criteria)}")
    
    # 验证类型
    assert isinstance(old_result.test_info.tc_steps, str), "原解析器tc_steps应该是str类型"
    assert isinstance(old_result.test_info.tc_expected_results, str), "原解析器tc_expected_results应该是str类型"
    assert isinstance(improved_result.test_info.tc_steps, str), "新解析器tc_steps应该是str类型"
    assert isinstance(improved_result.test_info.tc_expected_results, str), "新解析器tc_expected_results应该是str类型"
    
    print("\n✅ 解析器类型验证通过！")
    
    # 检查expected_results和pass_criteria是否正确分离
    print("\n🔍 检查expected_results和pass_criteria分离:")
    
    # 检查原解析器
    old_has_pass_in_expected = "通过准则" in old_result.test_info.expected_results
    old_has_expected_content = len(old_result.test_info.expected_results.strip()) > 0
    old_has_pass_content = len(old_result.test_info.pass_criteria.strip()) > 0
    
    print(f"  原解析器 - expected_results包含通过准则: {old_has_pass_in_expected}")
    print(f"  原解析器 - expected_results有内容: {old_has_expected_content}")
    print(f"  原解析器 - pass_criteria有内容: {old_has_pass_content}")
    
    # 检查新解析器
    new_has_pass_in_expected = "通过准则" in improved_result.test_info.expected_results
    new_has_expected_content = len(improved_result.test_info.expected_results.strip()) > 0
    new_has_pass_content = len(improved_result.test_info.pass_criteria.strip()) > 0
    
    print(f"  新解析器 - expected_results包含通过准则: {new_has_pass_in_expected}")
    print(f"  新解析器 - expected_results有内容: {new_has_expected_content}")
    print(f"  新解析器 - pass_criteria有内容: {new_has_pass_content}")
    
    if not old_has_pass_in_expected and old_has_pass_content:
        print("✅ 原解析器正确分离了expected_results和pass_criteria！")
    else:
        print("⚠️  原解析器可能没有正确分离expected_results和pass_criteria")
    
    if not new_has_pass_in_expected and new_has_pass_content:
        print("✅ 新解析器正确分离了expected_results和pass_criteria！")
    else:
        print("⚠️  新解析器可能没有正确分离expected_results和pass_criteria")


def test_backward_compatibility():
    """测试向后兼容性"""
    print("\n🔍 测试向后兼容性")
    print("=" * 60)
    
    # 测试默认值
    test_info_default = TestInfo(test_title="测试")
    print("默认值测试:")
    print(f"  preconditions: {repr(test_info_default.preconditions)}")
    print(f"  tc_steps: {repr(test_info_default.tc_steps)}")
    print(f"  tc_expected_results: {repr(test_info_default.tc_expected_results)}")
    print(f"  expected_results: {repr(test_info_default.expected_results)}")
    print(f"  pass_criteria: {repr(test_info_default.pass_criteria)}")
    
    assert test_info_default.preconditions == "", "默认preconditions应该是空字符串"
    assert test_info_default.tc_steps == "", "默认tc_steps应该是空字符串"
    assert test_info_default.tc_expected_results == "", "默认tc_expected_results应该是空字符串"
    assert test_info_default.expected_results == "", "默认expected_results应该是空字符串"
    assert test_info_default.pass_criteria == "", "默认pass_criteria应该是空字符串"
    
    print("✅ 向后兼容性验证通过！")


if __name__ == "__main__":
    print("🚀 开始测试TestInfo字段类型修改...")
    
    try:
        test_model_types()
        test_parser_results()
        test_backward_compatibility()
        
        print("\n🎉 所有测试通过！")
        print("✅ tc_steps和tc_expected_results已成功改为str类型")
        print("✅ expected_results和pass_criteria正确分离")
        print("✅ 所有解析器都兼容新的类型")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
