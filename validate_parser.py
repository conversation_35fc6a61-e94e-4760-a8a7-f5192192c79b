#!/usr/bin/env python3
"""
验证ImprovedMarkdownCorpusParser对RAN-5869391.md的解析效果
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from ft_corpus_evaluator.parsers.corpus_parser import CorpusParser
from ft_corpus_evaluator.parsers.improved_markdown_parser import ImprovedMarkdownCorpusParser


def validate_ran_5869391():
    """验证RAN-5869391.md的解析"""
    
    test_file = Path("corpus/RAN-5869391.md")
    if not test_file.exists():
        print(f"测试文件 {test_file} 不存在")
        return
    
    print(f"验证文件: {test_file}")
    print("=" * 80)
    
    # 使用原解析器
    print("\n📋 原解析器结果:")
    print("-" * 50)
    old_parser = CorpusParser()
    old_result = old_parser.parse_file(test_file)
    
    # 使用改进的解析器
    print("\n🚀 ImprovedMarkdownCorpusParser结果:")
    print("-" * 50)
    improved_parser = ImprovedMarkdownCorpusParser()
    improved_result = improved_parser.parse_file(test_file)
    
    # 详细验证各个部分
    validate_rdc_info(old_result, improved_result)
    validate_test_info(old_result, improved_result)
    validate_tag_identification(old_result, improved_result)
    validate_code_snippets(old_result, improved_result)
    validate_dependency_code(old_result, improved_result)
    validate_similar_code_info(old_result, improved_result)
    validate_other_sections(old_result, improved_result)
    
    # 总体评估
    print("\n📊 总体评估:")
    print("-" * 50)
    evaluate_overall_performance(old_result, improved_result)


def validate_rdc_info(old_result, new_result):
    """验证RdcInfo解析"""
    print("\n🔍 RdcInfo验证:")
    
    fields = {
        'rdc_id': 'RDC ID',
        'repo_name': '仓库名称',
        'gerrit_link': 'Gerrit链接',
        'date': '日期',
        'final_test': '最终测试',
        'compile_script': '编译脚本',
        'compile_command_json_path': '编译命令路径'
    }
    
    all_match = True
    for field, desc in fields.items():
        old_val = getattr(old_result.rdc_info, field, "") or ""
        new_val = getattr(new_result.rdc_info, field, "") or ""
        
        if old_val == new_val:
            status = "✅"
        else:
            status = "❌"
            all_match = False
        
        print(f"  {status} {desc}: {field}")
        if old_val != new_val:
            print(f"    原: '{old_val}'")
            print(f"    新: '{new_val}'")
    
    print(f"  📈 RdcInfo整体匹配: {'✅ 完全匹配' if all_match else '❌ 部分匹配'}")


def validate_test_info(old_result, new_result):
    """验证TestInfo解析"""
    print("\n🔍 TestInfo验证:")
    
    # 测试标题
    old_title_len = len(old_result.test_info.test_title)
    new_title_len = len(new_result.test_info.test_title)
    title_status = "✅" if old_title_len == new_title_len else "❌"
    print(f"  {title_status} 测试标题长度: 原{old_title_len} vs 新{new_title_len}")
    
    # 预置条件
    old_precond_len = len(old_result.test_info.preconditions)
    new_precond_len = len(new_result.test_info.preconditions)
    precond_status = "✅" if old_precond_len == new_precond_len else "❌"
    print(f"  {precond_status} 预置条件长度: 原{old_precond_len} vs 新{new_precond_len}")
    
    # TC步骤
    old_steps = len(old_result.test_info.tc_steps)
    new_steps = len(new_result.test_info.tc_steps)
    steps_status = "✅" if old_steps == new_steps else "❌"
    print(f"  {steps_status} TC步骤数量: 原{old_steps} vs 新{new_steps}")
    
    # TC预期结果
    old_results = len(old_result.test_info.tc_expected_results)
    new_results = len(new_result.test_info.tc_expected_results)
    results_status = "✅" if old_results == new_results else "❌"
    print(f"  {results_status} TC预期结果数量: 原{old_results} vs 新{new_results}")
    
    # 显示实际内容样例
    if new_result.test_info.test_title:
        print(f"  📝 新解析器提取的标题前100字符: '{new_result.test_info.test_title[:100]}...'")
    
    if new_result.test_info.preconditions:
        print(f"  📝 新解析器提取的预置条件前100字符: '{new_result.test_info.preconditions[:100]}...'")


def validate_tag_identification(old_result, new_result):
    """验证TagIdentification解析"""
    print("\n🔍 TagIdentification验证:")
    
    old_business = len(old_result.tag_identification.business_content_scene_tags)
    new_business = len(new_result.tag_identification.business_content_scene_tags)
    business_status = "✅" if old_business == new_business else "❌"
    print(f"  {business_status} 业务标签数量: 原{old_business} vs 新{new_business}")
    
    old_code = len(old_result.tag_identification.code_modify_scene_tags)
    new_code = len(new_result.tag_identification.code_modify_scene_tags)
    code_status = "✅" if old_code == new_code else "❌"
    print(f"  {code_status} 代码标签数量: 原{old_code} vs 新{new_code}")
    
    # 显示实际标签内容
    if new_result.tag_identification.business_content_scene_tags:
        print(f"  📝 业务标签: {new_result.tag_identification.business_content_scene_tags}")
    if new_result.tag_identification.code_modify_scene_tags:
        print(f"  📝 代码标签: {new_result.tag_identification.code_modify_scene_tags}")


def validate_code_snippets(old_result, new_result):
    """验证CodeSnippets解析"""
    print("\n🔍 CodeSnippets验证:")
    
    old_count = len(old_result.code_snippets)
    new_count = len(new_result.code_snippets)
    status = "✅" if old_count == new_count else "❌"
    print(f"  {status} 代码片段数量: 原{old_count} vs 新{new_count}")
    
    # 显示前几个代码片段的信息
    print("  📝 前3个代码片段:")
    for i, snippet in enumerate(new_result.code_snippets[:3]):
        print(f"    [{i+1}] {snippet.language}: {len(snippet.content)}字符")
        if snippet.file_path:
            print(f"        路径: {snippet.file_path}")


def validate_dependency_code(old_result, new_result):
    """验证DependencyCode解析"""
    print("\n🔍 DependencyCode验证:")
    
    old_count = len(old_result.dependency_code)
    new_count = len(new_result.dependency_code)
    status = "✅" if old_count == new_count else "❌"
    print(f"  {status} 依赖代码数量: 原{old_count} vs 新{new_count}")
    
    if new_result.dependency_code:
        print("  📝 依赖代码示例:")
        for i, dep in enumerate(new_result.dependency_code[:2]):
            print(f"    [{i+1}] {dep.language}: {len(dep.content)}字符")
            if dep.file_path:
                print(f"        路径: {dep.file_path}")


def validate_similar_code_info(old_result, new_result):
    """验证SimilarCodeInfo解析"""
    print("\n🔍 SimilarCodeInfo验证:")
    
    old_exists = old_result.similar_code_info is not None
    new_exists = new_result.similar_code_info is not None
    status = "✅" if old_exists == new_exists else "❌"
    print(f"  {status} SimilarCodeInfo存在: 原{old_exists} vs 新{new_exists}")
    
    if new_result.similar_code_info:
        old_ft = len(old_result.similar_code_info.similar_ft_code)
        new_ft = len(new_result.similar_code_info.similar_ft_code)
        ft_status = "✅" if old_ft == new_ft else "❌"
        print(f"  {ft_status} 相似FT代码数量: 原{old_ft} vs 新{new_ft}")
        
        old_dep = len(old_result.similar_code_info.similar_dependency_code)
        new_dep = len(new_result.similar_code_info.similar_dependency_code)
        dep_status = "✅" if old_dep == new_dep else "❌"
        print(f"  {dep_status} 相似依赖代码数量: 原{old_dep} vs 新{new_dep}")
        
        test_case_status = "✅" if bool(old_result.similar_code_info.similar_test_case) == bool(new_result.similar_code_info.similar_test_case) else "❌"
        print(f"  {test_case_status} 相似测试用例存在: 原{bool(old_result.similar_code_info.similar_test_case)} vs 新{bool(new_result.similar_code_info.similar_test_case)}")


def validate_other_sections(old_result, new_result):
    """验证其他section解析"""
    print("\n🔍 其他Sections验证:")
    
    sections = [
        ('generation_requirements', '代码生成要求'),
        ('implementation_steps', '实现步骤'),
        ('output_format', '输出格式'),
        ('output_requirements', '输出要求'),
        ('target_test_case_description', '目标测试用例描述')
    ]
    
    for field, desc in sections:
        old_exists = getattr(old_result, field, None) is not None
        new_exists = getattr(new_result, field, None) is not None
        status = "✅" if old_exists == new_exists else "❌"
        print(f"  {status} {desc}: 原{old_exists} vs 新{new_exists}")


def evaluate_overall_performance(old_result, new_result):
    """总体性能评估"""
    
    # 计算匹配度
    total_checks = 0
    passed_checks = 0
    
    # RdcInfo检查
    rdc_fields = ['rdc_id', 'repo_name', 'gerrit_link', 'date']
    for field in rdc_fields:
        total_checks += 1
        old_val = getattr(old_result.rdc_info, field, "")
        new_val = getattr(new_result.rdc_info, field, "")
        if old_val == new_val:
            passed_checks += 1
    
    # 数量检查
    checks = [
        (len(old_result.code_snippets), len(new_result.code_snippets)),
        (len(old_result.dependency_code), len(new_result.dependency_code)),
        (len(old_result.tag_identification.business_content_scene_tags), 
         len(new_result.tag_identification.business_content_scene_tags)),
    ]
    
    for old_val, new_val in checks:
        total_checks += 1
        if old_val == new_val:
            passed_checks += 1
    
    accuracy = (passed_checks / total_checks) * 100 if total_checks > 0 else 0
    
    print(f"📈 解析准确率: {accuracy:.1f}% ({passed_checks}/{total_checks})")
    
    if accuracy >= 90:
        print("🎉 解析质量: 优秀")
    elif accuracy >= 80:
        print("👍 解析质量: 良好")
    elif accuracy >= 70:
        print("⚠️  解析质量: 一般")
    else:
        print("❌ 解析质量: 需要改进")
    
    print("\n💡 建议:")
    if accuracy < 100:
        print("  - 可以进一步优化正则表达式匹配")
        print("  - 检查markdown结构解析逻辑")
        print("  - 验证特殊字符和格式处理")
    else:
        print("  - 解析器工作良好，可以投入使用")
        print("  - 建议进行更多样本测试")


if __name__ == "__main__":
    print("🔍 开始验证ImprovedMarkdownCorpusParser...")
    validate_ran_5869391()
    print("\n✅ 验证完成!")
